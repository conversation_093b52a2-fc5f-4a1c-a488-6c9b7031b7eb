-- 片段脚本管理数据库表创建脚本（简化版）
-- 最大兼容性版本，适用于所有MySQL/MariaDB版本

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建片段脚本表
CREATE TABLE IF NOT EXISTS `snippet_scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `code` longtext NOT NULL,
  `enabled` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `category` varchar(100) DEFAULT 'default',
  `load_priority` int(11) DEFAULT 50,
  `dependencies` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_category` (`category`),
  KEY `idx_load_priority` (`load_priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建脚本片段关联表
CREATE TABLE IF NOT EXISTS `script_snippets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `script_id` int(11) NOT NULL,
  `snippet_id` int(11) NOT NULL,
  `enabled` tinyint(1) DEFAULT 1,
  `load_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_script_snippet` (`script_id`, `snippet_id`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_snippet_id` (`snippet_id`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入示例片段脚本数据
-- -- INSERT IGNORE INTO `snippet_scripts` (`title`, `description`, `code`, `enabled`, `sort_order`, `category`, `load_priority`) VALUES (已注释掉不完整的INSERT语句) (已清理：测试数据)
--  (已清理：测试数据)
-- SET FOREIGN_KEY_CHECKS = 1; (已清理：测试数据)
