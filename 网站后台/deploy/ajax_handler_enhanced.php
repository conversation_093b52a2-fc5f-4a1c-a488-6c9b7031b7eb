<?php
// 增强版AJAX处理器 - 已修复自动配置问题
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 输出缓冲区清理
if (ob_get_level()) {
    ob_end_clean();
}

// 包含必要文件
require_once '../includes/db.php';
require_once '../includes/functions.php';

// JSON响应函数
function sendJsonResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查用户登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    sendJsonResponse(false, '用户未登录');
}

// 获取操作类型
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'send_trust_device_code':
            handleSendTrustDeviceCode();
            break;
            
        case 'send_smtp_config_code':
            handleSendSmtpConfigCode();
            break;
            
        case 'trust_device':
            handleTrustDevice();
            break;
            
        case 'update_smtp':
            handleUpdateSmtp();
            break;
            
        case 'test_email':
            handleTestEmail();
            break;
            
        default:
            sendJsonResponse(false, '未知操作类型');
    }
} catch (Exception $e) {
    error_log("AJAX处理器错误: " . $e->getMessage());
    sendJsonResponse(false, '系统错误，请重试');
}

// 发送信任设备验证码
function handleSendTrustDeviceCode() {
    global $pdo;
    
    try {
        // 检查用户邮箱
        $stmt = $pdo->prepare("SELECT email FROM admin_users WHERE id = ?");
        $stmt->execute([$_SESSION['admin_user_id']]);
        $user_email = $stmt->fetchColumn();
        
        if (!$user_email) {
            sendJsonResponse(false, '请先在系统设置中配置管理员邮箱地址');
        }
        
        // 检查SMTP配置
        $settings = getSystemSettings();
        
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            sendJsonResponse(false, '请先在系统设置中配置SMTP邮箱服务');
        }
        
        // 生成验证码
        $verification_code = generateVerificationCode();
        
        // 保存验证码
        ensureVerificationTableExists();
        $pdo->exec("DELETE FROM verification_codes WHERE expires_at < NOW() OR is_used = 1");
        
        $stmt = $pdo->prepare("INSERT INTO verification_codes (user_id, code, type, expires_at) VALUES (?, ?, 'trust_device', DATE_ADD(NOW(), INTERVAL 20 SECOND))");
        $stmt->execute([$_SESSION['admin_user_id'], $verification_code]);
        
        // 发送验证码邮件
        if (sendTrustDeviceVerificationEmail($user_email, $verification_code, $settings)) {
            sendJsonResponse(true, '验证码已发送到您的邮箱（有效期20秒）');
        } else {
            sendJsonResponse(false, '验证码发送失败，请检查邮箱配置');
        }
        
    } catch (Exception $e) {
        error_log("发送信任设备验证码错误: " . $e->getMessage());
        sendJsonResponse(false, '系统错误：' . $e->getMessage());
    }
}

// 发送SMTP配置验证码
function handleSendSmtpConfigCode() {
    global $pdo;
    
    try {
        // 检查用户邮箱
        $stmt = $pdo->prepare("SELECT email FROM admin_users WHERE id = ?");
        $stmt->execute([$_SESSION['admin_user_id']]);
        $user_email = $stmt->fetchColumn();
        
        if (!$user_email) {
            sendJsonResponse(false, '请先在系统设置中配置管理员邮箱地址');
        }
        
        // 检查SMTP配置
        $settings = getSystemSettings();
        
        if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
            sendJsonResponse(false, '请先在系统设置中配置SMTP邮箱服务');
        }
        
        // 生成验证码
        $verification_code = generateVerificationCode();
        
        // 保存验证码
        ensureVerificationTableExists();
        $pdo->exec("DELETE FROM verification_codes WHERE expires_at < NOW() OR is_used = 1");
        
        $stmt = $pdo->prepare("INSERT INTO verification_codes (user_id, code, type, expires_at) VALUES (?, ?, 'smtp_config', DATE_ADD(NOW(), INTERVAL 20 SECOND))");
        $stmt->execute([$_SESSION['admin_user_id'], $verification_code]);
        
        // 发送验证码邮件
        if (sendSmtpConfigVerificationEmail($user_email, $verification_code, $settings)) {
            sendJsonResponse(true, '验证码已发送到您的邮箱（有效期20秒）');
        } else {
            sendJsonResponse(false, '验证码发送失败，请检查邮箱配置');
        }
        
    } catch (Exception $e) {
        error_log("发送SMTP配置验证码错误: " . $e->getMessage());
        sendJsonResponse(false, '系统错误：' . $e->getMessage());
    }
}

// 信任设备处理
function handleTrustDevice() {
    global $pdo;
    
    $email_code = $_POST['email_code'] ?? '';
    $device_info = json_decode($_POST['device_info'] ?? '{}', true);
    
    if (empty($email_code)) {
        sendJsonResponse(false, '验证码不能为空');
    }
    
    if (!$device_info || !isset($device_info['fingerprint'])) {
        sendJsonResponse(false, '设备信息无效');
    }
    
    try {
        // 验证邮箱验证码
        $stmt = $pdo->prepare("SELECT * FROM verification_codes WHERE 
            user_id = ? AND 
            code = ? AND 
            type = 'trust_device' AND 
            is_used = 0 AND 
            expires_at > NOW()
        ");
        $stmt->execute([$_SESSION['admin_user_id'], $email_code]);
        $code_record = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$code_record) {
            sendJsonResponse(false, '验证码无效或已过期');
        }
        
        // 标记验证码为已使用
        $stmt = $pdo->prepare("UPDATE verification_codes SET is_used = 1 WHERE id = ?");
        $stmt->execute([$code_record['id']]);
        
        // 确保信任设备表存在
        ensureTrustedDevicesTableExists();
        
        // 添加信任设备
        $stmt = $pdo->prepare("
            INSERT INTO trusted_devices (user_id, device_name, device_fingerprint, user_agent, ip_address, created_at, is_active) 
            VALUES (?, ?, ?, ?, ?, NOW(), 1) 
            ON DUPLICATE KEY UPDATE 
                device_name = VALUES(device_name),
                user_agent = VALUES(user_agent),
                ip_address = VALUES(ip_address),
                is_active = 1,
                updated_at = NOW()
        ");
        
        $result = $stmt->execute([
            $_SESSION['admin_user_id'],
            $device_info['name'] ?? '未知设备',
            $device_info['fingerprint'],
            $device_info['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? ''
        ]);
        
        if ($result) {
            sendJsonResponse(true, '设备已成功添加到信任列表');
        } else {
            sendJsonResponse(false, '添加失败，请重试');
        }
    } catch (Exception $e) {
        error_log("信任设备处理错误: " . $e->getMessage());
        sendJsonResponse(false, '操作失败：' . $e->getMessage());
    }
}

// SMTP配置更新
function handleUpdateSmtp() {
    global $pdo;
    
    $host = $_POST['smtp_host'] ?? '';
    $port = $_POST['smtp_port'] ?? '';
    $username = $_POST['smtp_username'] ?? '';
    $password = $_POST['smtp_password'] ?? '';
    $verification_code = $_POST['verification_code'] ?? '';
    
    // 注意：不再自动启用这些功能，由用户手动选择
    $email_verification_enabled = $_POST['email_verification_enabled'] ?? '0';
    $login_notification_enabled = $_POST['login_notification_enabled'] ?? '0';
    
    try {
        // 确保表存在
        ensureSystemSettingsTableExists();
        
        // 检查是否有现有配置
        $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'smtp_username'");
        $stmt->execute();
        $existing_smtp = $stmt->fetchColumn();
        $has_existing_config = !empty($existing_smtp);
        
        // 如果有现有配置且提供了验证码，验证验证码
        if ($has_existing_config && !empty($verification_code)) {
            $stmt = $pdo->prepare("SELECT * FROM verification_codes WHERE 
                user_id = ? AND 
                code = ? AND 
                type = 'smtp_config' AND 
                is_used = 0 AND 
                expires_at > NOW()
            ");
            $stmt->execute([$_SESSION['admin_user_id'], $verification_code]);
            $code_record = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($code_record) {
                $stmt = $pdo->prepare("UPDATE verification_codes SET is_used = 1 WHERE id = ?");
                $stmt->execute([$code_record['id']]);
            } elseif ($has_existing_config) {
                sendJsonResponse(false, '修改现有配置需要邮箱验证码');
            }
        }
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 更新系统设置
        $system_settings = [
            'smtp_host' => $host,
            'smtp_port' => $port,
            'smtp_username' => $username,
            'smtp_password' => $password,
            'email_verification_enabled' => $email_verification_enabled,
            'login_notification_enabled' => $login_notification_enabled
        ];
        
        foreach ($system_settings as $key => $value) {
            $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");
            $stmt->execute([$key, $value, $value]);
        }
        
        $pdo->commit();
        
        sendJsonResponse(true, '邮箱配置保存成功', [
            'has_existing_config' => !empty($host),
            'settings_updated' => [
                'email_verification_enabled' => $email_verification_enabled,
                'login_notification_enabled' => $login_notification_enabled
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("SMTP配置更新失败: " . $e->getMessage());
        sendJsonResponse(false, '保存失败：' . $e->getMessage());
    }
}

// 测试邮件发送
function handleTestEmail() {
    $test_email = $_POST['test_email'] ?? '';
    $smtp_host = $_POST['smtp_host'] ?? '';
    $smtp_port = $_POST['smtp_port'] ?? '';
    $smtp_username = $_POST['smtp_username'] ?? '';
    $smtp_password = $_POST['smtp_password'] ?? '';
    
    if (empty($test_email) || empty($smtp_host) || empty($smtp_port) || empty($smtp_username) || empty($smtp_password)) {
        sendJsonResponse(false, '请填写完整的邮箱配置信息');
    }
    
    try {
        if (sendTestEmailWithConfig($test_email, $smtp_host, $smtp_port, $smtp_username, $smtp_password)) {
            sendJsonResponse(true, '测试邮件发送成功！请检查邮箱（包括垃圾邮件文件夹）');
        } else {
            sendJsonResponse(false, '测试邮件发送失败，请检查配置信息');
        }
    } catch (Exception $e) {
        error_log("测试邮件发送错误: " . $e->getMessage());
        sendJsonResponse(false, '发送失败：' . $e->getMessage());
    }
}

// 辅助函数
function ensureVerificationTableExists() {
    global $pdo;
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS verification_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        code VARCHAR(6) NOT NULL,
        type VARCHAR(50) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        is_used TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_code (user_id, code),
        INDEX idx_expires (expires_at),
        INDEX idx_type (type)
    )");
}

function ensureTrustedDevicesTableExists() {
    global $pdo;
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS trusted_devices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        device_name VARCHAR(255) NOT NULL,
        device_fingerprint VARCHAR(255) NOT NULL,
        user_agent TEXT,
        ip_address VARCHAR(45),
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY user_device (user_id, device_fingerprint),
        INDEX idx_user_id (user_id),
        INDEX idx_fingerprint (device_fingerprint)
    )");
}

function ensureSystemSettingsTableExists() {
    global $pdo;
    
    $pdo->exec("CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        description VARCHAR(255) DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
}

function sendTrustDeviceVerificationEmail($email, $code, $settings) {
    return sendVerificationEmailWithSettings($email, $code, 'trust_device', $settings);
}

function sendSmtpConfigVerificationEmail($email, $code, $settings) {
    return sendVerificationEmailWithSettings($email, $code, 'smtp_config', $settings);
}

function sendVerificationEmailWithSettings($email, $code, $type, $settings) {
    // 检查PHPMailer
    $phpmailer_paths = [
        __DIR__ . '/../includes/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/../includes/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('PHPMailer库未找到');
        return false;
    }
    
    // 根据版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
    } else {
        error_log('PHPMailer类未找到');
        return false;
    }
    
    try {
        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        $port = intval($settings['smtp_port'] ?? 465);
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
        } else {
            $mail->SMTPSecure = $encryption_starttls;
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        // 发件人和收件人
        $from_email = $settings['from_email'] ?: $settings['smtp_username'];
        $from_name = $settings['from_name'] ?: '小梅花AI客服系统';
        
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($email);
        
        // 邮件内容
        $mail->isHTML(true);
        
        if ($type === 'trust_device') {
            $mail->Subject = '【小梅花AI客服系统】设备信任验证码';
            $icon = '🛡️';
            $color = '#4CAF50';
            $title = '设备信任验证';
        } else {
            $mail->Subject = '【小梅花AI客服系统】邮箱配置验证码';
            $icon = '⚙️';
            $color = '#3498db';
            $title = '邮箱配置验证';
        }
        
        $mail->Body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 15px;'>
            <div style='background: white; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <div style='background: $color; width: 60px; height: 60px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;'>
                        <span style='color: white; font-size: 24px;'>$icon</span>
                    </div>
                    <h1 style='color: #333; margin: 0; font-size: 24px;'>$title</h1>
                </div>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>
                    <p style='color: #666; margin: 0 0 15px 0; font-size: 14px;'>您的验证码是：</p>
                    <div style='font-size: 32px; font-weight: bold; color: $color; letter-spacing: 5px; font-family: monospace;'>$code</div>
                    <p style='color: #999; margin: 15px 0 0 0; font-size: 12px;'>验证码有效期：20秒</p>
                </div>
                
                <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p style='color: #856404; margin: 0; font-size: 14px;'>
                        <strong>⚠️ 安全提醒：</strong><br>
                        • 请勿将此验证码告诉任何人<br>
                        • 验证码仅用于当前操作验证<br>
                        • 如非本人操作，请立即检查账号安全
                    </p>
                </div>
                
                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        此邮件由小梅花AI客服系统自动发送，请勿回复<br>
                        发送时间：" . date('Y-m-d H:i:s') . "
                    </p>
                </div>
            </div>
        </div>";
        
        $result = $mail->send();
        error_log("验证码邮件发送" . ($result ? "成功" : "失败"));
        return $result;
        
    } catch (Exception $e) {
        error_log("验证码邮件发送失败: " . $e->getMessage());
        return false;
    }
}
?>