-- 小梅花AI客服系统 - 代码片段表创建脚本 (纯净版)
-- 版本: v3.0.0 - 无测试数据版
-- 日期: 2025-06-30
-- 功能: 仅创建表结构，不包含任何测试数据

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建代码片段表
CREATE TABLE IF NOT EXISTS `snippet_scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '功能名称',
  `description` text DEFAULT NULL COMMENT '功能描述',
  `code` longtext NOT NULL COMMENT '片段代码',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '启用状态',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `category` varchar(100) DEFAULT 'default' COMMENT '分类',
  `load_priority` int(11) DEFAULT 50 COMMENT '加载优先级',
  `dependencies` text DEFAULT NULL COMMENT '依赖关系',
  `created_by` int(11) DEFAULT 1 COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_title` (`title`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代码片段表';

-- 创建脚本片段关联表
CREATE TABLE IF NOT EXISTS `script_snippets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `script_id` int(11) NOT NULL COMMENT '主脚本ID',
  `snippet_id` int(11) NOT NULL COMMENT '片段脚本ID',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `load_order` int(11) DEFAULT 0 COMMENT '加载顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_script_snippet` (`script_id`, `snippet_id`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_snippet_id` (`snippet_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='脚本片段关联表';

SET FOREIGN_KEY_CHECKS = 1;

SELECT '代码片段表创建完成！无测试数据。' as message;