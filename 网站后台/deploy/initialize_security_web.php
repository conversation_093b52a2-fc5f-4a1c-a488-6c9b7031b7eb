<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 安全系统初始化 - 小美花客服系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 30px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .step {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #ddd;
        }
        
        .step.active {
            border-left-color: #2196F3;
            background: #e3f2fd;
        }
        
        .step.success {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .step.error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        
        .step-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-content {
            color: #666;
            font-size: 14px;
        }
        
        .result-box {
            background: #f0f8ff;
            border: 1px solid #b0d4f1;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            width: 100%;
            margin: 20px 0;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .status-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 5px;
        }
        
        .status-label {
            color: #666;
            font-size: 14px;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            display: none;
        }
        
        .log-container.show {
            display: block;
        }
        
        .icon {
            font-size: 20px;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 安全系统初始化</h1>
            <p>小美花客服系统 - 企业级安全架构升级</p>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar"></div>
        </div>
        
        <div id="steps">
            <div class="step" id="step1">
                <div class="step-title">
                    <span class="icon">📋</span>
                    <span>步骤 1/6: 生成API密钥</span>
                </div>
                <div class="step-content">生成主要的API访问密钥，用于前后端安全通信</div>
            </div>
            
            <div class="step" id="step2">
                <div class="step-title">
                    <span class="icon">🔐</span>
                    <span>步骤 2/6: 生成JWT密钥</span>
                </div>
                <div class="step-content">生成JWT认证密钥，用于用户身份验证</div>
            </div>
            
            <div class="step" id="step3">
                <div class="step-title">
                    <span class="icon">📄</span>
                    <span>步骤 3/6: 生成安全配置文件</span>
                </div>
                <div class="step-content">创建安全的API配置文件，替换硬编码配置</div>
            </div>
            
            <div class="step" id="step4">
                <div class="step-title">
                    <span class="icon">🔍</span>
                    <span>步骤 4/6: 扫描敏感信息</span>
                </div>
                <div class="step-content">扫描所有前后端文件，识别敏感信息</div>
            </div>
            
            <div class="step" id="step5">
                <div class="step-title">
                    <span class="icon">🛠️</span>
                    <span>步骤 5/6: 自动修复敏感信息</span>
                </div>
                <div class="step-content">自动修复发现的敏感信息，替换为安全调用</div>
            </div>
            
            <div class="step" id="step6">
                <div class="step-title">
                    <span class="icon">🔗</span>
                    <span>步骤 6/6: 更新文件引用</span>
                </div>
                <div class="step-content">更新所有相关文件的引用，确保系统正常运行</div>
            </div>
        </div>
        
        <button class="btn" id="startBtn" onclick="startInitialization()">
            🚀 开始安全初始化
        </button>
        
        <div id="result" class="result-box" style="display: none;"></div>
        
        <div class="log-container" id="logContainer">
            <div id="logContent"></div>
        </div>
        
        <div class="status-grid" id="statusGrid" style="display: none;">
            <div class="status-card">
                <div class="status-number" id="apiKeyCount">0</div>
                <div class="status-label">API密钥</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="jwtTokenCount">0</div>
                <div class="status-label">JWT令牌</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="sensitiveCount">0</div>
                <div class="status-label">敏感信息</div>
            </div>
            <div class="status-card">
                <div class="status-number" id="fixedCount">0</div>
                <div class="status-label">修复成功</div>
            </div>
        </div>
        
        <div id="completionMessage" style="display: none;">
            <div style="background: #e8f5e8; border: 1px solid #4CAF50; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center;">
                <h3 style="color: #4CAF50; margin-bottom: 10px;">🎉 安全系统初始化完成！</h3>
                <p style="color: #333; margin-bottom: 15px;">您的系统已成功升级为企业级安全架构</p>
                <a href="xuxuemei/index.php" style="background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                    访问后台API配置页面
                </a>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let totalSteps = 6;
        
        function updateProgress(step) {
            const progress = (step / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        function setStepStatus(stepNum, status) {
            const step = document.getElementById(`step${stepNum}`);
            step.className = `step ${status}`;
            
            if (status === 'active') {
                currentStep = stepNum;
                updateProgress(stepNum - 1);
            } else if (status === 'success') {
                updateProgress(stepNum);
            }
        }
        
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logContent = document.getElementById('logContent');
            
            logContainer.classList.add('show');
            
            const timestamp = new Date().toLocaleTimeString();
            const logLine = document.createElement('div');
            logLine.innerHTML = `[${timestamp}] ${message}`;
            logLine.className = type;
            
            logContent.appendChild(logLine);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateStatus(apiKeys, jwtTokens, sensitive, fixed) {
            document.getElementById('statusGrid').style.display = 'grid';
            document.getElementById('apiKeyCount').textContent = apiKeys;
            document.getElementById('jwtTokenCount').textContent = jwtTokens;
            document.getElementById('sensitiveCount').textContent = sensitive;
            document.getElementById('fixedCount').textContent = fixed;
        }
        
        async function executeStep(stepNum, stepName, action) {
            setStepStatus(stepNum, 'active');
            addLog(`🔄 开始执行: ${stepName}`, 'info');
            
            try {
                const response = await fetch('api/initialize_security.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: action,
                        step: stepNum
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    setStepStatus(stepNum, 'success');
                    addLog(`✅ 完成: ${stepName}`, 'success');
                    if (result.message) {
                        addLog(result.message, 'info');
                    }
                    return result;
                } else {
                    setStepStatus(stepNum, 'error');
                    addLog(`❌ 失败: ${stepName} - ${result.error}`, 'error');
                    throw new Error(result.error);
                }
            } catch (error) {
                setStepStatus(stepNum, 'error');
                addLog(`❌ 错误: ${error.message}`, 'error');
                throw error;
            }
        }
        
        async function startInitialization() {
            const startBtn = document.getElementById('startBtn');
            startBtn.disabled = true;
            startBtn.textContent = '⏳ 初始化中...';
            
            addLog('🔒 开始安全系统初始化...', 'info');
            
            try {
                // 步骤 1: 生成API密钥
                const step1 = await executeStep(1, '生成API密钥', 'generate_api_key');
                
                // 步骤 2: 生成JWT密钥  
                const step2 = await executeStep(2, '生成JWT密钥', 'generate_jwt_secret');
                
                // 步骤 3: 生成安全配置文件
                const step3 = await executeStep(3, '生成安全配置文件', 'generate_config_file');
                
                // 步骤 4: 扫描敏感信息
                const step4 = await executeStep(4, '扫描敏感信息', 'scan_sensitive');
                
                // 步骤 5: 自动修复敏感信息
                const step5 = await executeStep(5, '自动修复敏感信息', 'fix_sensitive');
                
                // 步骤 6: 更新文件引用
                const step6 = await executeStep(6, '更新文件引用', 'update_references');
                
                // 更新状态统计
                updateStatus(
                    step1.data?.api_key_count || 1,
                    step2.data?.jwt_token_count || 1, 
                    step4.data?.sensitive_count || 0,
                    step5.data?.fixed_count || 0
                );
                
                // 显示完成消息
                document.getElementById('completionMessage').style.display = 'block';
                addLog('🎉 安全系统初始化完成！', 'success');
                addLog('📊 请访问后台API配置页面查看详细信息', 'info');
                
                startBtn.textContent = '✅ 初始化完成';
                
            } catch (error) {
                addLog(`💥 初始化失败: ${error.message}`, 'error');
                startBtn.disabled = false;
                startBtn.textContent = '🔄 重新初始化';
            }
        }
        
        // 页面加载时检查系统状态
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('api/auto_refresh_cron.php');
                const status = await response.json();
                
                if (status.active_tokens > 0) {
                    addLog('ℹ️ 检测到现有令牌，可以重新初始化以更新系统', 'warning');
                }
            } catch (error) {
                // 忽略错误，可能是首次运行
            }
        });
    </script>
</body>
</html>

<?php
// PHP后端处理
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    $step = $input['step'] ?? 0;
    
    // 定义安全访问常量
    define('API_SECURITY_INIT', true);
    
    try {
        switch ($action) {
            case 'generate_api_key':
                require_once 'api/token_manager.php';
                $tokenManager = new TokenManager();
                $result = $tokenManager->generateApiKey('Web Generated API Key', true, 86400);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'API密钥生成成功: ' . substr($result['key_value'], 0, 16) . '...',
                    'data' => ['api_key_count' => 1]
                ]);
                break;
                
            case 'generate_jwt_secret':
                require_once 'api/token_manager.php';
                $tokenManager = new TokenManager();
                $result = $tokenManager->generateJwtSecret(true, 604800);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'JWT密钥生成成功',
                    'data' => ['jwt_token_count' => 1]
                ]);
                break;
                
            case 'generate_config_file':
                require_once 'api/sensitive_fixer.php';
                $sensitiveFixer = new SensitiveFixer();
                $configFile = $sensitiveFixer->generateSecureConfigFile();
                
                echo json_encode([
                    'success' => true,
                    'message' => '安全配置文件生成成功: ' . basename($configFile)
                ]);
                break;
                
            case 'scan_sensitive':
                require_once 'api/token_manager.php';
                $tokenManager = new TokenManager();
                $scanResults = $tokenManager->scanSensitiveInfo();
                
                echo json_encode([
                    'success' => true,
                    'message' => '扫描完成，发现 ' . count($scanResults) . ' 个敏感信息',
                    'data' => ['sensitive_count' => count($scanResults)]
                ]);
                break;
                
            case 'fix_sensitive':
                require_once 'api/sensitive_fixer.php';
                $sensitiveFixer = new SensitiveFixer();
                $fixResults = $sensitiveFixer->batchFixAll();
                
                echo json_encode([
                    'success' => true,
                    'message' => '修复完成，成功修复 ' . count($fixResults['fixed']) . ' 个敏感信息',
                    'data' => ['fixed_count' => count($fixResults['fixed'])]
                ]);
                break;
                
            case 'update_references':
                require_once 'api/sensitive_fixer.php';
                $sensitiveFixer = new SensitiveFixer();
                $sensitiveFixer->updateFileReferences();
                
                echo json_encode([
                    'success' => true,
                    'message' => '文件引用更新完成'
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'error' => '未知操作: ' . $action
                ]);
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    
    exit;
} 