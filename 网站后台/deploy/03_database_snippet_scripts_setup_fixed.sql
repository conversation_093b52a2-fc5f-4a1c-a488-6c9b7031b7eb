-- 片段脚本管理数据库表创建脚本（修复版）
-- 用于实现模块化脚本管理和快速加载功能
-- 兼容MySQL 5.7+和MariaDB

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建片段脚本表
CREATE TABLE IF NOT EXISTS `snippet_scripts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '功能名称',
  `description` text DEFAULT NULL COMMENT '功能描述',
  `code` longtext NOT NULL COMMENT '片段代码',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '启用状态',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `category` varchar(100) DEFAULT 'default' COMMENT '分类',
  `load_priority` int(11) DEFAULT 50 COMMENT '加载优先级',
  `dependencies` text DEFAULT NULL COMMENT '依赖关系(JSON格式)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_category` (`category`),
  KEY `idx_load_priority` (`load_priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='片段脚本表';

-- 安全地为scripts表添加字段
-- 检查status字段是否存在，不存在则添加
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'scripts' 
   AND table_schema = DATABASE()
   AND column_name = 'status') > 0,
  'SELECT "status字段已存在" as result',
  'ALTER TABLE `scripts` ADD COLUMN `status` enum(\'active\',\'inactive\') NOT NULL DEFAULT \'active\' COMMENT \'脚本状态\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查load_mode字段是否存在，不存在则添加
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'scripts' 
   AND table_schema = DATABASE()
   AND column_name = 'load_mode') > 0,
  'SELECT "load_mode字段已存在" as result',
  'ALTER TABLE `scripts` ADD COLUMN `load_mode` enum(\'instant\',\'lazy\',\'manual\') NOT NULL DEFAULT \'instant\' COMMENT \'加载模式\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查cache_enabled字段是否存在，不存在则添加
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'scripts' 
   AND table_schema = DATABASE()
   AND column_name = 'cache_enabled') > 0,
  'SELECT "cache_enabled字段已存在" as result',
  'ALTER TABLE `scripts` ADD COLUMN `cache_enabled` tinyint(1) DEFAULT 1 COMMENT \'是否启用缓存\''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建脚本片段关联表（无外键约束版本，避免兼容性问题）
CREATE TABLE IF NOT EXISTS `script_snippets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `script_id` int(11) NOT NULL COMMENT '主脚本ID',
  `snippet_id` int(11) NOT NULL COMMENT '片段脚本ID',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `load_order` int(11) DEFAULT 0 COMMENT '加载顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_script_snippet` (`script_id`, `snippet_id`),
  KEY `idx_script_id` (`script_id`),
  KEY `idx_snippet_id` (`snippet_id`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='脚本片段关联表';

-- 插入示例片段脚本数据
-- -- INSERT IGNORE INTO `snippet_scripts` (`title`, `description`, `code`, `enabled`, `sort_order`, `category`, `load_priority`) VALUES (已注释掉不完整的INSERT语句) (已清理：测试数据)
--  (已清理：测试数据)
-- -- 安全地创建索引 (已清理：测试数据)
-- -- 检查并创建scripts表的status索引 (已清理：测试数据)
-- SET @sql = (SELECT IF( (已清理：测试数据)
--   (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS  (已清理：测试数据)
--    WHERE table_name = 'scripts'  (已清理：测试数据)
--    AND table_schema = DATABASE() (已清理：测试数据)
--    AND index_name = 'idx_scripts_status') > 0, (已清理：测试数据)
--   'SELECT "status索引已存在" as result', (已清理：测试数据)
--   'CREATE INDEX `idx_scripts_status` ON `scripts` (`status`)' (已清理：测试数据)
-- )); (已清理：测试数据)
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并创建scripts表的load_mode索引
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
   WHERE table_name = 'scripts' 
   AND table_schema = DATABASE()
   AND index_name = 'idx_scripts_load_mode') > 0,
  'SELECT "load_mode索引已存在" as result',
  'CREATE INDEX `idx_scripts_load_mode` ON `scripts` (`load_mode`)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET FOREIGN_KEY_CHECKS = 1;

-- 完成提示
SELECT '片段脚本管理系统数据库初始化完成！' as message; 
