<?php
require_once 'includes/db.php';

// 定义要创建的表结构
$tables = [
    // API安全配置表
    "system_config" => "
        CREATE TABLE IF NOT EXISTS `system_config` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `config_key` VARCHAR(64) NOT NULL UNIQUE,
            `config_value` TEXT,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ",
    
    // 管理员操作日志表
    "admin_action_log" => "
        CREATE TABLE IF NOT EXISTS `admin_action_log` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `admin_id` INT NOT NULL,
            `action` VARCHAR(64) NOT NULL,
            `description` VARCHAR(255),
            `ip_address` VARCHAR(45),
            `user_agent` VARCHAR(255),
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ",
    
    // API访问日志表
    "api_access_log" => "
        CREATE TABLE IF NOT EXISTS `api_access_log` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `key_id` INT NOT NULL,
            `client_id` VARCHAR(255),
            `ip_address` VARCHAR(45),
            `request_method` VARCHAR(10),
            `request_path` VARCHAR(255),
            `status_code` INT,
            `response_time` FLOAT,
            `request_timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `risk_score` FLOAT DEFAULT 0,
            `geo_location` VARCHAR(100),
            INDEX `idx_key_id` (`key_id`),
            INDEX `idx_client_id` (`client_id`),
            INDEX `idx_ip_address` (`ip_address`),
            INDEX `idx_timestamp` (`request_timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ",
    
    // API令牌管理表
    "api_tokens" => "
        CREATE TABLE IF NOT EXISTS `api_tokens` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `key_id` INT NOT NULL,
            `token_type` ENUM('session', 'refresh') NOT NULL,
            `token_value` VARCHAR(255) NOT NULL,
            `client_id` VARCHAR(255) NOT NULL,
            `issued_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `expires_at` TIMESTAMP NOT NULL,
            `last_used_at` TIMESTAMP NULL DEFAULT NULL,
            `is_revoked` TINYINT(1) DEFAULT 0,
            `device_info` TEXT,
            UNIQUE KEY `unique_token` (`token_value`),
            INDEX `idx_key_client` (`key_id`, `client_id`),
            INDEX `idx_expires` (`expires_at`),
            INDEX `idx_token_type` (`token_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ",
    
    // 设备指纹表
    "device_fingerprints" => "
        CREATE TABLE IF NOT EXISTS `device_fingerprints` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `fingerprint` VARCHAR(255) NOT NULL,
            `key_id` INT NOT NULL,
            `device_name` VARCHAR(255),
            `first_seen` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `last_seen` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `trust_level` TINYINT DEFAULT 0,
            `user_agent` TEXT,
            `os_info` VARCHAR(100),
            `browser_info` VARCHAR(100),
            `screen_info` VARCHAR(50),
            `language` VARCHAR(20),
            `timezone` VARCHAR(50),
            UNIQUE KEY `unique_fingerprint_key` (`fingerprint`, `key_id`),
            INDEX `idx_fingerprint` (`fingerprint`),
            INDEX `idx_key_id` (`key_id`),
            INDEX `idx_trust_level` (`trust_level`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ",
    
    // 风险评分记录表
    "risk_score_records" => "
        CREATE TABLE IF NOT EXISTS `risk_score_records` (
            `id` INT AUTO_INCREMENT PRIMARY KEY,
            `key_id` INT NOT NULL,
            `client_id` VARCHAR(255),
            `score_change` FLOAT NOT NULL,
            `current_score` FLOAT NOT NULL,
            `reason` VARCHAR(255) NOT NULL,
            `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `ip_address` VARCHAR(45),
            INDEX `idx_key_id` (`key_id`),
            INDEX `idx_timestamp` (`timestamp`),
            INDEX `idx_client_id` (`client_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    "
];

// 创建表
$created = 0;
$errors = [];

echo "<h1>API安全配置数据库结构初始化</h1>";
echo "<pre>";

foreach ($tables as $table_name => $query) {
    try {
        $result = $pdo->exec($query);
        echo "✅ 表 `$table_name` 创建成功或已存在\n";
        $created++;
    } catch (PDOException $e) {
        echo "❌ 表 `$table_name` 创建失败: " . $e->getMessage() . "\n";
        $errors[] = $e->getMessage();
    }
}

echo "\n总计: $created 个表创建成功, " . count($errors) . " 个表创建失败";

if (!empty($errors)) {
    echo "\n\n错误详情:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
}

echo "</pre>";

// 检查是否需要创建初始配置
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM system_config WHERE config_key = 'api_security_config'");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // 创建默认API安全配置
        $default_config = [
            'layer1' => [
                'forceHttps' => true,
                'jwtExpiry' => 2,
                'refreshTokenExpiry' => 7,
                'signatureTimeWindow' => 5,
                'jwtSecret' => generateRandomString(32),
                'rateLimit' => 60
            ],
            'layer2' => [
                'enableRiskScoring' => true,
                'highFreqFailureThreshold' => 10,
                'riskScoreIncrement' => 5,
                'coolingThreshold' => 25,
                'lockThreshold' => 50,
                'riskDecayRate' => 5
            ],
            'layer3' => [
                'enableCodeObfuscation' => true,
                'enableStringEncryption' => true,
                'enableIntegrityCheck' => true,
                'enableCloudLogic' => true
            ],
            'layer4' => [
                'useEnvVariables' => true,
                'enableWAF' => true,
                'enableSecurityLogs' => true,
                'alertMethod' => 'email'
            ]
        ];
        
        $config_json = json_encode($default_config, JSON_UNESCAPED_UNICODE);
        
        $stmt = $pdo->prepare("INSERT INTO system_config (config_key, config_value) VALUES ('api_security_config', ?)");
        $stmt->execute([$config_json]);
        
        echo "<p>✅ 默认API安全配置已创建</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ 创建默认配置失败: " . $e->getMessage() . "</p>";
} 