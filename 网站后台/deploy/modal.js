/**
 * 自定义弹窗组件
 * 功能：替换原生confirm()函数，实现居中显示的美观弹窗
 * 版本：v1.0.0
 */

class CustomModal {
    constructor() {
        this.currentModal = null;
        this.init();
    }

    init() {
        // 创建弹窗容器
        this.createModalContainer();
        // 覆盖原生confirm函数
        this.overrideNativeConfirm();
    }

    createModalContainer() {
        // 创建弹窗HTML结构
        const modalHTML = `
            <div id="customModal" class="modal-overlay">
                <div class="modal-dialog">
                    <div class="modal-header">
                        <div class="modal-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3 class="modal-title">确认操作</h3>
                    </div>
                    <div class="modal-body">
                        <p class="modal-message">您确定要执行此操作吗？</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="modal-btn modal-btn-cancel" data-action="cancel">
                            <i class="fas fa-times"></i> 取消
                        </button>
                        <button type="button" class="modal-btn modal-btn-confirm" data-action="confirm">
                            <i class="fas fa-check"></i> 确定
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // 绑定事件
        this.bindEvents();
    }

    bindEvents() {
        const modal = document.getElementById('customModal');
        const overlay = modal;
        const cancelBtn = modal.querySelector('[data-action="cancel"]');
        const confirmBtn = modal.querySelector('[data-action="confirm"]');

        // 点击遮罩层关闭弹窗
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.hide(false);
            }
        });

        // 取消按钮
        cancelBtn.addEventListener('click', () => {
            this.hide(false);
        });

        // 确认按钮
        confirmBtn.addEventListener('click', () => {
            this.hide(true);
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentModal) {
                this.hide(false);
            }
        });
    }

    show(options = {}) {
        return new Promise((resolve) => {
            const modal = document.getElementById('customModal');
            const icon = modal.querySelector('.modal-icon');
            const title = modal.querySelector('.modal-title');
            const message = modal.querySelector('.modal-message');
            const confirmBtn = modal.querySelector('[data-action="confirm"]');

            // 设置弹窗内容
            title.textContent = options.title || '确认操作';
            message.textContent = options.message || '您确定要执行此操作吗？';

            // 设置图标和样式
            const iconClass = this.getIconClass(options.type);
            const iconElement = icon.querySelector('i');
            
            // 清除旧的图标类
            iconElement.className = '';
            iconElement.className = iconClass;
            
            // 设置图标容器样式
            icon.className = `modal-icon ${options.type || 'warning'}`;

            // 设置确认按钮样式
            confirmBtn.className = `modal-btn ${this.getConfirmButtonClass(options.type)}`;
            confirmBtn.innerHTML = `<i class="${this.getConfirmIconClass(options.type)}"></i> ${options.confirmText || '确定'}`;

            // 显示弹窗
            modal.classList.add('active');
            this.currentModal = resolve;

            // 焦点管理
            setTimeout(() => {
                confirmBtn.focus();
            }, 300);
        });
    }

    hide(result) {
        const modal = document.getElementById('customModal');
        modal.classList.remove('active');
        
        if (this.currentModal) {
            this.currentModal(result);
            this.currentModal = null;
        }
    }

    getIconClass(type) {
        const iconMap = {
            'warning': 'fas fa-exclamation-triangle',
            'danger': 'fas fa-trash-alt',
            'success': 'fas fa-check-circle',
            'info': 'fas fa-info-circle',
            'question': 'fas fa-question-circle'
        };
        return iconMap[type] || iconMap['warning'];
    }

    getConfirmButtonClass(type) {
        const buttonMap = {
            'warning': 'modal-btn-confirm',
            'danger': 'modal-btn-danger',
            'success': 'modal-btn-success',
            'info': 'modal-btn-confirm'
        };
        return buttonMap[type] || buttonMap['warning'];
    }

    getConfirmIconClass(type) {
        const iconMap = {
            'warning': 'fas fa-check',
            'danger': 'fas fa-trash-alt',
            'success': 'fas fa-check',
            'info': 'fas fa-check'
        };
        return iconMap[type] || iconMap['warning'];
    }

    // 覆盖原生confirm函数
    overrideNativeConfirm() {
        // 保存原生confirm函数的引用
        window.originalConfirm = window.confirm;
        
        // 替换confirm函数
        window.confirm = (message) => {
            // 分析消息内容，确定弹窗类型
            const type = this.detectMessageType(message);
            
            return this.show({
                title: this.getTitle(type, message),
                message: message,
                type: type,
                confirmText: this.getConfirmText(type),
            });
        };
    }

    detectMessageType(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('删除') || lowerMessage.includes('delete')) {
            return 'danger';
        } else if (lowerMessage.includes('保存') || lowerMessage.includes('save')) {
            return 'success';
        } else if (lowerMessage.includes('退出') || lowerMessage.includes('logout')) {
            return 'warning';
        } else {
            return 'warning';
        }
    }

    getTitle(type, message) {
        const titleMap = {
            'danger': '删除确认',
            'success': '保存确认',
            'warning': '操作确认',
            'info': '信息提示'
        };

        // 根据消息内容自定义标题
        if (message.includes('删除')) {
            return '删除确认';
        } else if (message.includes('保存')) {
            return '保存确认';
        } else if (message.includes('退出')) {
            return '退出确认';
        } else if (message.includes('清空')) {
            return '清空确认';
        }

        return titleMap[type] || '操作确认';
    }

    getConfirmText(type) {
        const textMap = {
            'danger': '删除',
            'success': '保存',
            'warning': '确定',
            'info': '确定'
        };
        return textMap[type] || '确定';
    }
}

// 自定义弹窗函数
window.showModal = function(options) {
    if (!window.customModalInstance) {
        window.customModalInstance = new CustomModal();
    }
    return window.customModalInstance.show(options);
};

// 简化的弹窗函数
window.showConfirm = function(message, type = 'warning') {
    return window.showModal({
        message: message,
        type: type
    });
};

window.showDeleteConfirm = function(message = '确定要删除这个项目吗？') {
    return window.showModal({
        title: '删除确认',
        message: message,
        type: 'danger',
        confirmText: '删除'
    });
};

window.showSaveConfirm = function(message = '确定要保存修改吗？') {
    return window.showModal({
        title: '保存确认',
        message: message,
        type: 'success',
        confirmText: '保存'
    });
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化自定义弹窗
    if (!window.customModalInstance) {
        window.customModalInstance = new CustomModal();
    }

    // 为现有的confirm调用添加异步支持
    const originalAddEventListener = Element.prototype.addEventListener;
    Element.prototype.addEventListener = function(type, listener, options) {
        if (type === 'click' && typeof listener === 'function') {
            const originalListener = listener;
            const newListener = function(e) {
                // 检查是否是包含confirm的onclick处理器
                const onclickAttr = this.getAttribute('onclick');
                if (onclickAttr && onclickAttr.includes('confirm(')) {
                    e.preventDefault();
                    
                    // 提取confirm中的消息
                    const confirmMatch = onclickAttr.match(/confirm\(['"`]([^'"`]*)['"`]\)/);
                    if (confirmMatch) {
                        const message = confirmMatch[1];
                        window.confirm(message).then(result => {
                            if (result) {
                                // 如果确认，执行原始操作
                                if (this.tagName === 'BUTTON' && this.type === 'submit') {
                                    this.form.submit();
                                } else if (this.href) {
                                    window.location.href = this.href;
                                } else {
                                    // 移除onclick属性中的confirm部分，然后执行
                                    const cleanedOnclick = onclickAttr.replace(/return\s+confirm\([^)]+\)\s*;?\s*/, '');
                                    if (cleanedOnclick.trim()) {
                                        eval(cleanedOnclick);
                                    }
                                }
                            }
                        });
                        return false;
                    }
                }
                return originalListener.call(this, e);
            };
            return originalAddEventListener.call(this, type, newListener, options);
        }
        return originalAddEventListener.call(this, type, listener, options);
    };

    // 处理现有的onclick属性
    document.querySelectorAll('[onclick*="confirm("]').forEach(element => {
        const onclickAttr = element.getAttribute('onclick');
        const confirmMatch = onclickAttr.match(/confirm\(['"`]([^'"`]*)['"`]\)/);
        
        if (confirmMatch) {
            const message = confirmMatch[1];
            
            // 移除原有的onclick属性
            element.removeAttribute('onclick');
            
            // 添加新的事件监听器
            element.addEventListener('click', function(e) {
                e.preventDefault();
                
                window.confirm(message).then(result => {
                    if (result) {
                        // 如果确认，执行原始操作
                        if (this.tagName === 'BUTTON' && this.type === 'submit') {
                            this.form.submit();
                        } else if (this.href) {
                            window.location.href = this.href;
                        } else {
                            // 执行清理后的onclick代码
                            const cleanedOnclick = onclickAttr.replace(/return\s+confirm\([^)]+\)\s*;?\s*/, '');
                            if (cleanedOnclick.trim()) {
                                eval(cleanedOnclick);
                            }
                        }
                    }
                });
            });
        }
    });
});

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CustomModal;
} 