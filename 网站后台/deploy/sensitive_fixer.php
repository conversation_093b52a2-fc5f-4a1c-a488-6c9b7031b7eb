<?php
/**
 * 敏感信息自动修复系统
 * 版本: v2.0.0
 * 功能: 自动识别和修复前端代码中的敏感信息
 */

require_once 'config.php';
require_once 'token_manager.php';

class SensitiveFixer {
    private $tokenManager;
    private $config;
    
    public function __construct() {
        $this->tokenManager = new TokenManager();
        $this->config = SecureApiConfig::getInstance();
    }
    
    /**
     * 自动修复指定文件中的敏感信息
     */
    public function autoFixSensitive($file, $line, $type) {
        $filePath = $this->getFullPath($file);
        
        if (!file_exists($filePath)) {
            throw new Exception("文件不存在: {$file}");
        }
        
        if (!is_writable($filePath)) {
            throw new Exception("文件不可写: {$file}");
        }
        
        // 备份原文件
        $this->backupFile($filePath);
        
        // 读取文件内容
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);
        
        // 修复指定行的敏感信息
        $fixedLine = $this->fixSensitiveLine($lines[$line - 1], $type);
        $lines[$line - 1] = $fixedLine;
        
        // 写回文件
        $newContent = implode("\n", $lines);
        file_put_contents($filePath, $newContent);
        
        // 记录修复日志
        $this->logFix($file, $line, $type, $fixedLine);
        
        return true;
    }
    
    /**
     * 批量修复所有敏感信息
     */
    public function batchFixAll() {
        $scanResults = $this->tokenManager->scanSensitiveInfo();
        $fixed = [];
        $failed = [];
        
        foreach ($scanResults as $result) {
            try {
                $this->autoFixSensitive($result['file'], $result['line'], $result['type']);
                $fixed[] = $result;
            } catch (Exception $e) {
                $failed[] = [
                    'result' => $result,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return [
            'fixed' => $fixed,
            'failed' => $failed,
            'total' => count($scanResults)
        ];
    }
    
    /**
     * 修复单行敏感信息
     */
    private function fixSensitiveLine($line, $type) {
        switch ($type) {
            case 'DB_HOST':
                return $this->fixDbHost($line);
            case 'DB_USER':
                return $this->fixDbUser($line);
            case 'DB_PASS':
                return $this->fixDbPass($line);
            case 'API_URL':
                return $this->fixApiUrl($line);
            case 'HARDCODED_PASSWORD':
                return $this->fixHardcodedPassword($line);
            default:
                return $line;
        }
    }
    
    /**
     * 修复数据库主机配置
     */
    private function fixDbHost($line) {
        $pattern = '/(\$db_host\s*=\s*)[\'"][^\'"]*[\'"];/';
        $replacement = '$1getApiConfig("database.host");';
        return preg_replace($pattern, $replacement, $line);
    }
    
    /**
     * 修复数据库用户配置
     */
    private function fixDbUser($line) {
        $pattern = '/(\$db_user\s*=\s*)[\'"][^\'"]*[\'"];/';
        $replacement = '$1getApiConfig("database.user");';
        return preg_replace($pattern, $replacement, $line);
    }
    
    /**
     * 修复数据库密码配置
     */
    private function fixDbPass($line) {
        $pattern = '/(\$db_pass\s*=\s*)[\'"][^\'"]*[\'"];/';
        $replacement = '$1getApiConfig("database.pass");';
        return preg_replace($pattern, $replacement, $line);
    }
    
    /**
     * 修复API URL
     */
    private function fixApiUrl($line) {
        // 生成新的API密钥
        $apiKeyResult = $this->tokenManager->generateApiKey('Auto-Fix API Key', true, 86400);
        $apiKey = $apiKeyResult['key_value'];
        
        $pattern = '/(const\s+API_URL\s*=\s*)[\'"]http:\/\/[^\'"]*[\'"];/';
        $replacement = '$1`${getApiEndpoint()}/gateway.php?key=' . $apiKey . '`;';
        
        return preg_replace($pattern, $replacement, $line);
    }
    
    /**
     * 修复硬编码密码
     */
    private function fixHardcodedPassword($line) {
        $pattern = '/(password[\'"]?\s*[:=]\s*)[\'"][^\'"]+[\'"];/';
        $replacement = '$1getSecurePassword();';
        return preg_replace($pattern, $replacement, $line);
    }
    
    /**
     * 获取文件完整路径
     */
    private function getFullPath($relativePath) {
        $basePath = dirname(__DIR__);
        return $basePath . '/' . ltrim($relativePath, '/');
    }
    
    /**
     * 备份文件
     */
    private function backupFile($filePath) {
        $backupDir = dirname(__DIR__) . '/backups/sensitive_fixes';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $backupFile = $backupDir . '/' . basename($filePath) . '.' . date('Y-m-d_H-i-s') . '.bak';
        copy($filePath, $backupFile);
    }
    
    /**
     * 记录修复日志
     */
    private function logFix($file, $line, $type, $fixedContent) {
        $pdo = $this->config->getDatabaseConnection();
        
        $stmt = $pdo->prepare("
            INSERT INTO sensitive_fix_log (file_path, line_number, fix_type, original_content, fixed_content, created_at) 
            VALUES (?, ?, ?, ?, ?, datetime('now'))
        ");
        
        $stmt->execute([$file, $line, $type, '', $fixedContent]);
    }
    
    /**
     * 初始化修复日志表
     */
    public function initializeLogTable() {
        $pdo = $this->config->getDatabaseConnection();
        
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS sensitive_fix_log (
                id INT AUTOINCREMENT PRIMARY KEY,
                file_path VARCHAR(500) NOT NULL,
                line_number INT NOT NULL,
                fix_type VARCHAR(50) NOT NULL,
                original_content TEXT,
                fixed_content TEXT,
                created_at DATETIME DEFAULT datetime('now'),
                INDEX idx_file_type (file_path, fix_type),
                INDEX idx_created (created_at)
            )
        ");
    }
    
    /**
     * 生成安全配置文件
     */
    public function generateSecureConfigFile() {
        $configContent = $this->generateConfigFileContent();
        
        $configDir = dirname(__DIR__) . '/config';
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        $configFile = $configDir . '/secure_api.js';
        file_put_contents($configFile, $configContent);
        
        return $configFile;
    }
    
    /**
     * 生成配置文件内容
     */
    private function generateConfigFileContent() {
        // 生成新的API密钥
        $apiKeyResult = $this->tokenManager->generateApiKey('Secure Config API Key', true, 86400);
        $apiKey = $apiKeyResult['key_value'];
        
        return <<<JS
/**
 * 安全API配置文件
 * 自动生成于: {date('Y-m-d H:i:s')}
 * 版本: v2.0.0
 */

// API配置
const API_CONFIG = {
    baseUrl: 'https://xiaomeihuakefu.cn/api',
    gateway: 'gateway.php',
    version: 'v2.0',
    key: '{$apiKey}'
};

// 获取API端点
function getApiEndpoint() {
    return `\${API_CONFIG.baseUrl}/\${API_CONFIG.gateway}`;
}

// 获取API密钥
function getApiKey() {
    return API_CONFIG.key;
}

// 安全的API调用函数
async function secureFetch(endpoint, data = {}) {
    const token = localStorage.getItem('accessToken');
    
    // 检查令牌是否过期
    if (isTokenExpired(token)) {
        await refreshToken();
    }
    
    const response = await fetch(`\${getApiEndpoint()}/\${endpoint}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer \${getApiKey()}`,
            'X-API-Version': API_CONFIG.version
        },
        body: JSON.stringify(data)
    });
    
    if (!response.ok) {
        throw new Error(`API请求失败: \${response.status}`);
    }
    
    return response.json();
}

// 令牌过期检查
function isTokenExpired(token) {
    if (!token) return true;
    
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return Date.now() >= payload.exp * 1000;
    } catch (e) {
        return true;
    }
}

// 刷新令牌
async function refreshToken() {
    const response = await fetch(`\${getApiEndpoint()}/auth/refresh`, {
        method: 'POST',
        credentials: 'include'
    });
    
    if (response.ok) {
        const data = await response.json();
        localStorage.setItem('accessToken', data.accessToken);
        return data.accessToken;
    } else {
        // 重定向到登录页面
        window.location.href = '/login';
    }
}

// 数据库配置获取函数（替换硬编码）
function getApiConfig(key) {
    // 通过安全API获取配置
    return secureFetch('config/get', { key: key });
}

// 安全密码获取函数
function getSecurePassword() {
    // 从安全存储获取密码
    return secureFetch('auth/get-password');
}

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        API_CONFIG,
        getApiEndpoint,
        getApiKey,
        secureFetch,
        getApiConfig,
        getSecurePassword
    };
}
JS;
    }
    
    /**
     * 更新所有相关文件的引用
     */
    public function updateFileReferences() {
        $targetFiles = [
            'tampermonkey.js',
            'userscript_templates/complete_template.user.js',
            'api/secure_client.js'
        ];
        
        foreach ($targetFiles as $file) {
            $this->addSecureConfigImport($file);
        }
    }
    
    /**
     * 在文件中添加安全配置导入
     */
    private function addSecureConfigImport($file) {
        $filePath = $this->getFullPath($file);
        
        if (!file_exists($filePath)) {
            return;
        }
        
        $content = file_get_contents($filePath);
        
        // 检查是否已经导入
        if (strpos($content, 'secure_api.js') !== false) {
            return;
        }
        
        // 在文件开头添加导入
        $importLine = "// @require      https://xiaomeihuakefu.cn/config/secure_api.js\n";
        
        if (strpos($content, '// @require') !== false) {
            // 在最后一个@require后添加
            $content = preg_replace('/^(.*\/\/ @require.*\n)/m', '$1' . $importLine, $content, 1);
        } else {
            // 在文件开头添加
            $content = $importLine . $content;
        }
        
        file_put_contents($filePath, $content);
    }
}

// 初始化修复器
$sensitiveFixer = new SensitiveFixer();
$sensitiveFixer->initializeLogTable(); 