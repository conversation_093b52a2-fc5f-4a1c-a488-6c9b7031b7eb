<?php
/**
 * JWT认证服务
 * 版本: v2.0.0
 * 功能: 处理用户认证、令牌生成和验证
 */

require_once 'config.php';

class JWTAuthService {
    private $config;
    private $pdo;
    
    public function __construct() {
        $this->config = SecureApiConfig::getInstance();
        $this->pdo = $this->config->getDatabaseConnection();
    }
    
    /**
     * 用户登录认证
     */
    public function authenticate($username, $password, $clientInfo = []) {
        try {
            // 检查登录尝试次数
            if ($this->isAccountLocked($username)) {
                return [
                    'success' => false,
                    'error' => 'ACCOUNT_LOCKED',
                    'message' => '账户已被锁定，请稍后再试'
                ];
            }
            
            // 验证用户凭证
            $user = $this->validateCredentials($username, $password);
            if (!$user) {
                $this->recordFailedLogin($username, $clientInfo);
                return [
                    'success' => false,
                    'error' => 'INVALID_CREDENTIALS',
                    'message' => '用户名或密码错误'
                ];
            }
            
            // 生成令牌对
            $tokens = $this->generateTokenPair($user);
            
            // 记录成功登录
            $this->recordSuccessfulLogin($user['id'], $clientInfo);
            
            // 清除失败登录记录
            $this->clearFailedLogins($username);
            
            return [
                'success' => true,
                'access_token' => $tokens['access_token'],
                'refresh_token' => $tokens['refresh_token'],
                'expires_in' => $this->config->get('jwt', 'access_token_expire'),
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email']
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Authentication error: " . $e->getMessage());
            return [
                'success' => false,
                'error' => 'INTERNAL_ERROR',
                'message' => '认证服务暂时不可用'
            ];
        }
    }
    
    /**
     * 验证用户凭证
     */
    private function validateCredentials($username, $password) {
        $stmt = $this->pdo->prepare("
            SELECT id, username, password, email, is_active 
            FROM admin_users 
            WHERE username = ? AND is_active = 1
        ");
        $stmt->execute([$username]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return false;
        }
        
        // 验证密码（支持多种哈希方式）
        if ($this->verifyPassword($password, $user['password'])) {
            return $user;
        }
        
        return false;
    }
    
    /**
     * 验证密码
     */
    private function verifyPassword($password, $hashedPassword) {
        // 支持新的bcrypt哈希
        if (password_verify($password, $hashedPassword)) {
            return true;
        }
        
        // 兼容旧的MD5哈希
        if (md5($password) === $hashedPassword) {
            return true;
        }
        
        // 支持加盐哈希
        $salt = $this->config->get('security', 'password_salt');
        if (hash_hmac('sha256', $password, $salt) === $hashedPassword) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 生成令牌对
     */
    private function generateTokenPair($user) {
        $jwtConfig = $this->config->get('jwt');
        $currentTime = time();
        
        // Access Token payload
        $accessPayload = [
            'iss' => $jwtConfig['issuer'],
            'aud' => $jwtConfig['audience'],
            'iat' => $currentTime,
            'exp' => $currentTime + $jwtConfig['access_token_expire'],
            'sub' => $user['id'],
            'username' => $user['username'],
            'type' => 'access'
        ];
        
        // Refresh Token payload
        $refreshPayload = [
            'iss' => $jwtConfig['issuer'],
            'aud' => $jwtConfig['audience'],
            'iat' => $currentTime,
            'exp' => $currentTime + $jwtConfig['refresh_token_expire'],
            'sub' => $user['id'],
            'type' => 'refresh',
            'jti' => bin2hex(random_bytes(16)) // 唯一标识符
        ];
        
        $accessToken = $this->createJWT($accessPayload);
        $refreshToken = $this->createJWT($refreshPayload);
        
        // 存储refresh token
        $this->storeRefreshToken($user['id'], $refreshPayload['jti'], $refreshPayload['exp']);
        
        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken
        ];
    }
    
    /**
     * 创建JWT令牌
     */
    private function createJWT($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        
        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->config->get('jwt', 'secret'), true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }
    
    /**
     * 验证JWT令牌
     */
    public function validateToken($token) {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return false;
            }
            
            list($base64Header, $base64Payload, $base64Signature) = $parts;
            
            // 验证签名
            $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->config->get('jwt', 'secret'), true);
            $expectedSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
            
            if (!hash_equals($base64Signature, $expectedSignature)) {
                return false;
            }
            
            // 解码payload
            $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64Payload)), true);
            
            // 检查过期时间
            if ($payload['exp'] < time()) {
                return false;
            }
            
            // 检查令牌类型
            if ($payload['type'] === 'refresh') {
                // 验证refresh token是否在数据库中
                if (!$this->isRefreshTokenValid($payload['sub'], $payload['jti'])) {
                    return false;
                }
            }
            
            return $payload;
            
        } catch (Exception $e) {
            error_log("Token validation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 刷新访问令牌
     */
    public function refreshToken($refreshToken) {
        $payload = $this->validateToken($refreshToken);
        
        if (!$payload || $payload['type'] !== 'refresh') {
            return [
                'success' => false,
                'error' => 'INVALID_REFRESH_TOKEN',
                'message' => '无效的刷新令牌'
            ];
        }
        
        // 获取用户信息
        $stmt = $this->pdo->prepare("SELECT * FROM admin_users WHERE id = ? AND is_active = 1");
        $stmt->execute([$payload['sub']]);
        $user = $stmt->fetch();
        
        if (!$user) {
            return [
                'success' => false,
                'error' => 'USER_NOT_FOUND',
                'message' => '用户不存在或已禁用'
            ];
        }
        
        // 生成新的访问令牌
        $jwtConfig = $this->config->get('jwt');
        $currentTime = time();
        
        $accessPayload = [
            'iss' => $jwtConfig['issuer'],
            'aud' => $jwtConfig['audience'],
            'iat' => $currentTime,
            'exp' => $currentTime + $jwtConfig['access_token_expire'],
            'sub' => $user['id'],
            'username' => $user['username'],
            'type' => 'access'
        ];
        
        $newAccessToken = $this->createJWT($accessPayload);
        
        return [
            'success' => true,
            'access_token' => $newAccessToken,
            'expires_in' => $jwtConfig['access_token_expire']
        ];
    }
    
    /**
     * 撤销令牌
     */
    public function revokeToken($token) {
        $payload = $this->validateToken($token);
        
        if ($payload && $payload['type'] === 'refresh') {
            // 从数据库中删除refresh token
            $stmt = $this->pdo->prepare("DELETE FROM refresh_tokens WHERE user_id = ? AND token_id = ?");
            $stmt->execute([$payload['sub'], $payload['jti']]);
        }
        
        // 将token加入黑名单
        $this->addToBlacklist($token);
        
        return ['success' => true];
    }
    
    /**
     * 存储refresh token
     */
    private function storeRefreshToken($userId, $tokenId, $expiresAt) {
        // 确保表存在
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS refresh_tokens (
                id INT AUTOINCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token_id TEXT NOT NULL,
                expires_at DATETIME NOT NULL,
                created_at DATETIME DEFAULT datetime('now'),
                INDEX idx_user_token (user_id, token_id),
                INDEX idx_expires (expires_at)
            )
        ");
        
        $stmt = $this->pdo->prepare("
            INSERT INTO refresh_tokens (user_id, token_id, expires_at) 
            VALUES (?, ?, FROM_UNIXTIME(?))
        ");
        $stmt->execute([$userId, $tokenId, $expiresAt]);
    }
    
    /**
     * 验证refresh token是否有效
     */
    private function isRefreshTokenValid($userId, $tokenId) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) FROM refresh_tokens 
            WHERE user_id = ? AND token_id = ? AND expires_at > datetime('now')
        ");
        $stmt->execute([$userId, $tokenId]);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * 检查账户是否被锁定
     */
    private function isAccountLocked($username) {
        // 确保表存在
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INT AUTOINCREMENT PRIMARY KEY,
                username TEXT NOT NULL,
                ip_address VARCHAR(45),
                attempt_time DATETIME DEFAULT datetime('now'),
                success BOOLEAN DEFAULT 0,
                INDEX idx_username_time (username, attempt_time)
            )
        ");
        
        $lockoutDuration = $this->config->get('security', 'lockout_duration');
        $maxAttempts = $this->config->get('security', 'max_login_attempts');
        
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) FROM login_attempts 
            WHERE username = ? AND success = 0 
            AND attempt_time > DATE_SUB(datetime('now'), INTERVAL ? SECOND)
        ");
        $stmt->execute([$username, $lockoutDuration]);
        
        return $stmt->fetchColumn() >= $maxAttempts;
    }
    
    /**
     * 记录失败登录
     */
    private function recordFailedLogin($username, $clientInfo) {
        $stmt = $this->pdo->prepare("
            INSERT INTO login_attempts (username, ip_address, success) 
            VALUES (?, ?, 0)
        ");
        $stmt->execute([$username, $clientInfo['ip'] ?? '']);
    }
    
    /**
     * 记录成功登录
     */
    private function recordSuccessfulLogin($userId, $clientInfo) {
        // 记录到登录日志
        $stmt = $this->pdo->prepare("
            INSERT INTO login_logs (user_id, ip_address, user_agent, login_status) 
            VALUES (?, ?, ?, 'success')
        ");
        $stmt->execute([
            $userId, 
            $clientInfo['ip'] ?? '', 
            $clientInfo['user_agent'] ?? ''
        ]);
        
        // 更新最后登录时间
        $stmt = $this->pdo->prepare("UPDATE admin_users SET last_login = datetime('now') WHERE id = ?");
        $stmt->execute([$userId]);
    }
    
    /**
     * 清除失败登录记录
     */
    private function clearFailedLogins($username) {
        $stmt = $this->pdo->prepare("DELETE FROM login_attempts WHERE username = ? AND success = 0");
        $stmt->execute([$username]);
    }
    
    /**
     * 添加令牌到黑名单
     */
    private function addToBlacklist($token) {
        // 确保表存在
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS token_blacklist (
                id INT AUTOINCREMENT PRIMARY KEY,
                token_hash TEXT NOT NULL,
                created_at DATETIME DEFAULT datetime('now'),
                INDEX idx_token_hash (token_hash)
            )
        ");
        
        $tokenHash = hash('sha256', $token);
        $stmt = $this->pdo->prepare("INSERT INTO token_blacklist (token_hash) VALUES (?)");
        $stmt->execute([$tokenHash]);
    }
    
    /**
     * 检查令牌是否在黑名单中
     */
    public function isTokenBlacklisted($token) {
        $tokenHash = hash('sha256', $token);
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM token_blacklist WHERE token_hash = ?");
        $stmt->execute([$tokenHash]);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * 清理过期数据
     */
    public function cleanup() {
        // 清理过期的refresh tokens
        $this->pdo->exec("DELETE FROM refresh_tokens WHERE expires_at < datetime('now')");
        
        // 清理旧的登录尝试记录（保留30天）
        $this->pdo->exec("DELETE FROM login_attempts WHERE attempt_time < DATE_SUB(datetime('now'), INTERVAL 30 DAY)");
        
        // 清理旧的黑名单记录（保留7天）
        $this->pdo->exec("DELETE FROM token_blacklist WHERE created_at < DATE_SUB(datetime('now'), INTERVAL 7 DAY)");
    }
} 