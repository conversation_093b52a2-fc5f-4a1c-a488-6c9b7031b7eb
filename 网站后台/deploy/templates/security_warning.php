<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全警告 - 小梅花AI客服系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        /* 动态背景粒子 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: particleFloat 12s infinite linear;
        }
        
        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.5;
            }
            90% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(-10vh) rotate(360deg);
                opacity: 0;
            }
        }
        
        /* 主容器 */
        .warning-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 10;
            animation: slideIn 0.8s ease-out;
            text-align: center;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .warning-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #ffc107 0%, #ff6b6b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 48px;
            color: white;
            box-shadow: 0 15px 40px rgba(255, 193, 7, 0.4);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 15px 40px rgba(255, 193, 7, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 20px 50px rgba(255, 193, 7, 0.6);
            }
        }
        
        .warning-title {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .warning-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .warning-details {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .warning-details h3 {
            color: white;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .detail-value {
            font-family: monospace;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.2);
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .security-measures {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .security-measures h3 {
            color: white;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .security-measures ul {
            list-style: none;
            padding: 0;
        }
        
        .security-measures li {
            color: rgba(255, 255, 255, 0.9);
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .security-measures li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            background: rgba(40, 167, 69, 0.2);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .action-buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .footer-note {
            margin-top: 30px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            line-height: 1.6;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .warning-container {
                margin: 20px;
                padding: 30px 25px;
            }
            
            .warning-title {
                font-size: 24px;
            }
            
            .warning-subtitle {
                font-size: 16px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>
    
    <div class="warning-container">
        <div class="warning-icon">
            🚨
        </div>
        
        <h1 class="warning-title">安全访问限制</h1>
        <p class="warning-subtitle">
            检测到您正在使用陌生设备访问敏感页面<br>
            为保护系统安全，此操作已被阻止
        </p>
        
        <div class="warning-details">
            <h3>
                <i class="fas fa-info-circle"></i>
                访问详情
            </h3>
            <?php
            $current_time = date('Y-m-d H:i:s');
            $user_ip = $_SERVER['REMOTE_ADDR'] ?? '未知';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '未知';
            $device_fingerprint = getDeviceFingerprint();
            $device_name = getDeviceName();
            
            // 页面名称映射
            $page_names = [
                'dashboard' => '仪表盘',
                'keys' => '卡密管理',
                'scripts' => '脚本管理',
                'users' => '用户管理',
                'analytics' => '数据分析',
                'settings' => '系统设置'
            ];
            
            $page_display_name = $page_names[$current_page] ?? $current_page;
            ?>
            
            <div class="detail-item">
                <span class="detail-label">访问时间：</span>
                <span class="detail-value"><?php echo $current_time; ?></span>
            </div>
            <div class="detail-item">
                <span class="detail-label">目标页面：</span>
                <span class="detail-value"><?php echo htmlspecialchars($page_display_name); ?></span>
            </div>
            <div class="detail-item">
                <span class="detail-label">访问IP：</span>
                <span class="detail-value"><?php echo htmlspecialchars($user_ip); ?></span>
            </div>
            <div class="detail-item">
                <span class="detail-label">设备信息：</span>
                <span class="detail-value"><?php echo htmlspecialchars($device_name); ?></span>
            </div>
            <div class="detail-item">
                <span class="detail-label">设备状态：</span>
                <span class="detail-value" style="color: #ffc107; font-weight: bold;">⚠️ 陌生设备</span>
            </div>
        </div>
        
        <div class="security-measures">
            <h3>
                <i class="fas fa-shield-alt"></i>
                安全保护措施
            </h3>
            <ul>
                <li>陌生设备访问已被自动阻止</li>
                <li>安全事件已记录到系统日志</li>
                <li>管理员已收到安全警告邮件</li>
                <li>实时监控系统正常运行</li>
            </ul>
        </div>
        
        <div class="action-buttons">
            <a href="login.php" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i>
                重新登录验证
            </a>
            <a href="?page=dashboard" class="btn btn-secondary">
                <i class="fas fa-home"></i>
                返回首页
            </a>
        </div>
        
        <div class="footer-note">
            <i class="fas fa-info-circle"></i>
            如果您是系统管理员，请使用信任设备登录，或联系技术支持获取帮助。<br>
            此安全机制旨在保护系统免受未授权访问。
        </div>
    </div>
    
    <script>
        // 创建动态背景粒子
        function initializeParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;
            
            // 清空现有粒子
            particlesContainer.innerHTML = '';
            
            // 根据设备调整粒子数量
            const particleCount = window.innerWidth < 768 ? 20 : 40;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机位置和大小
                const size = Math.random() * 4 + 2;
                const x = Math.random() * 100;
                const y = Math.random() * 100;
                const duration = Math.random() * 15 + 10;
                const delay = Math.random() * 8;
                
                particle.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    left: ${x}%;
                    top: ${y}%;
                    animation: particleFloat ${duration}s ${delay}s infinite ease-in-out;
                    pointer-events: none;
                `;
                
                particlesContainer.appendChild(particle);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
            
            // 定期刷新粒子
            setInterval(initializeParticles, 30000);
        });
        
        // 响应式处理
        window.addEventListener('resize', function() {
            setTimeout(initializeParticles, 100);
        });
    </script>
</body>
</html> 