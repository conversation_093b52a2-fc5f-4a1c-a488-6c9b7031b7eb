<?php
/**
 * 代码编程页面 - UserScript代码合并工具
 * 支持主脚本与多个片段的智能合并
 */
?>

<style>
.code-editor-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.code-editor-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
}

.code-editor-header h1 {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.code-editor-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.editor-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.editor-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.editor-section h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.code-textarea {
    width: 100%;
    min-height: 300px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #f8f8f2;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: all 0.3s ease;
}

.code-textarea:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(0, 0, 0, 0.4);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.code-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.fragments-container {
    margin-top: 20px;
}

.fragment-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    position: relative;
}

.fragment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.fragment-title {
    color: white;
    font-size: 16px;
    font-weight: 500;
}

.fragment-remove {
    background: rgba(220, 53, 69, 0.3);
    border: 1px solid rgba(220, 53, 69, 0.4);
    color: #dc3545;
    padding: 5px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.fragment-remove:hover {
    background: rgba(220, 53, 69, 0.5);
    transform: translateY(-1px);
}

.control-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin: 20px 0;
    flex-wrap: wrap;
}

.result-section {
    grid-column: 1 / -1;
    margin-top: 20px;
}

.result-textarea {
    width: 100%;
    min-height: 400px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #f8f8f2;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    readonly: true;
}

.status-message {
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
    font-size: 14px;
    display: none;
    align-items: center;
    gap: 10px;
}

.status-success {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
}

.status-error {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

.loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.feature-tips {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.9);
}

.feature-tips h4 {
    color: #3498db;
    margin-bottom: 10px;
    font-size: 16px;
}

.feature-tips ul {
    margin: 0;
    padding-left: 20px;
}

.feature-tips li {
    margin-bottom: 5px;
    font-size: 14px;
}

/* 模块导航样式 */
.module-navigation {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-tab {
    flex: 1;
    max-width: 200px;
    padding: 15px 25px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    font-weight: 500;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.nav-tab:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-tab.active {
    background: linear-gradient(135deg, #2196f3, #0d47a1);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

.nav-tab i {
    font-size: 18px;
}

/* 模块内容样式 */
.module-content {
    display: none;
}

.module-content.active {
    display: block;
}

/* 代码移除模块样式 */
.remover-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.remover-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.remover-section h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.diff-container {
    margin-top: 20px;
    height: 200px;
    overflow: auto;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.diff-line {
    padding: 2px 5px;
    margin-bottom: 2px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    white-space: pre-wrap;
    line-height: 1.4;
}

.diff-line.removed {
    background-color: rgba(220, 53, 69, 0.2);
    color: #ff6b6b;
    text-decoration: line-through;
}

.diff-line.added {
    background-color: rgba(40, 167, 69, 0.2);
    color: #a9e34b;
}

.diff-line.unchanged {
    color: rgba(255, 255, 255, 0.7);
}

@media (max-width: 768px) {
    .editor-layout, .remover-layout {
        grid-template-columns: 1fr;
    }
    
    .control-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .code-editor-container {
        padding: 15px;
    }
    
    .code-textarea, .result-textarea {
        min-height: 250px;
    }
    
    .module-navigation {
        flex-direction: column;
        gap: 5px;
    }
    
    .nav-tab {
        max-width: none;
    }
}
</style>

<div class="code-editor-container">
    <div class="code-editor-header">
        <h1><i class="fas fa-code"></i> 代码编程工具</h1>
        <p>UserScript代码智能合并系统 - 支持多片段合并、头部智能处理、冲突检测</p>
    </div>
    
    <!-- 模块导航 -->
    <div class="module-navigation">
        <button class="nav-tab active" data-module="merger">
            <i class="fas fa-code-branch"></i> 代码合并
        </button>
        <button class="nav-tab" data-module="remover">
            <i class="fas fa-eraser"></i> 控制面板移除
        </button>
    </div>

            <!-- 代码合并模块 -->
    <div id="merger-module" class="module-content active">
        <div class="feature-tips">
            <h4><i class="fas fa-lightbulb"></i> 代码合并功能特性</h4>
            <ul>
                <li><strong>智能头部合并</strong>：自动处理@name、@include等元数据，主脚本优先级最高</li>
                <li><strong>智能代码插入</strong>：根据代码结构自动选择最佳插入位置，保证脚本正常运行</li>
                <li><strong>多场景支持</strong>：支持IIFE、模块化、jQuery ready、DOM事件等多种代码结构</li>
                <li><strong>代码清理</strong>：自动移除片段中的重复头部和IIFE包装，避免嵌套冲突</li>
                <li><strong>冲突检测</strong>：自动检测潜在的安全风险和语法问题</li>
                <li><strong>动态片段管理</strong>：支持无限添加代码片段，灵活组合</li>
            </ul>
        </div>

    <div id="status-message" class="status-message">
        <i class="fas fa-info-circle"></i>
        <span id="status-text"></span>
    </div>

    <div class="editor-layout">
        <!-- 主脚本编辑区 -->
        <div class="editor-section">
            <h3><i class="fas fa-file-code"></i> 主脚本</h3>
            <textarea 
                id="main-script" 
                class="code-textarea" 
                placeholder="// ==UserScript==&#10;// @name         我的脚本&#10;// @namespace    http://tampermonkey.net/&#10;// @version      1.0&#10;// @description  脚本描述&#10;// <AUTHOR> @match        https://example.com/*&#10;// @grant        none&#10;// ==/UserScript==&#10;&#10;(function() {&#10;    'use strict';&#10;    &#10;    // 主脚本代码&#10;    console.log('主脚本已加载');&#10;    &#10;})();"
            ></textarea>
        </div>

        <!-- 代码片段编辑区 -->
        <div class="editor-section">
            <h3><i class="fas fa-puzzle-piece"></i> 代码片段</h3>
            <div id="fragments-container" class="fragments-container">
                <div class="fragment-item" data-fragment="1">
                    <div class="fragment-header">
                        <span class="fragment-title">片段 #1</span>
                        <button class="fragment-remove" onclick="removeFragment(1)">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                    <textarea 
                        class="code-textarea fragment-textarea" 
                        placeholder="// 代码片段示例&#10;// @include https://another-site.com/*&#10;&#10;function myFunction() {&#10;    console.log('片段功能');&#10;}"
                        style="min-height: 200px;"
                    ></textarea>
                </div>
            </div>
            
            <button class="btn btn-secondary" onclick="addFragment()">
                <i class="fas fa-plus"></i> 添加片段
            </button>
        </div>
    </div>

    <div class="control-buttons">
        <button id="merge-btn" class="btn btn-primary btn-lg" onclick="mergeScripts()">
            <i class="fas fa-magic"></i> 开始合并
            <div id="merge-loading" class="loading-spinner"></div>
        </button>
        
        <button class="btn btn-secondary" onclick="clearAll()">
            <i class="fas fa-eraser"></i> 清空所有
        </button>
        
        <button class="btn btn-success" onclick="downloadResult()">
            <i class="fas fa-download"></i> 下载结果
        </button>
        
        <button class="btn btn-warning" onclick="copyResult()">
            <i class="fas fa-copy"></i> 复制结果
        </button>
    </div>

    <!-- 合并结果展示区 -->
    <div class="editor-section result-section">
        <h3><i class="fas fa-file-alt"></i> 合并结果</h3>
        <textarea 
            id="merge-result" 
            class="result-textarea" 
            readonly
            placeholder="合并后的完整UserScript代码将在这里显示..."
        ></textarea>
    </div>
</div>
    
    <!-- 代码移除模块 -->
    <div id="remover-module" class="module-content">
        <div class="feature-tips">
            <h4><i class="fas fa-lightbulb"></i> 控制面板移除功能特性</h4>
            <ul>
                <li><strong>智能识别</strong>：自动识别并移除Tampermonkey脚本中的控制面板UI元素</li>
                <li><strong>核心保留</strong>：保留所有核心功能代码，只移除UI相关代码</li>
                <li><strong>模式匹配</strong>：支持多种控制面板模式的识别和移除</li>
                <li><strong>差异对比</strong>：实时显示处理前后的代码差异</li>
                <li><strong>一键处理</strong>：简单操作即可获得纯净的功能代码</li>
                <li><strong>安全处理</strong>：确保移除过程不影响核心逻辑</li>
            </ul>
        </div>

        <div id="remover-status-message" class="status-message">
            <i class="fas fa-info-circle"></i>
            <span id="remover-status-text"></span>
        </div>

        <div class="remover-layout">
            <!-- 原始代码输入区 -->
            <div class="remover-section">
                <h3><i class="fas fa-file-code"></i> 原始脚本代码</h3>
                <textarea 
                    id="remover-input" 
                    class="code-textarea" 
                    placeholder="// ==UserScript==&#10;// @name         Example Script&#10;// @version      1.0&#10;// ==/UserScript==&#10;&#10;(function() {&#10;    'use strict';&#10;    &#10;    // 核心功能&#10;    function coreFunction() {&#10;        console.log('核心功能');&#10;    }&#10;    &#10;    // 控制面板&#10;    function createControlPanel() {&#10;        const panel = document.createElement('div');&#10;        // ... 控制面板代码&#10;    }&#10;    &#10;    createControlPanel();&#10;})();"
                    style="min-height: 350px;"
                ></textarea>
            </div>

            <!-- 处理后代码显示区 -->
            <div class="remover-section">
                <h3><i class="fas fa-magic"></i> 移除控制面板后的代码</h3>
                <textarea 
                    id="remover-output" 
                    class="result-textarea" 
                    readonly
                    placeholder="移除控制面板后的纯净代码将在这里显示..."
                    style="min-height: 350px;"
                ></textarea>
            </div>
        </div>

        <div class="control-buttons">
            <button id="remove-panel-btn" class="btn btn-primary btn-lg">
                <i class="fas fa-eraser"></i> 移除控制面板
                <div id="remove-loading" class="loading-spinner"></div>
            </button>
            
            <button id="clear-remover-btn" class="btn btn-secondary">
                <i class="fas fa-trash"></i> 清空内容
            </button>
            
            <button id="copy-remover-result-btn" class="btn btn-success">
                <i class="fas fa-copy"></i> 复制结果
            </button>
        </div>

        <!-- 代码差异对比 -->
        <div class="remover-section">
            <h3><i class="fas fa-exchange-alt"></i> 代码差异对比</h3>
            <div id="remover-diff-container" class="diff-container">
                <div class="diff-line unchanged">处理后将显示代码变化对比...</div>
            </div>
        </div>

        <div class="feature-tips">
            <h4><i class="fas fa-info-circle"></i> 使用说明</h4>
            <ul>
                <li><strong>输入代码</strong>：在左侧输入包含控制面板的Tampermonkey脚本代码</li>
                <li><strong>处理代码</strong>：点击"移除控制面板"按钮自动处理</li>
                <li><strong>查看结果</strong>：右侧显示移除控制面板后的纯净代码</li>
                <li><strong>对比差异</strong>：底部显示详细的代码变化对比</li>
                <li><strong>复制使用</strong>：使用"复制结果"按钮获取处理后的代码</li>
            </ul>
        </div>
    </div>
</div>

<script>
let fragmentCounter = 1;

// 添加代码片段
function addFragment() {
    fragmentCounter++;
    const container = document.getElementById('fragments-container');
    
    const fragmentHtml = `
        <div class="fragment-item" data-fragment="${fragmentCounter}">
            <div class="fragment-header">
                <span class="fragment-title">片段 #${fragmentCounter}</span>
                <button class="fragment-remove" onclick="removeFragment(${fragmentCounter})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
            <textarea 
                class="code-textarea fragment-textarea" 
                placeholder="// 代码片段 #${fragmentCounter}&#10;// 在这里输入要合并的代码..."
                style="min-height: 200px;"
            ></textarea>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', fragmentHtml);
    showStatus('已添加新的代码片段', 'success');
}

// 删除代码片段
function removeFragment(id) {
    const fragment = document.querySelector(`[data-fragment="${id}"]`);
    if (fragment) {
        fragment.remove();
        showStatus('代码片段已删除', 'success');
    }
}

// 显示状态消息
function showStatus(message, type = 'success') {
    const statusDiv = document.getElementById('status-message');
    const statusText = document.getElementById('status-text');
    
    statusDiv.className = `status-message status-${type}`;
    statusText.textContent = message;
    statusDiv.style.display = 'flex';
    
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

// 合并脚本
async function mergeScripts() {
    const mainScript = document.getElementById('main-script').value.trim();
    const fragmentTextareas = document.querySelectorAll('.fragment-textarea');
    
    if (!mainScript) {
        showStatus('请输入主脚本代码', 'error');
        return;
    }
    
    // 收集所有片段
    const fragments = [];
    fragmentTextareas.forEach(textarea => {
        const content = textarea.value.trim();
        if (content) {
            fragments.push(content);
        }
    });
    
    // 显示加载状态
    const mergeBtn = document.getElementById('merge-btn');
    const loadingSpinner = document.getElementById('merge-loading');
    
    mergeBtn.disabled = true;
    loadingSpinner.style.display = 'inline-block';
    
    try {
        // 调用专门的API文件
        const response = await fetch('../api/code_merger.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                main_script: mainScript,
                fragments: fragments
            })
        });
        
        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            document.getElementById('merge-result').value = result.result;
            showStatus(result.message, 'success');
        } else {
            showStatus(result.message, 'error');
        }
        
    } catch (error) {
        console.error('合并请求失败:', error);
        showStatus('合并失败: ' + error.message, 'error');
    } finally {
        mergeBtn.disabled = false;
        loadingSpinner.style.display = 'none';
    }
}

// 清空所有内容
function clearAll() {
    if (confirm('确定要清空所有内容吗？此操作不可撤销。')) {
        document.getElementById('main-script').value = '';
        document.getElementById('merge-result').value = '';
        
        // 清空所有片段
        const fragments = document.querySelectorAll('.fragment-textarea');
        fragments.forEach(textarea => {
            textarea.value = '';
        });
        
        showStatus('所有内容已清空', 'success');
    }
}

// 下载结果
function downloadResult() {
    const result = document.getElementById('merge-result').value;
    if (!result.trim()) {
        showStatus('没有可下载的内容', 'error');
        return;
    }
    
    const blob = new Blob([result], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'merged-userscript.user.js';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showStatus('文件下载已开始', 'success');
}

// 复制结果
async function copyResult() {
    const result = document.getElementById('merge-result').value;
    if (!result.trim()) {
        showStatus('没有可复制的内容', 'error');
        return;
    }
    
    try {
        await navigator.clipboard.writeText(result);
        showStatus('代码已复制到剪贴板', 'success');
    } catch (error) {
        // 降级到传统方法
        const textarea = document.getElementById('merge-result');
        textarea.select();
        document.execCommand('copy');
        showStatus('代码已复制到剪贴板', 'success');
    }
}

// 代码移除相关功能
function showRemoverStatus(message, type = 'success') {
    const statusDiv = document.getElementById('remover-status-message');
    const statusText = document.getElementById('remover-status-text');
    
    statusDiv.className = `status-message status-${type}`;
    statusText.textContent = message;
    statusDiv.style.display = 'flex';
    
    setTimeout(() => {
        statusDiv.style.display = 'none';
    }, 3000);
}

// 移除控制面板功能
function removeControlPanel() {
    const inputCode = document.getElementById('remover-input').value.trim();
    const outputTextarea = document.getElementById('remover-output');
    const diffContainer = document.getElementById('remover-diff-container');
    const removeBtn = document.getElementById('remove-panel-btn');
    const loadingSpinner = document.getElementById('remove-loading');
    
    if (!inputCode) {
        showRemoverStatus('请输入脚本代码', 'error');
        return;
    }
    
    // 显示加载状态
    removeBtn.disabled = true;
    loadingSpinner.style.display = 'inline-block';
    
    try {
        // 执行代码移除逻辑
        const cleanedCode = processControlPanelRemoval(inputCode);
        
        // 显示结果
        outputTextarea.value = cleanedCode;
        
        // 生成差异对比
        generateRemoverDiff(inputCode, cleanedCode, diffContainer);
        
        showRemoverStatus('控制面板已成功移除！', 'success');
        
    } catch (error) {
        showRemoverStatus('处理失败: ' + error.message, 'error');
        console.error('代码移除错误:', error);
    } finally {
        removeBtn.disabled = false;
        loadingSpinner.style.display = 'none';
    }
}

// 控制面板移除处理逻辑
function processControlPanelRemoval(code) {
    // 定义控制面板相关的正则表达式模式
    const patterns = [
        // 移除控制面板创建函数
        /function\s+createControlPanel\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        /function\s+showControlPanel\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        /function\s+initUI\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        /function\s+createPanel\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        
        // 移除设置面板相关函数
        /function\s+showSettings\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        /function\s+saveSettings\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        /function\s+loadSettings\s*\([^)]*\)\s*\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        
        // 移除控制面板初始化调用
        /createControlPanel\s*\(\s*\)\s*;/g,
        /initUI\s*\(\s*\)\s*;/g,
        /showControlPanel\s*\(\s*\)\s*;/g,
        /createPanel\s*\(\s*\)\s*;/g,
        
        // 移除控制面板元素创建和添加
        /const\s+panel\s*=\s*document\.createElement\s*\(\s*['"]\w+['"]\s*\)\s*;[\s\S]*?document\.body\.appendChild\s*\(\s*panel\s*\)\s*;/g,
        /let\s+panel\s*=\s*document\.createElement\s*\(\s*['"]\w+['"]\s*\)\s*;[\s\S]*?document\.body\.appendChild\s*\(\s*panel\s*\)\s*;/g,
        /var\s+panel\s*=\s*document\.createElement\s*\(\s*['"]\w+['"]\s*\)\s*;[\s\S]*?document\.body\.appendChild\s*\(\s*panel\s*\)\s*;/g,
        
        // 移除控制面板相关的样式添加
        /GM_addStyle\s*\(\s*[`'"][^`'"]*(?:control-panel|panel|ui-panel)[^`'"]*[`'"]\s*\)\s*;/g,
        
        // 移除控制面板相关的变量声明
        /(const|let|var)\s+(controlPanel|settingsPanel|uiPanel)\s*=[\s\S]*?;/g,
        
        // 移除控制面板相关的事件监听器
        /document\.addEventListener\s*\(\s*['"]keydown['"]\s*,\s*function[^}]*showControlPanel[^}]*\}\s*\)\s*;/g,
        
        // 移除控制面板切换快捷键
        /if\s*\(\s*e\.key\s*===?\s*['"][^'"]*['"]\s*\)\s*\{[^}]*(?:show|toggle|create).*?panel[^}]*\}/gi
    ];
    
    let result = code;
    
    // 应用所有移除模式
    patterns.forEach(pattern => {
        result = result.replace(pattern, '');
    });
    
    // 清理空行和多余空格
    result = result
        .replace(/\n\s*\n\s*\n/g, '\n\n')  // 移除多个连续空行
        .replace(/^\s+$/gm, '')            // 移除只包含空格的行
        .trim();                           // 移除首尾空白
    
    return result;
}

// 生成代码差异对比
function generateRemoverDiff(original, processed, container) {
    const originalLines = original.split('\n');
    const processedLines = processed.split('\n');
    
    let diffHTML = '';
    let originalIndex = 0;
    let processedIndex = 0;
    
    while (originalIndex < originalLines.length || processedIndex < processedLines.length) {
        const originalLine = originalLines[originalIndex] || '';
        const processedLine = processedLines[processedIndex] || '';
        
        if (originalLine === processedLine) {
            // 相同的行
            diffHTML += `<div class="diff-line unchanged">  ${escapeHtml(originalLine)}</div>`;
            originalIndex++;
            processedIndex++;
        } else {
            // 检查是否是删除的行
            let found = false;
            for (let i = processedIndex; i < Math.min(processedIndex + 5, processedLines.length); i++) {
                if (originalLine === processedLines[i]) {
                    // 在处理后的代码中找到了这行，说明中间有被删除的行
                    for (let j = processedIndex; j < i; j++) {
                        diffHTML += `<div class="diff-line added">+ ${escapeHtml(processedLines[j])}</div>`;
                    }
                    diffHTML += `<div class="diff-line unchanged">  ${escapeHtml(originalLine)}</div>`;
                    originalIndex++;
                    processedIndex = i + 1;
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                // 检查是否是被删除的行
                let foundInOriginal = false;
                for (let i = originalIndex + 1; i < Math.min(originalIndex + 5, originalLines.length); i++) {
                    if (processedLine === originalLines[i]) {
                        // 在原始代码中找到了这行，说明中间有被删除的行
                        for (let j = originalIndex; j < i; j++) {
                            diffHTML += `<div class="diff-line removed">- ${escapeHtml(originalLines[j])}</div>`;
                        }
                        diffHTML += `<div class="diff-line unchanged">  ${escapeHtml(processedLine)}</div>`;
                        originalIndex = i + 1;
                        processedIndex++;
                        foundInOriginal = true;
                        break;
                    }
                }
                
                if (!foundInOriginal) {
                    // 简单的不同行处理
                    if (originalIndex < originalLines.length) {
                        diffHTML += `<div class="diff-line removed">- ${escapeHtml(originalLine)}</div>`;
                        originalIndex++;
                    }
                    if (processedIndex < processedLines.length) {
                        diffHTML += `<div class="diff-line added">+ ${escapeHtml(processedLine)}</div>`;
                        processedIndex++;
                    }
                }
            }
        }
    }
    
    container.innerHTML = diffHTML || '<div class="diff-line unchanged">没有检测到显著变化</div>';
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 清空代码移除器内容
function clearRemoverContent() {
    if (confirm('确定要清空所有内容吗？')) {
        document.getElementById('remover-input').value = '';
        document.getElementById('remover-output').value = '';
        document.getElementById('remover-diff-container').innerHTML = 
            '<div class="diff-line unchanged">处理后将显示代码变化对比...</div>';
        showRemoverStatus('内容已清空', 'success');
    }
}

// 复制代码移除结果
async function copyRemoverResult() {
    const result = document.getElementById('remover-output').value;
    if (!result.trim()) {
        showRemoverStatus('没有可复制的内容', 'error');
        return;
    }
    
    try {
        await navigator.clipboard.writeText(result);
        showRemoverStatus('代码已复制到剪贴板', 'success');
    } catch (error) {
        // 降级到传统方法
        const textarea = document.getElementById('remover-output');
        textarea.select();
        document.execCommand('copy');
        showRemoverStatus('代码已复制到剪贴板', 'success');
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('代码编程工具已加载');
    
    // 初始化模块导航
    initModuleNavigation();
    
    // 代码移除功能事件监听
    const removePanelBtn = document.getElementById('remove-panel-btn');
    const clearRemoverBtn = document.getElementById('clear-remover-btn');
    const copyRemoverResultBtn = document.getElementById('copy-remover-result-btn');
    
    if (removePanelBtn) {
        removePanelBtn.addEventListener('click', removeControlPanel);
    }
    if (clearRemoverBtn) {
        clearRemoverBtn.addEventListener('click', clearRemoverContent);
    }
    if (copyRemoverResultBtn) {
        copyRemoverResultBtn.addEventListener('click', copyRemoverResult);
    }
    
    // 为所有文本框添加语法高亮提示
    const textareas = document.querySelectorAll('.code-textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            // 简单的语法检查
            const content = this.value;
            if (content.includes('// ==UserScript==') && !content.includes('// ==/UserScript==')) {
                this.style.borderColor = 'rgba(255, 193, 7, 0.6)';
            } else {
                this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
            }
        });
    });
});

// 模块导航初始化
function initModuleNavigation() {
    const navTabs = document.querySelectorAll('.nav-tab');
    
    // 从localStorage获取上次选中的模块
    const lastActiveModule = localStorage.getItem('xiaomeihua_codeeditor_module');
    
    // 默认激活第一个或上次选中的模块
    let activeTabFound = false;
    
    navTabs.forEach(tab => {
        const moduleName = tab.getAttribute('data-module');
        
        // 如果是上次选中的模块，则激活它
        if (lastActiveModule && moduleName === lastActiveModule) {
            switchModule(moduleName);
            tab.classList.add('active');
            activeTabFound = true;
        }
        
        // 添加点击事件
        tab.addEventListener('click', function() {
            const moduleName = this.getAttribute('data-module');
            switchModule(moduleName);
        });
    });
}

// 模块切换功能
function switchModule(moduleName) {
    // 隐藏所有模块
    document.querySelectorAll('.module-content').forEach(module => {
        module.classList.remove('active');
    });
    
    // 移除所有导航标签的active状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 激活选中的模块
    const targetModule = document.getElementById(moduleName + '-module');
    if (targetModule) {
        targetModule.classList.add('active');
    }
    
    // 激活对应的导航标签
    const targetTab = document.querySelector(`.nav-tab[data-module="${moduleName}"]`);
    if (targetTab) {
        targetTab.classList.add('active');
    }
    
    // 保存当前选中的模块到localStorage
    localStorage.setItem('xiaomeihua_codeeditor_module', moduleName);
}
</script>