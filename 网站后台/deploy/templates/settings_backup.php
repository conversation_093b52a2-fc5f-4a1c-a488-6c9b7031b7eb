<?php
require_once '../includes/functions.php';

// 获取flash消息函数
function getFlashMessage() {
    $messages = [];
    
    if (isset($_SESSION['flash_success'])) {
        $messages['success'] = $_SESSION['flash_success'];
        unset($_SESSION['flash_success']);
    }
    
    if (isset($_SESSION['flash_error'])) {
        $messages['error'] = $_SESSION['flash_error'];
        unset($_SESSION['flash_error']);
    }
    
    return $messages;
}

// POST重定向函数
function handlePostRedirect($page, $success_message = '', $error_message = '') {
    // 将消息存储到session中
    if (!empty($success_message)) {
        $_SESSION['flash_success'] = $success_message;
    }
    if (!empty($error_message)) {
        $_SESSION['flash_error'] = $error_message;
    }
    
    // 重定向到指定页面
    $redirect_url = "index.php?page=" . urlencode($page);
    header("Location: " . $redirect_url);
    exit;
}

// 获取flash消息
$flash_messages = getFlashMessage();
$success_message = $flash_messages['success'] ?? '';
$error_message = $flash_messages['error'] ?? '';

// 处理表单提交
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_smtp':
                // 检查用户是否已登录
                if (!isset($_SESSION['admin_user_id'])) {
                    $error_message = "用户未登录";
                    break;
                }
                
                $smtp_settings = [
                    'smtp_host' => $_POST['smtp_host'] ?? '',
                    'smtp_port' => $_POST['smtp_port'] ?? '465',
                    'smtp_username' => $_POST['smtp_username'] ?? '',
                    'smtp_password' => $_POST['smtp_password'] ?? '',
                    'email_verification_enabled' => isset($_POST['email_verification_enabled']) ? '1' : '0',
                    'login_notification_enabled' => isset($_POST['login_notification_enabled']) ? '1' : '0'
                ];
                
                $saved_count = 0;
                foreach ($smtp_settings as $key => $value) {
                    try {
                        // 确保system_settings表存在
                        $pdo->exec("CREATE TABLE IF NOT EXISTS system_settings (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            setting_key VARCHAR(100) NOT NULL UNIQUE,
                            setting_value TEXT,
                            description VARCHAR(255) DEFAULT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        )");
                        
                        $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
                        if ($stmt->execute([$key, $value, $value])) {
                            $saved_count++;
                        }
                    } catch (Exception $e) {
                        error_log("SMTP设置保存失败: " . $e->getMessage());
                    }
                }
                
                if ($saved_count == count($smtp_settings)) {
                    handlePostRedirect('settings', 'SMTP配置已保存');
                } else {
                    handlePostRedirect('settings', '', 'SMTP配置保存失败，请重试');
                }
                break;
                
            case 'update_admin':
                // 检查用户是否已登录
                if (!isset($_SESSION['admin_user_id'])) {
                    $error_message = "用户未登录";
                    break;
                }
                
                $new_username = trim($_POST['new_username'] ?? '');
                $current_password = $_POST['current_password'] ?? '';
                $new_password = $_POST['new_password'] ?? '';
                $confirm_password = $_POST['confirm_password'] ?? '';
                
                if (empty($new_username) || empty($current_password)) {
                    $error_message = "用户名和当前密码不能为空";
                    break;
                }
                
                // 验证当前密码
                $stmt = $pdo->prepare("SELECT password FROM admin_users WHERE id = ?");
                $stmt->execute([$_SESSION['admin_user_id']]);
                $current_hash = $stmt->fetchColumn();
                
                if (md5($current_password) !== $current_hash) {
                    $error_message = "当前密码错误";
                    break;
                }
                
                // 检查是否修改密码
                $update_password = false;
                if (!empty($new_password)) {
                    if ($new_password !== $confirm_password) {
                        $error_message = "新密码与确认密码不匹配";
                        break;
                    }
                    if (strlen($new_password) < 6) {
                        $error_message = "新密码长度不能少于6位";
                        break;
                    }
                    $update_password = true;
                }
                
                // 更新用户信息
                if ($update_password) {
                    $stmt = $pdo->prepare("UPDATE admin_users SET username = ?, password = ? WHERE id = ?");
                    $result = $stmt->execute([$new_username, md5($new_password), $_SESSION['admin_user_id']]);
                } else {
                    $stmt = $pdo->prepare("UPDATE admin_users SET username = ? WHERE id = ?");
                    $result = $stmt->execute([$new_username, $_SESSION['admin_user_id']]);
                }
                
                if ($result) {
                    $_SESSION['admin_username'] = $new_username;
                    $success_message = $update_password ? "账号信息和密码已更新" : "用户名已更新";
                    handlePostRedirect('settings', $success_message);
                } else {
                    $error_message = "更新失败，请重试";
                }
                break;
        }
    }
}

// 获取当前设置
try {
    $settings = getSystemSettings();
} catch (Exception $e) {
    $settings = [];
}

// 检查是否有现有的SMTP配置
$has_smtp_config = false;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM smtp_config WHERE user_id = ?");
    $stmt->execute([$_SESSION['admin_user_id']]);
    $has_smtp_config = $stmt->fetchColumn() > 0;
} catch (Exception $e) {
    // smtp_config表可能不存在，检查system_settings中的配置
    $has_smtp_config = !empty($settings['smtp_host']) && !empty($settings['smtp_username']) && !empty($settings['smtp_password']);
}

// 获取信任设备列表
try {
    $stmt = $pdo->prepare("SELECT * FROM trusted_devices WHERE user_id = ? AND is_active = 1 ORDER BY created_at DESC");
    $stmt->execute([$_SESSION['admin_user_id']]);
    $trusted_devices = $stmt->fetchAll();
} catch (Exception $e) {
    $trusted_devices = [];
}

// 获取登录日志
try {
    $stmt = $pdo->prepare("SELECT * FROM login_logs WHERE user_id = ? ORDER BY login_time DESC LIMIT 20");
    $stmt->execute([$_SESSION['admin_user_id']]);
    $login_logs = $stmt->fetchAll();
} catch (Exception $e) {
    $login_logs = [];
}

$message = '';

// 显示处理结果消息
if (!empty($success_message)) {
    echo "<div class='message success'><i class='fas fa-check-circle'></i> {$success_message}</div>";
}
if (!empty($error_message)) {
    echo "<div class='message error'><i class='fas fa-exclamation-triangle'></i> {$error_message}</div>";
}
?>

<div class="settings-page">
    <div class="page-header">
        <h2><i class="fas fa-cog"></i> 系统设置</h2>
        <p>配置系统参数和功能选项</p>
    </div>
    
    <div class="settings-grid">
        <!-- 管理员账号设置 -->
        <div class="settings-card">
            <div class="card-header">
                <h3><i class="fas fa-user-shield"></i> 管理员账号</h3>
                <span class="help-text">修改登录账号和密码</span>
            </div>
            
            <form method="POST" class="settings-form">
                <input type="hidden" name="action" value="update_admin">
                
                <div class="form-group">
                    <label>当前用户名</label>
                    <input type="text" name="new_username" value="<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'admin'); ?>" placeholder="输入新用户名" required>
                </div>
                
                <div class="form-group">
                    <label>当前密码（验证身份）</label>
                    <input type="password" name="current_password" placeholder="输入当前密码以验证身份" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>新密码（留空不修改）</label>
                        <input type="password" name="new_password" placeholder="输入新密码">
                    </div>
                    <div class="form-group">
                        <label>确认新密码</label>
                        <input type="password" name="confirm_password" placeholder="再次输入新密码">
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary glass-btn">
                        <i class="fas fa-save"></i> 更新账号信息
                    </button>
                </div>
            </form>
        </div>
        
        <!-- SMTP邮箱配置 -->
        <div class="settings-card">
            <div class="card-header">
                <h3><i class="fas fa-envelope"></i> 邮箱配置</h3>
                <span class="status-badge glass-badge <?php echo !empty($settings['smtp_username']) ? 'status-active' : 'status-inactive'; ?>">
                    <?php echo !empty($settings['smtp_username']) ? '已配置' : '未配置'; ?>
                </span>
            </div>
            
            <form method="POST" class="settings-form" id="smtpForm">
                <input type="hidden" name="action" value="update_smtp">
                
                <div class="form-row">
                    <div class="form-group">
                        <label>SMTP服务器</label>
                        <input type="text" name="smtp_host" id="smtp_host" value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>" placeholder="smtp.163.com">
                    </div>
                    <div class="form-group">
                        <label>SMTP端口</label>
                        <input type="number" name="smtp_port" id="smtp_port" value="<?php echo htmlspecialchars(!empty($settings['smtp_port']) ? $settings['smtp_port'] : '465'); ?>" placeholder="465">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>邮箱账号</label>
                        <input type="email" name="smtp_username" id="smtp_username" value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>邮箱密码/授权码</label>
                        <input type="password" name="smtp_password" id="smtp_password" value="<?php echo htmlspecialchars($settings['smtp_password'] ?? ''); ?>" placeholder="授权码">
                    </div>
                </div>
                
                <!-- 验证码输入区域（仅在修改现有配置时显示） -->
                <div class="form-row" id="emailVerificationSection" style="display: none;">
                    <div class="form-group">
                        <label>邮箱验证码</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <input type="text" name="email_code" id="smtp_email_code" placeholder="输入6位验证码" maxlength="6">
                            <button type="button" class="btn btn-secondary btn-sm" id="sendSmtpCodeBtn" onclick="sendSmtpConfigCode()">
                                <i class="fas fa-paper-plane"></i> 发送验证码
                            </button>
                        </div>
                        <small style="color: #999; margin-top: 5px; display: block;">修改邮箱配置需要验证码确认，有效期20秒</small>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <div class="toggle-group">
                            <label class="toggle-switch">
                                <input type="checkbox" name="email_verification_enabled" id="email_verification_enabled" value="1" <?php echo ($settings['email_verification_enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <span class="toggle-label">启用邮箱验证登录</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="toggle-group">
                            <label class="toggle-switch">
                                <input type="checkbox" name="login_notification_enabled" id="login_notification_enabled" value="1" <?php echo ($settings['login_notification_enabled'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <span class="toggle-label">启用登录通知邮件</span>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary glass-btn">
                        <i class="fas fa-save"></i> 保存配置
                    </button>
                    <button type="button" class="btn btn-secondary glass-btn" id="testEmailBtn" onclick="testEmailSettings()" disabled>
                        <i class="fas fa-paper-plane"></i> 发送测试邮件
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 信任设备管理 -->
        <div class="settings-card">
            <div class="card-header">
                <h3><i class="fas fa-shield-alt"></i> 信任设备管理</h3>
                <span class="help-text">管理受信任的登录设备</span>
            </div>
            
            <!-- 当前设备信息 -->
            <div class="current-device-info">
                <div class="device-detection">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4><i class="fas fa-desktop"></i> 当前设备信息</h4>
                        <button type="button" class="btn-sm btn-secondary" onclick="refreshDeviceInfo()" title="刷新设备信息">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="device-details" id="deviceDetails">
                        <div class="device-item">
                            <span class="device-label">设备类型：</span>
                            <span class="device-value" id="deviceType">正在检测...</span>
                        </div>
                        <div class="device-item">
                            <span class="device-label">浏览器：</span>
                            <span class="device-value" id="browserInfo">正在检测...</span>
                        </div>
                        <div class="device-item">
                            <span class="device-label">操作系统：</span>
                            <span class="device-value" id="osInfo">正在检测...</span>
                        </div>
                        <div class="device-item">
                            <span class="device-label">设备指纹：</span>
                            <span class="device-value" id="deviceFingerprint">正在生成...</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <button type="button" class="btn btn-primary glass-btn" onclick="trustCurrentDevice()">
                            <i class="fas fa-shield-check"></i> 信任此设备
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="trusted-devices">
                <?php if (empty($trusted_devices)): ?>
                    <div class="no-devices">
                        <i class="fas fa-mobile-alt"></i>
                        <p>暂无信任设备</p>
                        <small>点击上方"信任此设备"后将显示在这里</small>
                    </div>
                <?php else: ?>
                    <h4 style="color: white; margin: 20px 0 15px 0;"><i class="fas fa-list"></i> 已信任设备</h4>
                    <div class="devices-list">
                        <?php foreach ($trusted_devices as $device): ?>
                            <div class="device-item">
                                <div class="device-info">
                                    <div class="device-name">
                                        <i class="fas fa-desktop"></i>
                                        <?php echo htmlspecialchars($device['device_name']); ?>
                                    </div>
                                    <div class="device-details">
                                        <small>添加时间：<?php echo date('Y-m-d H:i', strtotime($device['created_at'])); ?></small>
                                    </div>
                                </div>
                                <div class="device-actions">
                                    <button class="btn-sm btn-danger" onclick="removeDevice(<?php echo $device['id']; ?>)">
                                        <i class="fas fa-trash"></i> 移除
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 系统信息 -->
        <div class="settings-card">
            <div class="card-header">
                <h3><i class="fas fa-info-circle"></i> 系统信息</h3>
                <span class="help-text">当前系统运行状态</span>
            </div>
            
            <div class="system-info">
                <div class="info-item">
                    <span class="info-label">系统版本</span>
                    <span class="info-value">v1.2.0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">PHP版本</span>
                    <span class="info-value"><?php echo PHP_VERSION; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">数据库状态</span>
                    <span class="info-value status-active">正常连接</span>
                </div>
                <div class="info-item">
                    <span class="info-label">邮件服务</span>
                    <span class="info-value glass-badge <?php echo !empty($settings['smtp_username']) ? 'status-active' : 'status-inactive'; ?>">
                        <?php echo !empty($settings['smtp_username']) ? '已配置' : '未配置'; ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">系统时间</span>
                    <span class="info-value"><?php echo date('Y-m-d H:i:s'); ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.settings-page {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 30px;
}

.page-header h2 {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
}

.page-header p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.settings-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 25px;
    transition: all 0.3s ease;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.help-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.status-inactive {
    background: rgba(108, 117, 125, 0.2);
}

/* 毛玻璃按钮效果 */
.glass-btn {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.glass-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* 毛玻璃徽章效果 */
.glass-badge {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* 开关按钮样式 */
.toggle-group {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: .4s;
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #ff6b9d;
    border-color: #ff6b9d;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    color: white;
    font-weight: 500;
    font-size: 14px;
}
    color: #6c757d;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: white;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-group input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px 12px;
    color: white;
    font-size: 13px;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 16px;
    height: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #ff6b9d;
    border-color: #ff6b9d;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.form-actions {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
}

.no-devices {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
}

.no-devices i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.devices-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.device-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.device-name {
    color: white;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.device-details small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 11px;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 11px;
    border-radius: 6px;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
}

.info-value {
    color: white;
    font-size: 13px;
    font-weight: 500;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.alert-success {
    background: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
}

.alert-error {
    background: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

@media (max-width: 768px) {
    .settings-page {
        padding: 20px;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .device-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

.system-info .info-value.status-inactive {
    color: #6c757d;
}

/* 设备管理样式 */
.current-device-info {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
}

.device-detection h4 {
    color: white;
    font-size: 16px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.device-details .device-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.device-details .device-item:last-child {
    border-bottom: none;
}

.device-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    font-weight: 500;
}

.device-value {
    color: white;
    font-size: 13px;
    font-family: monospace;
}

.devices-list .device-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 10px;
}

.device-info .device-name {
    color: white;
    font-weight: 500;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.device-info .device-details small {
    color: rgba(255, 255, 255, 0.6);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* 密码验证弹窗样式 */
.password-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.password-modal.active {
    opacity: 1;
    visibility: visible;
}

.password-modal-content {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 25px;
    max-width: 350px;
    width: 85%;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.password-modal.active .password-modal-content {
    transform: translateY(0);
}

.password-modal h3 {
    color: white;
    text-align: center;
    margin-bottom: 15px;
    font-size: 16px;
}

.password-modal .form-group {
    margin-bottom: 15px;
}

.password-modal .form-group label {
    color: white;
    font-size: 14px;
    margin-bottom: 8px;
    display: block;
}

.password-modal .form-group input {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px;
    color: white;
    font-size: 14px;
    box-sizing: border-box;
}

.password-modal .form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.password-modal .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.password-modal .btn-primary {
    background: #007bff;
    color: white;
}

.password-modal .btn-primary:hover {
    background: #0056b3;
}

.password-modal .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.password-modal .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}
</style>

<script>
// 初始化SMTP配置状态
var hasInitialSmtpConfig = <?php echo $has_smtp_config ? 'true' : 'false'; ?>;
console.log('🔧 初始化SMTP配置状态:', hasInitialSmtpConfig);

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 页面DOM加载完成');
    
    initEmailTestButton();
    checkSmtpConfigChange(); // 初始化SMTP配置变化检测
    
    // 等待页面完全渲染后再执行设备检测
    setTimeout(() => {
        console.log('🚀 开始执行设备检测...');
        detectDevice();
    }, 100);
    
    // 1秒后重试检测
    setTimeout(() => {
        const deviceTypeElement = document.getElementById('deviceType');
        if (deviceTypeElement && (deviceTypeElement.textContent.includes('正在检测') || deviceTypeElement.textContent.includes('检测失败'))) {
            console.log('🔄 设备检测失败，正在重试...');
            detectDevice();
        }
    }, 1000);
    
    // 2秒后强制检测
    setTimeout(() => {
        const deviceTypeElement = document.getElementById('deviceType');
        if (deviceTypeElement && (deviceTypeElement.textContent.includes('正在检测') || deviceTypeElement.textContent.includes('检测失败'))) {
            console.log('🔄 最后一次重试设备检测...');
            forceDetectDevice();
        }
    }, 2000);
    
    // SMTP表单AJAX提交
    const smtpForm = document.getElementById('smtpForm');
    if (smtpForm) {
        smtpForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitSmtpConfig();
        });
        
        // 添加表单字段监听器，实时检查是否需要验证码
        const fields = smtpForm.querySelectorAll('input[name^="smtp_"], input[name="from_email"], input[name="from_name"]');
        fields.forEach(field => {
            field.addEventListener('input', function() {
                // 延迟检查，避免频繁触发
                clearTimeout(window.smtpCheckTimeout);
                window.smtpCheckTimeout = setTimeout(() => {
                    console.log('📝 检测到输入变化，重新检查验证码需求');
                    checkSmtpConfigChange();
                }, 300);
            });
            
            // 添加焦点事件监听
            field.addEventListener('focus', function() {
                console.log('🎯 字段获得焦点，检查验证码需求');
                checkSmtpConfigChange();
            });
        });
    }
});

// 页面完全加载后的额外检查
window.addEventListener('load', function() {
    console.log('🌐 页面完全加载完成，执行最终检查');
    
    // 最终检查设备检测状态
    setTimeout(() => {
        const deviceTypeElement = document.getElementById('deviceType');
        if (deviceTypeElement && deviceTypeElement.textContent.includes('正在检测')) {
            console.log('🔧 页面加载后设备仍在检测中，强制执行检测');
            forceDetectDevice();
        }
    }, 500);
    
    // 最终检查验证码状态
    setTimeout(() => {
        console.log('🔧 页面加载后检查SMTP配置状态');
        checkSmtpConfigChange();
    }, 300);
});

// 强制设备检测（用于最后的保障）
function forceDetectDevice() {
    console.log('🚀 强制执行设备检测...');
    
    // 直接设置设备信息，不依赖DOM查找
    try {
        // 设备类型
        const deviceType = /Mobi|Android/i.test(navigator.userAgent) ? '移动设备' : '桌面设备';
        const deviceTypeEl = document.getElementById('deviceType');
        if (deviceTypeEl) deviceTypeEl.textContent = deviceType;
        
        // 浏览器信息
        let browserInfo = '未知浏览器';
        const ua = navigator.userAgent;
        if (ua.includes('Chrome') && !ua.includes('Edge')) {
            const match = ua.match(/Chrome\/(\d+)/);
            browserInfo = match ? `Chrome ${match[1]}` : 'Chrome';
        } else if (ua.includes('Firefox')) {
            const match = ua.match(/Firefox\/(\d+)/);
            browserInfo = match ? `Firefox ${match[1]}` : 'Firefox';
        } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
            browserInfo = 'Safari';
        } else if (ua.includes('Edge')) {
            browserInfo = 'Edge';
        }
        const browserEl = document.getElementById('browserInfo');
        if (browserEl) browserEl.textContent = browserInfo;
        
        // 操作系统
        let osInfo = '未知系统';
        if (ua.includes('Windows NT 10.0')) osInfo = 'Windows 10/11';
        else if (ua.includes('Windows')) osInfo = 'Windows';
        else if (ua.includes('Mac OS X')) osInfo = 'macOS';
        else if (ua.includes('Linux')) osInfo = 'Linux';
        else if (ua.includes('Android')) osInfo = 'Android';
        else if (ua.includes('iPhone') || ua.includes('iPad')) osInfo = 'iOS';
        const osEl = document.getElementById('osInfo');
        if (osEl) osEl.textContent = osInfo;
        
        // 设备指纹
        const timestamp = Date.now().toString(16);
        const simpleFingerprint = btoa(navigator.userAgent + screen.width + screen.height).substring(0, 16);
        const fingerprintEl = document.getElementById('deviceFingerprint');
        if (fingerprintEl) fingerprintEl.textContent = simpleFingerprint + '...';
        
        console.log('✅ 强制设备检测完成');
    } catch (error) {
        console.error('❌ 强制设备检测失败:', error);
    }
}

// 手动刷新设备信息
function refreshDeviceInfo() {
    console.log('🔄 手动刷新设备信息');
    
    // 重置显示状态
    const elements = ['deviceType', 'browserInfo', 'osInfo', 'deviceFingerprint'];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '正在检测...';
            element.style.color = '#999';
        }
    });
    
    // 立即重新检测
    detectDevice();
}

// SMTP配置提交
function submitSmtpConfig() {
    const form = document.getElementById('smtpForm');
    const formData = new FormData(form);
    formData.append('action', 'update_smtp');
    
    // 确保复选框状态正确提交
    const emailVerificationEnabled = document.getElementById('email_verification_enabled').checked;
    const loginNotificationEnabled = document.getElementById('login_notification_enabled').checked;
    
    formData.set('email_verification_enabled', emailVerificationEnabled ? '1' : '0');
    formData.set('login_notification_enabled', loginNotificationEnabled ? '1' : '0');
    
    console.log('📧 提交SMTP配置:', {
        email_verification_enabled: emailVerificationEnabled,
        login_notification_enabled: loginNotificationEnabled,
        hasInitialSmtpConfig: hasInitialSmtpConfig
    });
    
    // 如果有现有配置，需要验证码
    if (hasInitialSmtpConfig) {
        const emailCode = document.getElementById('smtp_email_code').value;
        if (!emailCode) {
            showToast('请输入邮箱验证码', 'error');
            return;
        }
        formData.append('verification_code', emailCode);
    }
    
    // 显示加载状态
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    
    fetch('../api/ajax_handler.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON格式的响应');
        }
        return response.json();
    })
    .then(data => {
        console.log('📧 SMTP配置保存结果:', data);
        if (data.success) {
            showToast(data.message, 'success');
            // 更新配置状态
            if (data.has_existing_config) {
                hasInitialSmtpConfig = true;
                checkSmtpConfigChange(); // 重新检查显示状态
            }
            // 清空验证码输入
            const codeInput = document.getElementById('smtp_email_code');
            if (codeInput) codeInput.value = '';
            // 启用测试邮件按钮
            document.getElementById('testEmailBtn').disabled = false;
        } else {
            showToast(data.message || '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('操作失败，请检查网络连接', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

function detectDevice() {
    console.log('🔍 开始检测设备信息...');
    
    try {
        // 等待DOM完全准备好
        if (document.readyState !== 'complete') {
            console.log('⏳ DOM未完全加载，延迟100ms重试...');
            setTimeout(() => detectDevice(), 100);
            return;
        }
        
        // 检查关键元素是否存在
        const requiredElements = ['deviceType', 'browserInfo', 'osInfo', 'deviceFingerprint'];
        const missingElements = requiredElements.filter(id => !document.getElementById(id));
        
        if (missingElements.length > 0) {
            console.error('❌ 缺少必要的DOM元素:', missingElements);
            setTimeout(() => detectDevice(), 500);
            return;
        }
        
        // 检测设备类型
        const deviceType = /Mobi|Android/i.test(navigator.userAgent) ? '移动设备' : '桌面设备';
        const deviceTypeElement = document.getElementById('deviceType');
        if (deviceTypeElement) {
            deviceTypeElement.textContent = deviceType;
            deviceTypeElement.style.color = '';
            console.log('✅ 设备类型检测完成:', deviceType);
        }
        
        // 检测浏览器信息
        const browserInfo = getBrowserInfo();
        const browserInfoElement = document.getElementById('browserInfo');
        if (browserInfoElement) {
            browserInfoElement.textContent = browserInfo;
            browserInfoElement.style.color = '';
            console.log('✅ 浏览器信息检测完成:', browserInfo);
        }
        
        // 检测操作系统
        const osInfo = getOSInfo();
        const osInfoElement = document.getElementById('osInfo');
        if (osInfoElement) {
            osInfoElement.textContent = osInfo;
            osInfoElement.style.color = '';
            console.log('✅ 操作系统检测完成:', osInfo);
        }
        
        // 生成设备指纹
        const fingerprint = generateDeviceFingerprint();
        const fingerprintElement = document.getElementById('deviceFingerprint');
        if (fingerprintElement) {
            fingerprintElement.textContent = fingerprint.substring(0, 16) + '...';
            fingerprintElement.style.color = '';
            console.log('✅ 设备指纹生成完成:', fingerprint.substring(0, 16) + '...');
        }
        
        console.log('🎉 设备信息检测完成');
    } catch (error) {
        console.error('❌ 设备检测过程中发生错误:', error);
        
        // 如果检测失败，显示错误信息
        const elements = ['deviceType', 'browserInfo', 'osInfo', 'deviceFingerprint'];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element && (element.textContent.includes('正在检测') || element.textContent.includes('正在生成'))) {
                element.textContent = '检测失败';
                element.style.color = '#ff6b6b';
            }
        });
    }
}

function getBrowserInfo() {
    try {
        const ua = navigator.userAgent;
        if (ua.includes('Chrome') && !ua.includes('Edge')) {
            const match = ua.match(/Chrome\/(\d+)/);
            return match ? `Chrome ${match[1]}` : 'Chrome';
        }
        if (ua.includes('Firefox')) {
            const match = ua.match(/Firefox\/(\d+)/);
            return match ? `Firefox ${match[1]}` : 'Firefox';
        }
        if (ua.includes('Safari') && !ua.includes('Chrome')) {
            const match = ua.match(/Version\/(\d+)/);
            return match ? `Safari ${match[1]}` : 'Safari';
        }
        if (ua.includes('Edge')) {
            const match = ua.match(/Edge\/(\d+)/);
            return match ? `Edge ${match[1]}` : 'Edge';
        }
        return '未知浏览器';
    } catch (error) {
        console.error('浏览器信息检测失败:', error);
        return '检测失败';
    }
}

function getOSInfo() {
    try {
        const ua = navigator.userAgent;
        if (ua.includes('Windows NT 10.0')) return 'Windows 10/11';
        if (ua.includes('Windows NT 6.3')) return 'Windows 8.1';
        if (ua.includes('Windows NT 6.2')) return 'Windows 8';
        if (ua.includes('Windows NT 6.1')) return 'Windows 7';
        if (ua.includes('Windows')) return 'Windows';
        if (ua.includes('Mac OS X')) {
            const match = ua.match(/Mac OS X (\d+_\d+)/);
            return match ? `macOS ${match[1].replace('_', '.')}` : 'macOS';
        }
        if (ua.includes('Linux')) return 'Linux';
        if (ua.includes('Android')) {
            const match = ua.match(/Android (\d+\.?\d*)/);
            return match ? `Android ${match[1]}` : 'Android';
        }
        if (ua.includes('iPhone') || ua.includes('iPad')) {
            const match = ua.match(/OS (\d+_\d+)/);
            return match ? `iOS ${match[1].replace('_', '.')}` : 'iOS';
        }
        return '未知系统';
    } catch (error) {
        console.error('操作系统信息检测失败:', error);
        return '检测失败';
    }
}

function generateDeviceFingerprint() {
    try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
        
        const fingerprint = [
            navigator.userAgent,
            navigator.language || 'unknown',
            screen.width + 'x' + screen.height,
            new Date().getTimezoneOffset(),
            canvas.toDataURL()
        ].join('|');
        
        // 简单哈希
        let hash = 0;
        for (let i = 0; i < fingerprint.length; i++) {
            const char = fingerprint.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16);
    } catch (error) {
        console.error('设备指纹生成失败:', error);
        return 'error_' + Date.now().toString(16);
    }
}

function trustCurrentDevice() {
    showPasswordModal();
}

function showPasswordModal() {
    const modal = document.createElement('div');
    modal.className = 'password-modal';
    modal.innerHTML = `
        <div class="password-modal-content">
            <h3><i class="fas fa-shield-check"></i> 验证设备信任</h3>
            <div class="form-group">
                <label>请输入邮箱验证码以验证身份：</label>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="text" id="verifyEmailCode" placeholder="输入6位验证码" maxlength="6" autofocus>
                    <button type="button" class="btn btn-secondary btn-sm" id="sendCodeBtn" onclick="sendTrustDeviceCode()">
                        <i class="fas fa-paper-plane"></i> 发送验证码
                    </button>
                </div>
                <small style="color: #999; margin-top: 5px; display: block;">验证码有效期20秒</small>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-primary" onclick="verifyEmailCodeAndTrust()">
                    <i class="fas fa-check"></i> 确认信任
                </button>
                <button type="button" class="btn btn-secondary" onclick="closePasswordModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    // 减少延迟，立即显示
    requestAnimationFrame(() => {
        modal.classList.add('active');
        document.getElementById('sendCodeBtn').focus();
    });
    
    // 回车键确认
    document.getElementById('verifyEmailCode').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifyEmailCodeAndTrust();
        }
    });
}

function closePasswordModal() {
    const modal = document.querySelector('.password-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => modal.remove(), 300);
    }
}

function sendTrustDeviceCode() {
    const btn = document.getElementById('sendCodeBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
    
    const formData = new FormData();
    formData.append('action', 'send_trust_device_code');
    
    fetch('../api/ajax_handler.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON格式的响应');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            // 开始倒计时
            startCountdown(btn, 20);
            document.getElementById('verifyEmailCode').focus();
        } else {
            showToast(data.message || '发送失败', 'error');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送验证码';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('操作失败，请检查网络连接', 'error');
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送验证码';
    });
}

function startCountdown(btn, seconds) {
    let remaining = seconds;
    const timer = setInterval(() => {
        btn.innerHTML = `<i class="fas fa-clock"></i> ${remaining}秒后重发`;
        remaining--;
        
        if (remaining < 0) {
            clearInterval(timer);
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-paper-plane"></i> 重新发送';
        }
    }, 1000);
}

function verifyEmailCodeAndTrust() {
    const emailCode = document.getElementById('verifyEmailCode').value;
    if (!emailCode) {
        showToast('请输入验证码', 'error');
        return;
    }
    
    // 禁用按钮防止重复点击
    const btn = document.querySelector('.password-modal .btn-primary');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';
    
    // 发送AJAX请求验证验证码并添加信任设备
    const formData = new FormData();
    formData.append('action', 'trust_device');
    formData.append('email_code', emailCode);
    formData.append('device_info', JSON.stringify({
        name: getDeviceName(),
        fingerprint: generateDeviceFingerprint(),
        user_agent: navigator.userAgent,
        screen_resolution: screen.width + 'x' + screen.height
    }));
    
    fetch('../api/ajax_handler.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON格式的响应');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            closePasswordModal();
            showToast('设备已成功添加到信任列表', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showToast(data.message || '验证码验证失败', 'error');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check"></i> 确认信任';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('操作失败，请检查网络连接', 'error');
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-check"></i> 确认信任';
    });
}

function getDeviceName() {
    const os = getOSInfo();
    const browser = getBrowserInfo();
    const deviceType = /Mobi|Android/i.test(navigator.userAgent) ? '移动设备' : '桌面设备';
    return `${os} - ${browser} (${deviceType})`;
}

function removeDevice(deviceId) {
    if (confirm('确定要移除这个信任设备吗？')) {
        const formData = new FormData();
        formData.append('action', 'remove_device');
        formData.append('device_id', deviceId);
        
        fetch('../api/ajax_handler.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('服务器返回了非JSON格式的响应');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showToast('设备已移除', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || '操作失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('操作失败，请检查网络连接', 'error');
        });
    }
}

function updateAdminEmail() {
    const emailInput = document.querySelector('input[name="admin_email"]');
    const newEmail = emailInput.value.trim();
    
    if (!newEmail) {
        showToast('请输入邮箱地址', 'error');
        return;
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
        showToast('邮箱格式不正确', 'error');
        return;
    }
    
    if (!confirm('确定要更新管理员邮箱为：' + newEmail + ' 吗？')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('email', newEmail);
    
    fetch('../api/update_admin_email.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('管理员邮箱更新成功', 'success');
        } else {
            showToast(data.message || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('更新失败，请检查网络连接', 'error');
    });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    const colors = {
        'success': 'rgba(40, 167, 69, 0.9)',
        'error': 'rgba(220, 53, 69, 0.9)',
        'info': 'rgba(0, 123, 255, 0.9)'
    };
    
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type] || colors.info};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10001;
        font-size: 14px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // 动画显示
    requestAnimationFrame(() => {
        toast.style.transform = 'translateX(0)';
    });
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// 检查SMTP配置变化
function checkSmtpConfigChange() {
    const form = document.getElementById('smtpForm');
    const verificationSection = document.getElementById('emailVerificationSection');
    
    if (!form || !verificationSection) {
        console.error('❌ 找不到SMTP表单或验证码区域');
        return;
    }
    
    console.log('🔍 检查SMTP配置变化，hasInitialSmtpConfig:', hasInitialSmtpConfig);
    
    // 如果有初始配置，任何保存操作都需要验证码
    if (hasInitialSmtpConfig) {
        verificationSection.style.display = 'block';
        console.log('✅ 显示验证码区域（有现有配置）');
        
        // 确保验证码输入框可见和必填
        const codeInput = document.getElementById('smtp_email_code');
        const sendBtn = document.getElementById('sendSmtpCodeBtn');
        if (codeInput) {
            codeInput.required = true;
            codeInput.style.display = 'block';
        }
        if (sendBtn) {
            sendBtn.style.display = 'inline-block';
        }
        
        // 添加提示文本
        const hintElement = verificationSection.querySelector('.verification-hint');
        if (!hintElement) {
            const hint = document.createElement('small');
            hint.className = 'verification-hint';
            hint.style.cssText = 'color: #666; display: block; margin-top: 5px;';
            hint.textContent = '修改邮箱配置需要验证原邮箱身份';
            verificationSection.appendChild(hint);
        }
        
        return;
    }
    
    // 如果没有初始配置，则隐藏验证码区域（首次配置不需要验证码）
    verificationSection.style.display = 'none';
    console.log('✅ 隐藏验证码区域（首次配置）');
    
    // 清空验证码输入框
    const codeInput = document.getElementById('smtp_email_code');
    if (codeInput) {
        codeInput.value = '';
        codeInput.required = false;
    }
    
    // 移除提示文本
    const hintElement = verificationSection.querySelector('.verification-hint');
    if (hintElement) {
        hintElement.remove();
    }
}

// 发送SMTP配置验证码
function sendSmtpConfigCode() {
    const btn = document.getElementById('sendSmtpCodeBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
    
    const formData = new FormData();
    formData.append('action', 'send_smtp_config_code');
    
    fetch('../api/ajax_handler.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON格式的响应');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            // 开始倒计时
            startCountdown(btn, 20);
            document.getElementById('smtp_email_code').focus();
        } else {
            showToast(data.message || '发送失败', 'error');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送验证码';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('操作失败，请检查网络连接', 'error');
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送验证码';
    });
}

// 初始化邮件测试按钮
function initEmailTestButton() {
    const smtpHost = document.querySelector('input[name="smtp_host"]');
    const smtpPort = document.querySelector('input[name="smtp_port"]');
    const smtpUsername = document.querySelector('input[name="smtp_username"]');
    const smtpPassword = document.querySelector('input[name="smtp_password"]');
    const testBtn = document.getElementById('testEmailBtn');
    
    if (!testBtn) return;
    
    function checkEmailConfig() {
        const isConfigComplete = smtpHost.value.trim() && 
                                smtpPort.value.trim() && 
                                smtpUsername.value.trim() && 
                                smtpPassword.value.trim();
        
        testBtn.disabled = !isConfigComplete;
        if (isConfigComplete) {
            testBtn.classList.remove('btn-secondary');
            testBtn.classList.add('btn-success');
        } else {
            testBtn.classList.remove('btn-success');
            testBtn.classList.add('btn-secondary');
        }
    }
    
    // 监听输入变化
    [smtpHost, smtpPort, smtpUsername, smtpPassword].forEach(input => {
        if (input) {
            input.addEventListener('input', checkEmailConfig);
        }
    });
    
    // 初始检查
    checkEmailConfig();
}

// 测试邮件设置
function testEmailSettings() {
    const smtpHost = document.querySelector('input[name="smtp_host"]').value.trim();
    const smtpPort = document.querySelector('input[name="smtp_port"]').value.trim();
    const smtpUsername = document.querySelector('input[name="smtp_username"]').value.trim();
    const smtpPassword = document.querySelector('input[name="smtp_password"]').value.trim();
    
    if (!smtpHost || !smtpPort || !smtpUsername || !smtpPassword) {
        showToast('请先完整配置SMTP设置', 'error');
        return;
    }
    
    // 验证端口号
    const port = parseInt(smtpPort);
    if (isNaN(port) || port <= 0 || port > 65535) {
        showToast('请输入有效的端口号 (1-65535)', 'error');
        return;
    }
    
    // 弹出邮箱输入框
    const email = prompt('请输入测试邮箱地址：', smtpUsername);
    if (!email) return;
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showToast('邮箱格式不正确', 'error');
        return;
    }
    
    const testBtn = document.getElementById('testEmailBtn');
    const originalText = testBtn.innerHTML;
    testBtn.disabled = true;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
    
    // 发送测试邮件
    const formData = new FormData();
    formData.append('action', 'test_email');
    formData.append('test_email', email);
    formData.append('smtp_host', smtpHost);
    formData.append('smtp_port', smtpPort);
    formData.append('smtp_username', smtpUsername);
    formData.append('smtp_password', smtpPassword);
    
    fetch('../api/ajax_handler.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON格式的响应');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
        } else {
            let errorMessage = data.message || '测试邮件发送失败';
            if (data.details) {
                errorMessage += '\n\n建议：' + data.details;
            }
            if (data.technical_error) {
                console.error('技术错误信息:', data.technical_error);
            }
            showDetailedErrorToast(errorMessage);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('发送失败，请检查网络连接', 'error');
    })
    .finally(() => {
        testBtn.disabled = false;
        testBtn.innerHTML = originalText;
    });
}

// 显示详细错误信息的Toast
function showDetailedErrorToast(message) {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(220, 53, 69, 0.95);
        color: white;
        padding: 20px;
        border-radius: 12px;
        z-index: 10001;
        font-size: 14px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
        word-wrap: break-word;
        white-space: pre-line;
        line-height: 1.5;
    `;
    
    // 添加关闭按钮
    toast.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
            <strong style="color: #fff;">邮件发送失败</strong>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; margin-left: 10px;">&times;</button>
        </div>
        <div>${message}</div>
    `;
    
    document.body.appendChild(toast);
    
    // 动画显示
    requestAnimationFrame(() => {
        toast.style.transform = 'translateX(0)';
    });
    
    // 10秒后自动隐藏
    setTimeout(() => {
        if (toast.parentElement) {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }
    }, 10000);
}
</script> 