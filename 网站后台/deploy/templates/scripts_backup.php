<?php
$message = '';
$loader_code = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_script'])) {
        $script_id = $_POST['script_id'] ?? null;
        $name = trim($_POST['name']);
        $version = trim($_POST['version']);
        $code = $_POST['script_code']; // 不做trim，保留原始格式
        $loader_code = $_POST['loader_code'] ?? null; // 加载器代码

        if (empty($name) || empty($version) || empty($code)) {
            $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 脚本名称、版本和代码都不能为空</div>";
        } else {
            if ($script_id) { // 更新
                $stmt = $pdo->prepare("UPDATE scripts SET name = ?, version = ?, script_code = ?, loader_code = ? WHERE id = ?");
                if ($stmt->execute([$name, $version, $code, $loader_code, $script_id])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功更新！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 更新脚本失败</div>";
                }
            } else { // 新增
                $stmt = $pdo->prepare("INSERT INTO scripts (name, version, script_code, loader_code) VALUES (?, ?, ?, ?)");
                if ($stmt->execute([$name, $version, $code, $loader_code])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已成功添加！</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 添加脚本失败</div>";
                }
            }
        }
    }
    
    // 智能转换并保存
    if (isset($_POST['convert_and_save'])) {
        $full_script_with_header = $_POST['full_script_with_header'];
        
        // 1. 提取 UserScript 头部
        preg_match('/(\/\/ ==UserScript==.*?\/\/ ==\/UserScript==)/s', $full_script_with_header, $matches);
        if (!empty($matches[1])) {
            $userscript_header = $matches[1];
            
            // 2. 提取脚本信息
            $script_name = '';
            $script_version = '';
            
            if (preg_match('/\/\/ @name\s+(.+)/i', $userscript_header, $name_matches)) {
                $script_name = trim($name_matches[1]);
            }
            
            if (preg_match('/\/\/ @version\s+(.+)/i', $userscript_header, $version_matches)) {
                $script_version = trim($version_matches[1]);
            }
            
            // 3. 移除UserScript头部，得到纯JS代码
            $pure_js = preg_replace('/(\/\/ ==UserScript==.*?\/\/ ==\/UserScript==)\s*/s', '', $full_script_with_header);
            $pure_js = trim($pure_js);
            
            // 4. 确保 @connect 指向你的域名
            if (strpos($userscript_header, '@connect      xiaomeihuakefu.cn') === false) {
                $userscript_header = str_replace(
                    "// ==/UserScript==",
                    "// @connect      xiaomeihuakefu.cn\n// ==/UserScript==",
                    $userscript_header
                );
            }
            
            // 5. 生成加载器代码 (复用现有的JS代码)
            $loader_js = <<<'JS'
(function() {
    'use strict';
    
    let isScriptLoaded = false;
    let currentKey = null;
    let controlPanel = null;

    // 创建UI
    function createUI() {
        if (document.getElementById('auth-panel')) return; // 防止重复创建

        const panel = document.createElement('div');
        panel.id = 'auth-panel';
        panel.innerHTML = `
            <div class="auth-header">
                小梅花AI客服助手 v` + GM_info.script.version + `
                <button id="close-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; width: 20px; height: 20px;">×</button>
            </div>
            <div class="auth-body">
                <p>请输入卡密以解锁完整功能:</p>
                <input type="text" id="license-key-input" placeholder="在此输入卡密">
                <button id="verify-key-btn">验证</button>
                <div id="auth-message"></div>
            </div>
        `;
        document.body.appendChild(panel);

        GM_addStyle(`
            #auth-panel { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99999;
                         background: rgba(255,255,255,0.95); border: 1px solid #ddd; border-radius: 15px; 
                         box-shadow: 0 10px 30px rgba(0,0,0,0.3); width: 380px; font-family: sans-serif; }
            #auth-panel .auth-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; 
                                      padding: 20px; border-top-left-radius: 15px; border-top-right-radius: 15px; 
                                      font-size: 18px; text-align: center; font-weight: bold; position: relative; }
            #auth-panel .auth-body { padding: 25px; }
            #auth-panel p { margin-bottom: 15px; color: #333; font-size: 14px; }
            #auth-panel input { width: 100%; padding: 12px; box-sizing: border-box; margin-bottom: 15px; 
                               border-radius: 8px; border: 2px solid #e1e5e9; font-size: 14px; }
            #auth-panel input:focus { outline: none; border-color: #667eea; }
            #auth-panel button { width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea, #764ba2); 
                                color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; 
                                font-weight: bold; transition: all 0.3s ease; }
            #auth-panel button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102,126,234,0.4); }
            #auth-message { margin-top: 15px; text-align: center; font-weight: bold; }
            .success { color: #28a745; }
            .error { color: #dc3545; }
            #control-panel { position: fixed; top: 20px; right: 20px; z-index: 99998; background: rgba(255,255,255,0.95); 
                           border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.2); 
                           width: 300px; font-family: sans-serif; }
            #control-panel .panel-header { background: linear-gradient(135deg, #28a745, #20c997); color: white; 
                                         padding: 15px; border-top-left-radius: 10px; border-top-right-radius: 10px; 
                                         font-size: 16px; font-weight: bold; position: relative; }
            #control-panel .panel-body { padding: 20px; }
            #control-panel .status-item { margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; }
            #control-panel .status-label { font-weight: bold; color: #495057; }
            #control-panel .status-value { color: #28a745; }
        `);
        
        const verifyBtn = document.getElementById('verify-key-btn');
        const inputField = document.getElementById('license-key-input');
        const closeBtn = document.getElementById('close-panel-btn');
        
        if (verifyBtn) {
            verifyBtn.addEventListener('click', verifyKey);
        }
        
        if (inputField) {
            inputField.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    verifyKey();
                }
            });
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                const panel = document.getElementById('auth-panel');
                if (panel) {
                    panel.remove();
                }
            });
        }
    }
    
    // 创建控制面板
    function createControlPanel() {
        if (document.getElementById('control-panel')) return;
        
        controlPanel = document.createElement('div');
        controlPanel.id = 'control-panel';
        controlPanel.innerHTML = `
            <div class="panel-header">
                脚本控制面板
                <button id="hide-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 0; width: 18px; height: 18px;">×</button>
            </div>
            <div class="panel-body">
                <div class="status-item">
                    <div class="status-label">状态:</div>
                    <div class="status-value">已激活</div>
                </div>
                <div class="status-item">
                    <div class="status-label">卡密:</div>
                    <div class="status-value">${currentKey ? currentKey.substring(0, 8) + '...' : '未知'}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">版本:</div>
                    <div class="status-value">v` + GM_info.script.version + `</div>
                </div>
                <button id="logout-btn" style="width: 100%; padding: 8px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px;">退出登录</button>
            </div>
        `;
        document.body.appendChild(controlPanel);
        
        // 绑定事件
        const hideBtn = document.getElementById('hide-panel-btn');
        const logoutBtn = document.getElementById('logout-btn');
        
        if (hideBtn) {
            hideBtn.addEventListener('click', function() {
                controlPanel.style.display = 'none';
            });
        }
        
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                if (confirm('确定要退出登录吗？')) {
                    GM_deleteValue('saved_license_key_xiaomeihua');
                    controlPanel.remove();
                    location.reload();
                }
            });
        }
        
        // 双击显示隐藏的面板
        document.addEventListener('dblclick', function(e) {
            if (e.ctrlKey && controlPanel && controlPanel.style.display === 'none') {
                controlPanel.style.display = 'block';
            }
        });
    }
    
    // 验证逻辑
    function verifyKey() {
        const inputField = document.getElementById('license-key-input');
        const messageDiv = document.getElementById('auth-message');
        
        if (!inputField || !messageDiv) {
            console.error('UI elements not found');
            return;
        }
        
        const key = inputField.value.trim();
        
        if (!key) {
            messageDiv.innerHTML = '<span class="error">卡密不能为空！</span>';
            return;
        }
        
        messageDiv.innerHTML = '<span style="color: #007bff;">正在验证...</span>';
        currentKey = key;
        
        GM_xmlhttpRequest({
            method: 'POST',
            url: 'https://xiaomeihuakefu.cn/api/verify.php',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            data: 'key=' + encodeURIComponent(key),
            onload: function(response) {
                console.log('API Response:', response.responseText);
                try {
                    const data = JSON.parse(response.responseText);
                    if (data.success) {
                        messageDiv.innerHTML = '<span class="success">验证成功，正在加载脚本...</span>';
                        
                        // 保存卡密
                        GM_setValue('saved_license_key_xiaomeihua', key);
                        
                        // 延迟执行脚本，确保UI更新
                        setTimeout(() => {
                            try {
                                // 移除验证面板
                                const panel = document.getElementById('auth-panel');
                                if (panel) {
                                    panel.remove();
                                }
                                
                                // 执行完整版脚本
                                console.log('Loading script...');
                                const scriptFunction = new Function(data.script);
                                scriptFunction();
                                isScriptLoaded = true;
                                
                                console.log('Script loaded successfully');
                                
                                // 创建控制面板
                                createControlPanel();
                                
                                // 启动心跳
                                startHeartbeat(key);
                                
                            } catch (scriptError) {
                                console.error('Script execution error:', scriptError);
                                messageDiv.innerHTML = '<span class="error">脚本执行失败: ' + scriptError.message + '</span>';
                            }
                        }, 1500);
                        
                    } else {
                        messageDiv.innerHTML = '<span class="error">错误: ' + data.message + '</span>';
                        GM_deleteValue('saved_license_key_xiaomeihua');
                    }
                } catch (e) {
                    console.error('JSON Parse Error:', e, 'Response:', response.responseText);
                    messageDiv.innerHTML = '<span class="error">验证服务器响应格式错误</span>';
                }
            },
            onerror: function(err) {
                console.error('GM_xmlhttpRequest Error:', err);
                messageDiv.innerHTML = '<span class="error">无法连接到验证服务器，请检查网络</span>';
            }
        });
    }
    
    // 启动心跳
    function startHeartbeat(key) {
        if (!key) return;
        
        const sendHeartbeat = () => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://xiaomeihuakefu.cn/api/heartbeat.php',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                data: 'key=' + encodeURIComponent(key),
                onload: function(response) {
                    console.log('Heartbeat sent successfully');
                },
                onerror: function(err) {
                    console.error('Heartbeat error:', err);
                }
            });
        };
        
        // 立即发送一次心跳
        sendHeartbeat();
        
        // 每3分钟发送一次心跳
        setInterval(sendHeartbeat, 3 * 60 * 1000);
    }

    // 页面加载后执行
    function main() {
        console.log('Script loader starting...');
        
        // 尝试自动验证
        const savedKey = GM_getValue('saved_license_key_xiaomeihua', null);
        if (savedKey) {
            console.log('Found saved key, attempting auto-verification...');
            
            // 创建临时UI元素进行自动验证
            const tempDiv = document.createElement('div');
            tempDiv.style.display = 'none';
            tempDiv.innerHTML = '<input id="license-key-input" value="' + savedKey + '"><div id="auth-message"></div>';
            document.body.appendChild(tempDiv);
            
            // 延迟执行验证，确保元素已添加到DOM
            setTimeout(() => {
                verifyKey();
            }, 100);
        } else {
            console.log('No saved key found, showing UI...');
            createUI();
        }
    }
    
    // 确保在页面加载完成后执行
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        main();
    } else {
        window.addEventListener('DOMContentLoaded', main);
        window.addEventListener('load', main);
    }
})();
JS;
            
            $loader_code = $userscript_header . "\n\n" . $loader_js;
            
            // 6. 保存脚本到数据库
            if (!empty($script_name) && !empty($script_version) && !empty($pure_js)) {
                $stmt = $pdo->prepare("INSERT INTO scripts (name, version, script_code, loader_code) VALUES (?, ?, ?, ?)");
                if ($stmt->execute([$script_name, $script_version, $pure_js, $loader_code])) {
                    $message = "<div class='message success'><i class='fas fa-check-circle'></i> 智能转换成功！脚本「{$script_name} v{$script_version}」和加载器已保存到系统中</div>";
                } else {
                    $message = "<div class='message error'><i class='fas fa-times-circle'></i> 保存失败，请检查数据库连接</div>";
                }
            } else {
                $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 无法提取脚本信息，请确保UserScript头部包含@name和@version</div>";
            }
        } else {
            $message = "<div class='message error'><i class='fas fa-times-circle'></i> 转换失败，未找到有效的 UserScript 头部</div>";
        }
    }

    // 加载器转换
    if (isset($_POST['convert_loader'])) {
        $full_script_with_header = $_POST['full_script_with_header'];
        
        // 1. 提取 UserScript 头部
        preg_match('/(\/\/ ==UserScript==.*?\/\/ ==\/UserScript==)/s', $full_script_with_header, $matches);
        if (!empty($matches[1])) {
            $userscript_header = $matches[1];
            
            // 2. 确保 @connect 指向你的域名
            if (strpos($userscript_header, '@connect      xiaomeihuakefu.cn') === false) {
                $userscript_header = str_replace(
                    "// ==/UserScript==",
                    "// @connect      xiaomeihuakefu.cn\n// ==/UserScript==",
                    $userscript_header
                );
            }
            
            // 3. 生成加载器JS代码
            $loader_js = <<<'JS'
(function() {
    'use strict';
    
    let isScriptLoaded = false;
    let currentKey = null;
    let controlPanel = null;

    // 创建UI
    function createUI() {
        if (document.getElementById('auth-panel')) return; // 防止重复创建

        const panel = document.createElement('div');
        panel.id = 'auth-panel';
        panel.innerHTML = `
            <div class="auth-header">
                小梅花AI客服助手 v` + GM_info.script.version + `
                <button id="close-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 18px; cursor: pointer; padding: 0; width: 20px; height: 20px;">×</button>
            </div>
            <div class="auth-body">
                <p>请输入卡密以解锁完整功能:</p>
                <input type="text" id="license-key-input" placeholder="在此输入卡密">
                <button id="verify-key-btn">验证</button>
                <div id="auth-message"></div>
            </div>
        `;
        document.body.appendChild(panel);

        GM_addStyle(`
            #auth-panel { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 99999;
                         background: rgba(255,255,255,0.95); border: 1px solid #ddd; border-radius: 15px; 
                         box-shadow: 0 10px 30px rgba(0,0,0,0.3); width: 380px; font-family: sans-serif; }
            #auth-panel .auth-header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; 
                                      padding: 20px; border-top-left-radius: 15px; border-top-right-radius: 15px; 
                                      font-size: 18px; text-align: center; font-weight: bold; position: relative; }
            #auth-panel .auth-body { padding: 25px; }
            #auth-panel p { margin-bottom: 15px; color: #333; font-size: 14px; }
            #auth-panel input { width: 100%; padding: 12px; box-sizing: border-box; margin-bottom: 15px; 
                               border-radius: 8px; border: 2px solid #e1e5e9; font-size: 14px; }
            #auth-panel input:focus { outline: none; border-color: #667eea; }
            #auth-panel button { width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea, #764ba2); 
                                color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; 
                                font-weight: bold; transition: all 0.3s ease; }
            #auth-panel button:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102,126,234,0.4); }
            #auth-message { margin-top: 15px; text-align: center; font-weight: bold; }
            .success { color: #28a745; }
            .error { color: #dc3545; }
            #control-panel { position: fixed; top: 20px; right: 20px; z-index: 99998; background: rgba(255,255,255,0.95); 
                           border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.2); 
                           width: 300px; font-family: sans-serif; }
            #control-panel .panel-header { background: linear-gradient(135deg, #28a745, #20c997); color: white; 
                                         padding: 15px; border-top-left-radius: 10px; border-top-right-radius: 10px; 
                                         font-size: 16px; font-weight: bold; position: relative; }
            #control-panel .panel-body { padding: 20px; }
            #control-panel .status-item { margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 5px; }
            #control-panel .status-label { font-weight: bold; color: #495057; }
            #control-panel .status-value { color: #28a745; }
        `);
        
        const verifyBtn = document.getElementById('verify-key-btn');
        const inputField = document.getElementById('license-key-input');
        const closeBtn = document.getElementById('close-panel-btn');
        
        if (verifyBtn) {
            verifyBtn.addEventListener('click', verifyKey);
        }
        
        if (inputField) {
            inputField.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    verifyKey();
                }
            });
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                const panel = document.getElementById('auth-panel');
                if (panel) {
                    panel.remove();
                }
            });
        }
    }
    
    // 创建控制面板
    function createControlPanel() {
        if (document.getElementById('control-panel')) return;
        
        controlPanel = document.createElement('div');
        controlPanel.id = 'control-panel';
        controlPanel.innerHTML = `
            <div class="panel-header">
                脚本控制面板
                <button id="hide-panel-btn" style="float: right; background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 0; width: 18px; height: 18px;">×</button>
            </div>
            <div class="panel-body">
                <div class="status-item">
                    <div class="status-label">状态:</div>
                    <div class="status-value">已激活</div>
                </div>
                <div class="status-item">
                    <div class="status-label">卡密:</div>
                    <div class="status-value">${currentKey ? currentKey.substring(0, 8) + '...' : '未知'}</div>
                </div>
                <div class="status-item">
                    <div class="status-label">版本:</div>
                    <div class="status-value">v` + GM_info.script.version + `</div>
                </div>
                <button id="logout-btn" style="width: 100%; padding: 8px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px;">退出登录</button>
            </div>
        `;
        document.body.appendChild(controlPanel);
        
        // 绑定事件
        const hideBtn = document.getElementById('hide-panel-btn');
        const logoutBtn = document.getElementById('logout-btn');
        
        if (hideBtn) {
            hideBtn.addEventListener('click', function() {
                controlPanel.style.display = 'none';
            });
        }
        
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                if (confirm('确定要退出登录吗？')) {
                    GM_deleteValue('saved_license_key_xiaomeihua');
                    controlPanel.remove();
                    location.reload();
                }
            });
        }
        
        // 双击显示隐藏的面板
        document.addEventListener('dblclick', function(e) {
            if (e.ctrlKey && controlPanel && controlPanel.style.display === 'none') {
                controlPanel.style.display = 'block';
            }
        });
    }
    
    // 验证逻辑
    function verifyKey() {
        const inputField = document.getElementById('license-key-input');
        const messageDiv = document.getElementById('auth-message');
        
        if (!inputField || !messageDiv) {
            console.error('UI elements not found');
            return;
        }
        
        const key = inputField.value.trim();
        
        if (!key) {
            messageDiv.innerHTML = '<span class="error">卡密不能为空！</span>';
            return;
        }
        
        messageDiv.innerHTML = '<span style="color: #007bff;">正在验证...</span>';
        currentKey = key;
        
        GM_xmlhttpRequest({
            method: 'POST',
            url: 'https://xiaomeihuakefu.cn/api/verify.php',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            data: 'key=' + encodeURIComponent(key),
            onload: function(response) {
                console.log('API Response:', response.responseText);
                try {
                    const data = JSON.parse(response.responseText);
                    if (data.success) {
                        messageDiv.innerHTML = '<span class="success">验证成功，正在加载脚本...</span>';
                        
                        // 保存卡密
                        GM_setValue('saved_license_key_xiaomeihua', key);
                        
                        // 延迟执行脚本，确保UI更新
                        setTimeout(() => {
                            try {
                                // 移除验证面板
                                const panel = document.getElementById('auth-panel');
                                if (panel) {
                                    panel.remove();
                                }
                                
                                // 执行完整版脚本
                                console.log('Loading script...');
                                const scriptFunction = new Function(data.script);
                                scriptFunction();
                                isScriptLoaded = true;
                                
                                console.log('Script loaded successfully');
                                
                                // 创建控制面板
                                createControlPanel();
                                
                                // 启动心跳
                                startHeartbeat(key);
                                
                            } catch (scriptError) {
                                console.error('Script execution error:', scriptError);
                                messageDiv.innerHTML = '<span class="error">脚本执行失败: ' + scriptError.message + '</span>';
                            }
                        }, 1500);
                        
                    } else {
                        messageDiv.innerHTML = '<span class="error">错误: ' + data.message + '</span>';
                        GM_deleteValue('saved_license_key_xiaomeihua');
                    }
                } catch (e) {
                    console.error('JSON Parse Error:', e, 'Response:', response.responseText);
                    messageDiv.innerHTML = '<span class="error">验证服务器响应格式错误</span>';
                }
            },
            onerror: function(err) {
                console.error('GM_xmlhttpRequest Error:', err);
                messageDiv.innerHTML = '<span class="error">无法连接到验证服务器，请检查网络</span>';
            }
        });
    }
    
    // 启动心跳
    function startHeartbeat(key) {
        if (!key) return;
        
        const sendHeartbeat = () => {
            GM_xmlhttpRequest({
                method: 'POST',
                url: 'https://xiaomeihuakefu.cn/api/heartbeat.php',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                data: 'key=' + encodeURIComponent(key),
                onload: function(response) {
                    console.log('Heartbeat sent successfully');
                },
                onerror: function(err) {
                    console.error('Heartbeat error:', err);
                }
            });
        };
        
        // 立即发送一次心跳
        sendHeartbeat();
        
        // 每3分钟发送一次心跳
        setInterval(sendHeartbeat, 3 * 60 * 1000);
    }

    // 页面加载后执行
    function main() {
        console.log('Script loader starting...');
        
        // 尝试自动验证
        const savedKey = GM_getValue('saved_license_key_xiaomeihua', null);
        if (savedKey) {
            console.log('Found saved key, attempting auto-verification...');
            
            // 创建临时UI元素进行自动验证
            const tempDiv = document.createElement('div');
            tempDiv.style.display = 'none';
            tempDiv.innerHTML = '<input id="license-key-input" value="' + savedKey + '"><div id="auth-message"></div>';
            document.body.appendChild(tempDiv);
            
            // 延迟执行验证，确保元素已添加到DOM
            setTimeout(() => {
                verifyKey();
            }, 100);
        } else {
            console.log('No saved key found, showing UI...');
            createUI();
        }
    }
    
    // 确保在页面加载完成后执行
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        main();
    } else {
        window.addEventListener('DOMContentLoaded', main);
        window.addEventListener('load', main);
    }
})();
JS;

            $loader_code = $userscript_header . "\n\n" . $loader_js;
            $message = "<div class='message success'><i class='fas fa-check-circle'></i> 加载器代码已生成！请复制下方代码或保存到脚本中</div>";
        } else {
            $message = "<div class='message error'><i class='fas fa-times-circle'></i> 转换失败，未找到有效的 UserScript 头部。请确保你粘贴的是带头部的完整代码</div>";
        }
    }
}

// 删除脚本
if (isset($_GET['delete_script'])) {
    // 先检查是否有卡密关联
    $stmt_check = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE script_id = ?");
    $stmt_check->execute([$_GET['delete_script']]);
    if ($stmt_check->fetchColumn() > 0) {
        $message = "<div class='message error'><i class='fas fa-exclamation-triangle'></i> 无法删除，有卡密正关联此脚本</div>";
    } else {
        $stmt_delete = $pdo->prepare("DELETE FROM scripts WHERE id = ?");
        if ($stmt_delete->execute([$_GET['delete_script']])) {
            $message = "<div class='message success'><i class='fas fa-check-circle'></i> 脚本已删除！</div>";
        }
    }
}

// 获取要编辑的脚本
$edit_script = null;
if (isset($_GET['edit_script'])) {
    $stmt = $pdo->prepare("SELECT script_code FROM scripts WHERE id = ?");
    $stmt->execute([$_GET['edit_script']]);
    $edit_script = $stmt->fetch(PDO::FETCH_ASSOC);
}

// 获取脚本列表
$scripts_list = $pdo->query("SELECT id, name, version, script_code, loader_code, created_at FROM scripts ORDER BY id DESC")->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="card">
    <h2><i class="fas fa-magic"></i> 智能脚本转换器</h2>
    <p style="color: rgba(255,255,255,0.8); margin-bottom: 20px;">
        输入完整版脚本代码，自动提取脚本信息并生成加载器，一键保存脚本和加载器到系统中。
    </p>
    
    <?php echo $message; ?>
    
    <form method="POST" id="smart-converter-form">
        <div class="form-group">
            <label for="full_script_with_header">
                <i class="fas fa-file-code"></i> 完整版脚本代码 (带UserScript头部)
            </label>
            <textarea name="full_script_with_header" id="full_script_with_header" 
                      class="code-editor" rows="12" 
                      placeholder="在此粘贴你的完整版.user.js代码..."
                      oninput="extractScriptInfo()"></textarea>
        </div>
        
        <div class="form-row" style="display: flex; gap: 15px; margin-bottom: 15px;">
            <div class="form-group" style="flex: 1;">
                <label for="extracted-name">
                    <i class="fas fa-tag"></i> 脚本名称 (自动提取)
                </label>
                <input type="text" id="extracted-name" name="extracted_name" 
                       placeholder="将从@name自动提取" readonly 
                       style="background: rgba(255,255,255,0.1); color: #4ecdc4;">
            </div>
            <div class="form-group" style="flex: 1;">
                <label for="extracted-version">
                    <i class="fas fa-code-branch"></i> 版本号 (自动提取)
                </label>
                <input type="text" id="extracted-version" name="extracted_version" 
                       placeholder="将从@version自动提取" readonly
                       style="background: rgba(255,255,255,0.1); color: #4ecdc4;">
            </div>
        </div>
        
        <div style="display: flex; gap: 15px;">
            <button type="submit" name="convert_loader" class="btn btn-primary" style="flex: 1;">
                <i class="fas fa-cogs"></i> 仅生成加载器
            </button>
            <button type="submit" name="convert_and_save" class="btn btn-success" style="flex: 2; font-size: 16px;">
                <i class="fas fa-magic"></i> 智能转换并保存脚本
            </button>
        </div>
    </form>
    
    <?php if ($loader_code): ?>
    <div style="margin-top: 25px;">
        <h4 style="color: white; margin-bottom: 15px;">
            <i class="fas fa-download"></i> 生成的加载器代码
        </h4>
        <textarea readonly class="code-editor" rows="15" id="loader-code"><?php echo htmlspecialchars($loader_code); ?></textarea>
        <div style="margin-top: 10px; display: flex; gap: 10px;">
            <button onclick="copyLoaderCode()" class="btn btn-success">
                <i class="fas fa-copy"></i> 复制代码
            </button>
            <button onclick="saveLoaderToScript()" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存到脚本
            </button>
        </div>
    </div>
    <?php endif; ?>
</div>

<div class="card">
    <h2><i class="fas fa-code"></i> 脚本管理</h2>
    <p style="color: rgba(255,255,255,0.8); margin-bottom: 20px;">
        在这里添加或编辑你的<strong>完整版脚本</strong>（不包含 <code>==UserScript==</code> 头部）。卡密系统将从这里获取代码。
    </p>
    
    <form method="POST">
        <input type="hidden" name="script_id" value="<?php echo $edit_script['id'] ?? ''; ?>">
        
        <div class="form-inline">
            <div class="form-group">
                <label for="name"><i class="fas fa-tag"></i> 脚本名称</label>
                <input type="text" name="name" id="name" 
                       value="<?php echo htmlspecialchars($edit_script['name'] ?? ''); ?>" 
                       placeholder="例如：微信客服助手" required>
            </div>
            <div class="form-group">
                <label for="version"><i class="fas fa-code-branch"></i> 版本号</label>
                <input type="text" name="version" id="version" 
                       value="<?php echo htmlspecialchars($edit_script['version'] ?? ''); ?>" 
                       placeholder="例如：1.0.0" required>
            </div>
        </div>
        
        <div class="form-group">
            <label for="code">
                <i class="fas fa-file-code"></i> 完整版 JS 代码 (不带 UserScript 头部)
            </label>
            <textarea name="code" id="code" class="code-editor" rows="15" 
                      placeholder="从 (function() { ... })(); 开始粘贴你的脚本代码..." required><?php echo htmlspecialchars($edit_script['script_code'] ?? ''); ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="loader_code">
                <i class="fas fa-download"></i> 加载器代码 (可选)
            </label>
            <textarea name="loader_code" id="loader_code" class="code-editor" rows="10" 
                      placeholder="生成的加载器代码会自动填充到这里..."><?php echo htmlspecialchars($edit_script['loader_code'] ?? ''); ?></textarea>
        </div>
        
        <div style="display: flex; gap: 15px;">
            <button type="submit" name="save_script" class="btn btn-primary">
                <i class="fas fa-save"></i> <?php echo $edit_script ? '更新脚本' : '保存脚本'; ?>
            </button>
            <?php if ($edit_script): ?>
                <a href="index.php?page=scripts" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消编辑
                </a>
            <?php endif; ?>
        </div>
    </form>
</div>

<div class="card">
    <h2><i class="fas fa-list"></i> 已存脚本列表</h2>
    
    <?php if (empty($scripts_list)): ?>
        <p style="color: rgba(255,255,255,0.7); text-align: center; padding: 40px;">
            <i class="fas fa-info-circle"></i> 暂无脚本数据，请先添加脚本
        </p>
    <?php else: ?>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag"></i> ID</th>
                        <th><i class="fas fa-tag"></i> 名称</th>
                        <th><i class="fas fa-code-branch"></i> 版本</th>
                        <th><i class="fas fa-calendar"></i> 创建时间</th>
                        <th><i class="fas fa-key"></i> 关联卡密</th>
                        <th><i class="fas fa-cogs"></i> 操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($scripts_list as $script): ?>
                    <?php
                    // 获取关联的卡密数量
                    $stmt_count = $pdo->prepare("SELECT COUNT(*) FROM license_keys WHERE script_id = ?");
                    $stmt_count->execute([$script['id']]);
                    $linked_keys = $stmt_count->fetchColumn();
                    ?>
                    <tr>
                        <td><?php echo $script['id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($script['name']); ?></strong>
                        </td>
                        <td>
                            <span class="status-badge status-active">
                                v<?php echo htmlspecialchars($script['version']); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo date('Y-m-d H:i', strtotime($script['created_at'])); ?>
                        </td>
                        <td>
                            <?php if ($linked_keys > 0): ?>
                                <span style="color: #4ecdc4;">
                                    <i class="fas fa-link"></i> <?php echo $linked_keys; ?> 个卡密
                                </span>
                            <?php else: ?>
                                <span style="opacity: 0.5;">未关联</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                                <a href="index.php?page=scripts&edit_script=<?php echo $script['id']; ?>" 
                                   class="btn btn-secondary" 
                                   style="padding: 6px 12px; font-size: 12px;">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                <?php if (!empty($script['loader_code'])): ?>
                                    <button onclick="showLoaderCode(<?php echo $script['id']; ?>)" 
                                            class="btn btn-success" 
                                            style="padding: 6px 12px; font-size: 12px;">
                                        <i class="fas fa-download"></i> 加载器
                                    </button>
                                <?php endif; ?>
                                <?php if ($linked_keys == 0): ?>
                                    <a href="index.php?page=scripts&delete_script=<?php echo $script['id']; ?>" 
                                       class="btn btn-danger" 
                                       style="padding: 6px 12px; font-size: 12px;"
                                       onclick="return confirm('确定要删除这个脚本吗？')">
                                        <i class="fas fa-trash"></i> 删除
                                    </a>
                                <?php else: ?>
                                    <span class="btn btn-secondary" 
                                          style="padding: 6px 12px; font-size: 12px; opacity: 0.5; cursor: not-allowed;"
                                          title="有卡密关联，无法删除">
                                        <i class="fas fa-lock"></i> 锁定
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (!empty($script['loader_code'])): ?>
                                <div id="loader-modal-<?php echo $script['id']; ?>" style="display: none;">
                                    <textarea readonly class="code-editor" rows="15" style="width: 100%; margin-top: 10px;"><?php echo htmlspecialchars($script['loader_code']); ?></textarea>
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<script>
function copyLoaderCode() {
    const textarea = document.getElementById('loader-code');
    textarea.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> 已复制！';
    btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.style.background = '';
    }, 2000);
    
    // 自动填充到加载器代码字段
    const loaderCodeField = document.getElementById('loader_code');
    if (loaderCodeField) {
        loaderCodeField.value = textarea.value;
    }
}

function saveLoaderToScript() {
    const generatedCode = document.getElementById('loader-code');
    const loaderField = document.getElementById('loader_code');
    
    if (generatedCode && loaderField) {
        loaderField.value = generatedCode.value;
        
        // 显示保存成功提示
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> 已保存！';
        btn.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
        }, 2000);
        
        // 提示用户记得点击保存脚本按钮
        setTimeout(() => {
            alert('加载器代码已填入表单，请点击"保存脚本"按钮完成保存！');
        }, 500);
    }
}

function showLoaderCode(scriptId) {
    const modal = document.getElementById('loader-modal-' + scriptId);
    if (modal.style.display === 'none') {
        // 隐藏其他打开的模态框
        document.querySelectorAll('[id^="loader-modal-"]').forEach(m => m.style.display = 'none');
        modal.style.display = 'block';
    } else {
        modal.style.display = 'none';
    }
}

// 自动提取脚本信息
function extractScriptInfo() {
    const textarea = document.getElementById('full_script_with_header');
    const nameField = document.getElementById('extracted-name');
    const versionField = document.getElementById('extracted-version');
    
    if (!textarea || !nameField || !versionField) return;
    
    const content = textarea.value;
    
    // 提取 @name
    const nameMatch = content.match(/\/\/ @name\s+(.+)/i);
    if (nameMatch) {
        nameField.value = nameMatch[1].trim();
        nameField.style.color = '#28a745';
    } else {
        nameField.value = '';
        nameField.style.color = '#6c757d';
    }
    
    // 提取 @version
    const versionMatch = content.match(/\/\/ @version\s+(.+)/i);
    if (versionMatch) {
        versionField.value = versionMatch[1].trim();
        versionField.style.color = '#28a745';
    } else {
        versionField.value = '';
        versionField.style.color = '#6c757d';
    }
}

// 当生成加载器后，自动填充到表单字段
document.addEventListener('DOMContentLoaded', function() {
    const convertForm = document.querySelector('form[method="POST"]');
    if (convertForm) {
        convertForm.addEventListener('submit', function(e) {
            if (e.submitter && e.submitter.name === 'convert_loader') {
                setTimeout(() => {
                    const generatedCode = document.getElementById('loader-code');
                    const loaderField = document.getElementById('loader_code');
                    if (generatedCode && loaderField && generatedCode.value) {
                        loaderField.value = generatedCode.value;
                    }
                }, 100);
            }
        });
    }
});
</script> 