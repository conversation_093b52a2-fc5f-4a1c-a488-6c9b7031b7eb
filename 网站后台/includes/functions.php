<?php

function is_logged_in() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function require_login() {
    if (!is_logged_in()) {
        header('Location: login.php');
        exit();
    }
}

function logout() {
    $_SESSION = array();
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    session_destroy();
    header('Location: login.php');
    exit();
}

function generate_key($length = 20) {
    return 'XMHS-' . strtoupper(bin2hex(random_bytes($length / 2)));
}

function get_client_ip() {
    $ip = '';
    if (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED'];
    } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
        $ip = $_SERVER['HTTP_FORWARDED'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

function format_time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return '刚刚';
    if ($time < 3600) return floor($time / 60) . '分钟前';
    if ($time < 86400) return floor($time / 3600) . '小时前';
    if ($time < 2592000) return floor($time / 86400) . '天前';
    if ($time < 31536000) return floor($time / 2592000) . '个月前';
    return floor($time / 31536000) . '年前';
}

function getDeviceFingerprint() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $accept_language = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
    $accept_encoding = $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    
    $fingerprint_data = $user_agent . $accept_language . $accept_encoding . $ip_address;
    return hash('sha256', $fingerprint_data);
}

function getDeviceName() {
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    if (strpos($user_agent, 'Windows') !== false) {
        $os = 'Windows';
    } elseif (strpos($user_agent, 'Mac') !== false) {
        $os = 'macOS';
    } elseif (strpos($user_agent, 'Linux') !== false) {
        $os = 'Linux';
    } elseif (strpos($user_agent, 'iPhone') !== false) {
        $os = 'iPhone';
    } elseif (strpos($user_agent, 'Android') !== false) {
        $os = 'Android';
    } else {
        $os = '未知设备';
    }
    
    if (strpos($user_agent, 'Chrome') !== false) {
        $browser = 'Chrome';
    } elseif (strpos($user_agent, 'Firefox') !== false) {
        $browser = 'Firefox';
    } elseif (strpos($user_agent, 'Safari') !== false) {
        $browser = 'Safari';
    } elseif (strpos($user_agent, 'Edge') !== false) {
        $browser = 'Edge';
    } else {
        $browser = '未知浏览器';
    }
    
    return $os . ' - ' . $browser;
}

function generateVerificationCode() {
    return sprintf('%06d', mt_rand(100000, 999999));
}

function getSystemSettings() {
    global $pdo;
    
    try {
        $settings = [];
        
        // 首先从system_settings表读取配置
        $stmt = $pdo->query("SELECT * FROM system_settings");
        while ($row = $stmt->fetch()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // 然后从smtp_config表读取用户的SMTP配置（如果存在）
        // 这样用户在设置页面配置的SMTP会覆盖系统默认设置
        try {
            // 检查当前登录用户的SMTP配置
            if (isset($_SESSION['admin_user_id'])) {
                $stmt = $pdo->prepare("SELECT * FROM smtp_config WHERE user_id = ?");
                $stmt->execute([$_SESSION['admin_user_id']]);
                $smtp_config = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($smtp_config) {
                    // 用用户配置覆盖系统配置
                    $settings['smtp_host'] = $smtp_config['smtp_host'];
                    $settings['smtp_port'] = $smtp_config['smtp_port'];
                    $settings['smtp_username'] = $smtp_config['smtp_username'];
                    $settings['smtp_password'] = $smtp_config['smtp_password'];
                    $settings['from_email'] = $smtp_config['from_email'] ?? $smtp_config['smtp_username'];
                    $settings['from_name'] = $smtp_config['from_name'] ?? '小梅花AI客服系统';
                    
                    error_log("使用用户SMTP配置: Host={$smtp_config['smtp_host']}, Port={$smtp_config['smtp_port']}, Username={$smtp_config['smtp_username']}");
                }
            }
        } catch (Exception $e) {
            // smtp_config表可能不存在，忽略错误
            error_log("读取用户SMTP配置失败（可能表不存在）: " . $e->getMessage());
        }
        
        return $settings;
    } catch (Exception $e) {
        error_log("获取系统设置失败: " . $e->getMessage());
        return [];
    }
}

function updateSystemSetting($key, $value) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value, updated_at) VALUES (?, ?, datetime('now')) -- ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = datetime('now')");
        return $stmt->execute([$key, $value, $value]);
    } catch (Exception $e) {
        return false;
    }
}

function sendVerificationEmail($email, $code) {
    $settings = getSystemSettings();
    
    error_log("=== 登录验证码邮件发送 ===");
    error_log("目标邮箱: $email");
    error_log("验证码: $code");
    
    if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
        error_log('❌ SMTP配置不完整，无法发送邮件');
        return false;
    }
    
    // 使用与测试邮件相同的成功方法
    return sendTestEmailWithConfig($email, $settings['smtp_host'], $settings['smtp_port'], $settings['smtp_username'], $settings['smtp_password']);
}

function sendLoginNotification($email, $ip, $time) {
    $settings = getSystemSettings();
    
    error_log("发送登录通知邮件: email=$email, ip=$ip");
    error_log("登录通知设置: " . ($settings['login_notification_enabled'] ?? '0'));
    error_log("SMTP配置: host=" . ($settings['smtp_host'] ?? 'empty') . ", username=" . ($settings['smtp_username'] ?? 'empty'));
    
    if (empty($settings['login_notification_enabled']) || $settings['login_notification_enabled'] !== '1') {
        error_log("登录通知未启用");
        return true;
    }
    
    if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
        error_log("SMTP配置不完整，无法发送登录通知");
        return false;
    }
    
    // 检查PHPMailer库是否存在
    if (file_exists(__DIR__ . '/PHPMailer/src/PHPMailer.php')) {
        require_once __DIR__ . '/PHPMailer/src/PHPMailer.php';
        require_once __DIR__ . '/PHPMailer/src/SMTP.php';
        require_once __DIR__ . '/PHPMailer/src/Exception.php';
    } elseif (file_exists(__DIR__ . '/PHPMailer/PHPMailer.php')) {
        require_once __DIR__ . '/PHPMailer/PHPMailer.php';
        require_once __DIR__ . '/PHPMailer/SMTP.php';
        require_once __DIR__ . '/PHPMailer/Exception.php';
    } else {
        error_log('PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
    } else {
        error_log('PHPMailer类未找到');
        return false;
    }
    
    try {
        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        // 根据端口选择加密方式
        $port = intval($settings['smtp_port'] ?? 465);
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        // 发件人和收件人
        $from_email = $settings['from_email'] ?: $settings['smtp_username'];
        $from_name = $settings['from_name'] ?: '小梅花AI客服系统';
        
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($email);
        
        // 邮件内容
        $mail->isHTML(true);
        $mail->Subject = '【小梅花AI客服系统】登录通知';
        
        $mail->Body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); padding: 30px; border-radius: 15px;'>
            <div style='background: white; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <div style='background: #ff6b6b; width: 60px; height: 60px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;'>
                        <span style='color: white; font-size: 24px;'>🔐</span>
                    </div>
                    <h1 style='color: #333; margin: 0; font-size: 24px;'>账号登录通知</h1>
                </div>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>
                    <h3 style='color: #333; margin: 0 0 15px 0; font-size: 16px;'>登录详情：</h3>
                    <div style='color: #666; line-height: 1.6;'>
                        <p style='margin: 5px 0;'><strong>登录时间：</strong> $time</p>
                        <p style='margin: 5px 0;'><strong>登录IP：</strong> $ip</p>
                        <p style='margin: 5px 0;'><strong>登录邮箱：</strong> $email</p>
                    </div>
                </div>
                
                <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p style='color: #856404; margin: 0; font-size: 14px;'>
                        <strong>🔒 安全提醒：</strong><br>
                        • 如果这是您的正常登录，可以忽略此邮件<br>
                        • 如果您没有进行此次登录，请立即修改密码<br>
                        • 建议启用设备信任功能增强账号安全
                    </p>
                </div>
                
                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        此邮件由小梅花AI客服系统自动发送，请勿回复<br>
                        发送时间：" . date('Y-m-d H:i:s') . "
                    </p>
                </div>
            </div>
        </div>";
        
        $mail->AltBody = "【小梅花AI客服系统】账号登录通知 - 登录时间：$time，登录IP：$ip";
        
        $result = $mail->send();
        error_log("登录通知邮件发送" . ($result ? "成功" : "失败"));
        return $result;
        
    } catch (Exception $e) {
        error_log('登录通知邮件发送失败: ' . $e->getMessage());
        error_log('PHPMailer错误信息: ' . $mail->ErrorInfo);
        error_log('SMTP配置详情: Host=' . $settings['smtp_host'] . ', Port=' . $port . ', Username=' . $settings['smtp_username']);
        return false;
    }
}

function getLocationByIP($ip) {
    if ($ip === '127.0.0.1' || $ip === '::1') {
        return '本地环境';
    }
    
    try {
        $response = @file_get_contents("http://ip-api.com/json/{$ip}?lang=zh-CN");
        if ($response) {
            $data = json_decode($response, true);
            if ($data && $data['status'] === 'success') {
                return $data['country'] . ' ' . $data['regionName'] . ' ' . $data['city'];
            }
        }
    } catch (Exception $e) {
        // 忽略错误
    }
    
    return '未知地点';
}

function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return '刚刚';
    } elseif ($time < 3600) {
        return floor($time / 60) . '分钟前';
    } elseif ($time < 86400) {
        return floor($time / 3600) . '小时前';
    } elseif ($time < 2592000) {
        return floor($time / 86400) . '天前';
    } else {
        return date('Y-m-d H:i', strtotime($datetime));
    }
}

function generateRandomString($length = 32) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function isValidIP($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP) !== false;
}

function getRealIP() {
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ips[0]);
    } elseif (isset($_SERVER['HTTP_X_REAL_IP']) && !empty($_SERVER['HTTP_X_REAL_IP'])) {
        return $_SERVER['HTTP_X_REAL_IP'];
    } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && !empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// 使用指定SMTP配置发送测试邮件
function sendTestEmailWithConfig($to_email, $smtp_host, $smtp_port, $smtp_username, $smtp_password) {
    error_log("测试邮件发送开始 - 目标邮箱: $to_email, SMTP: $smtp_host:$smtp_port, 用户: $smtp_username");
    
    // 智能检测PHPMailer路径
    $phpmailer_paths = [
        __DIR__ . '/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            error_log("PHPMailer库加载成功: $path");
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        error_log("使用命名空间版本的PHPMailer");
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
        error_log("使用传统版本的PHPMailer");
    } else {
        error_log('PHPMailer类未找到');
        return false;
    }
    
    try {
        // 服务器设置
        $mail->isSMTP();
        $mail->Host = $smtp_host;
        $mail->SMTPAuth = true;
        $mail->Username = $smtp_username;
        $mail->Password = $smtp_password;
        
        // 根据端口和主机选择加密方式
        $port = intval($smtp_port);
        $host = strtolower($smtp_host);
        
        error_log("配置SMTP - 主机: $smtp_host, 端口: $port");
        
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
            error_log("使用SSL加密 (端口465)");
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (端口587)");
        } elseif ($port == 25) {
            // 25端口通常不使用加密
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
            error_log("使用无加密连接 (端口25)");
        } else {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (其他端口)");
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0; // 生产环境关闭调试
        
        // 发件人和收件人
        $mail->setFrom($smtp_username, '小梅花AI客服系统');
        $mail->addAddress($to_email);
        
        // 邮件内容
        $mail->isHTML(true);
        $mail->Subject = '小梅花AI客服系统 - 邮件配置测试';
        
        $mail->Body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; overflow: hidden;">
            <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); padding: 40px; text-align: center;">
                <div style="background: linear-gradient(135deg, #ff6b9d, #c44569); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 32px; color: white;">
                    🌸
                </div>
                <h1 style="color: white; font-size: 24px; margin-bottom: 20px;">邮件配置测试成功！</h1>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
                    恭喜！您的SMTP邮件配置已成功设置。<br>
                    这是一封来自小梅花AI客服系统的测试邮件。
                </p>
                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: white; margin-bottom: 15px;">📧 配置信息</h3>
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 5px 0;">SMTP服务器: ' . htmlspecialchars($smtp_host) . '</p>
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 5px 0;">端口: ' . htmlspecialchars($smtp_port) . '</p>
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 5px 0;">发送时间: ' . date('Y-m-d H:i:s') . '</p>
                </div>
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.2);">
                    <p style="color: rgba(255, 255, 255, 0.7); font-size: 14px;">
                        此邮件由小梅花AI客服系统自动发送，请勿回复。
                    </p>
                </div>
            </div>
        </div>';
        
        $mail->AltBody = "小梅花AI客服系统邮件配置测试成功！\n\n您的SMTP配置已正确设置。\n\n发送时间：" . date('Y-m-d H:i:s');
        
        error_log("开始发送邮件到: $to_email");
        $mail->send();
        error_log("测试邮件发送成功: $to_email");
        return true;
    } catch (Exception $e) {
        $error_msg = $e->getMessage();
        error_log("邮件发送失败 - 错误信息: " . $error_msg);
        error_log("邮件发送失败 - PHPMailer错误: " . $mail->ErrorInfo);
        
        // 分析常见错误并提供解决建议
        if (strpos($error_msg, 'SMTP connect() failed') !== false) {
            error_log("SMTP连接失败 - 可能是主机或端口配置错误");
        } elseif (strpos($error_msg, 'SMTP AUTH') !== false || strpos($error_msg, 'Authentication') !== false) {
            error_log("SMTP认证失败 - 可能是用户名或密码错误");
        } elseif (strpos($error_msg, 'tls') !== false || strpos($error_msg, 'ssl') !== false) {
            error_log("SSL/TLS连接失败 - 可能是加密方式配置错误");
        }
        
        return false;
    }
}

function sendTrustDeviceVerificationEmail($email, $code) {
    $settings = getSystemSettings();
    
    error_log("=== 发送信任设备验证码邮件 ===");
    error_log("目标邮箱: $email");
    error_log("验证码: $code");
    error_log("SMTP配置检查:");
    error_log("- Host: " . ($settings['smtp_host'] ?? 'empty'));
    error_log("- Port: " . ($settings['smtp_port'] ?? 'empty'));
    error_log("- Username: " . ($settings['smtp_username'] ?? 'empty'));
    error_log("- Password: " . (!empty($settings['smtp_password']) ? '***已设置***' : 'empty'));
    
    if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
        error_log('❌ SMTP配置不完整，无法发送邮件');
        return false;
    }
    
    // 检查PHPMailer库是否存在
    $phpmailer_paths = [
        __DIR__ . '/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            error_log("✅ PHPMailer库加载成功: $path");
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('❌ PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        error_log("使用命名空间版本的PHPMailer");
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
        error_log("使用传统版本的PHPMailer");
    } else {
        error_log('❌ PHPMailer类未找到');
        return false;
    }
    
    try {
        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        // 根据端口选择加密方式
        $port = intval($settings['smtp_port'] ?? 465);
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
            error_log("使用SSL加密 (端口465)");
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (端口587)");
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
            error_log("使用无加密连接 (端口25)");
        } else {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (其他端口)");
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        // 发件人和收件人
        $from_email = $settings['from_email'] ?: $settings['smtp_username'];
        $from_name = $settings['from_name'] ?: '小梅花AI客服系统';
        
        error_log("发件人: $from_name <$from_email>");
        
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($email);
        
        // 邮件内容
        $mail->isHTML(true);
        $mail->Subject = '【小梅花AI客服系统】设备信任验证码';
        
        $mail->Body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 15px;'>
            <div style='background: white; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <div style='background: #4CAF50; width: 60px; height: 60px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;'>
                        <span style='color: white; font-size: 24px;'>🛡️</span>
                    </div>
                    <h1 style='color: #333; margin: 0; font-size: 24px;'>设备信任验证</h1>
                </div>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>
                    <p style='color: #666; margin: 0 0 15px 0; font-size: 14px;'>您的验证码是：</p>
                    <div style='font-size: 32px; font-weight: bold; color: #4CAF50; letter-spacing: 5px; font-family: monospace;'>$code</div>
                    <p style='color: #999; margin: 15px 0 0 0; font-size: 12px;'>验证码有效期：20秒</p>
                </div>
                
                <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p style='color: #856404; margin: 0; font-size: 14px;'>
                        <strong>⚠️ 安全提醒：</strong><br>
                        • 请勿将此验证码告诉任何人<br>
                        • 验证码仅用于信任当前设备<br>
                        • 如非本人操作，请立即检查账号安全
                    </p>
                </div>
                
                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        此邮件由小梅花AI客服系统自动发送，请勿回复<br>
                        发送时间：" . date('Y-m-d H:i:s') . "
                    </p>
                </div>
            </div>
        </div>";
        
        $mail->AltBody = "【小梅花AI客服系统】设备信任验证码：$code（有效期20秒）";
        
        error_log("开始发送邮件...");
        $result = $mail->send();
        error_log("✅ 信任设备验证码邮件发送成功");
        return true;
        
    } catch (Exception $e) {
        $error_msg = $e->getMessage();
        error_log('❌ 信任设备验证码邮件发送失败: ' . $error_msg);
        error_log('PHPMailer错误信息: ' . $mail->ErrorInfo);
        error_log('SMTP配置详情: Host=' . $settings['smtp_host'] . ', Port=' . $port . ', Username=' . $settings['smtp_username']);
        
        // 分析常见错误并提供解决建议
        if (strpos($error_msg, 'SMTP connect() failed') !== false) {
            error_log("分析：SMTP连接失败 - 可能是主机或端口配置错误");
        } elseif (strpos($error_msg, 'SMTP AUTH') !== false || strpos($error_msg, 'Authentication') !== false) {
            error_log("分析：SMTP认证失败 - 可能是用户名或密码错误");
        } elseif (strpos($error_msg, 'tls') !== false || strpos($error_msg, 'ssl') !== false) {
            error_log("分析：SSL/TLS连接失败 - 可能是加密方式配置错误");
        }
        
        return false;
    }
}

function sendSmtpConfigVerificationEmail($email, $code) {
    $settings = getSystemSettings();
    
    error_log("=== 发送SMTP配置验证码邮件 ===");
    error_log("目标邮箱: $email");
    error_log("验证码: $code");
    error_log("SMTP配置检查:");
    error_log("- Host: " . ($settings['smtp_host'] ?? 'empty'));
    error_log("- Port: " . ($settings['smtp_port'] ?? 'empty'));
    error_log("- Username: " . ($settings['smtp_username'] ?? 'empty'));
    error_log("- Password: " . (!empty($settings['smtp_password']) ? '***已设置***' : 'empty'));
    
    if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
        error_log('❌ SMTP配置不完整，无法发送邮件');
        return false;
    }
    
    // 检查PHPMailer库是否存在
    $phpmailer_paths = [
        __DIR__ . '/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            error_log("✅ PHPMailer库加载成功: $path");
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('❌ PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        error_log("使用命名空间版本的PHPMailer");
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
        error_log("使用传统版本的PHPMailer");
    } else {
        error_log('❌ PHPMailer类未找到');
        return false;
    }
    
    try {
        // SMTP配置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        // 根据端口选择加密方式
        $port = intval($settings['smtp_port'] ?? 465);
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
            error_log("使用SSL加密 (端口465)");
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (端口587)");
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
            error_log("使用无加密连接 (端口25)");
        } else {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (其他端口)");
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        // 发件人和收件人
        $from_email = $settings['from_email'] ?: $settings['smtp_username'];
        $from_name = $settings['from_name'] ?: '小梅花AI客服系统';
        
        error_log("发件人: $from_name <$from_email>");
        
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress($email);
        
        // 邮件内容
        $mail->isHTML(true);
        $mail->Subject = '【小梅花AI客服系统】邮箱配置验证码';
        
        $mail->Body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); padding: 30px; border-radius: 15px;'>
            <div style='background: white; padding: 30px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);'>
                <div style='text-align: center; margin-bottom: 30px;'>
                    <div style='background: #3498db; width: 60px; height: 60px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;'>
                        <span style='color: white; font-size: 24px;'>⚙️</span>
                    </div>
                    <h1 style='color: #333; margin: 0; font-size: 24px;'>邮箱配置验证</h1>
                </div>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>
                    <p style='color: #666; margin: 0 0 15px 0; font-size: 14px;'>您的验证码是：</p>
                    <div style='font-size: 32px; font-weight: bold; color: #3498db; letter-spacing: 5px; font-family: monospace;'>$code</div>
                    <p style='color: #999; margin: 15px 0 0 0; font-size: 12px;'>验证码有效期：20秒</p>
                </div>
                
                <div style='background: #e3f2fd; border: 1px solid #bbdefb; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p style='color: #1565c0; margin: 0; font-size: 14px;'>
                        <strong>ℹ️ 温馨提示：</strong><br>
                        • 此验证码用于确认邮箱配置修改<br>
                        • 请在20秒内完成验证<br>
                        • 如非本人操作，请立即检查账号安全
                    </p>
                </div>
                
                <div style='text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                    <p style='color: #999; font-size: 12px; margin: 0;'>
                        此邮件由小梅花AI客服系统自动发送，请勿回复<br>
                        发送时间：" . date('Y-m-d H:i:s') . "
                    </p>
                </div>
            </div>
        </div>";
        
        $mail->AltBody = "【小梅花AI客服系统】邮箱配置验证码：$code（有效期20秒）";
        
        error_log("开始发送邮件...");
        $result = $mail->send();
        error_log("✅ SMTP配置验证码邮件发送成功");
        return true;
        
    } catch (Exception $e) {
        $error_msg = $e->getMessage();
        error_log('❌ SMTP配置验证码邮件发送失败: ' . $error_msg);
        error_log('PHPMailer错误信息: ' . $mail->ErrorInfo);
        error_log('SMTP配置详情: Host=' . $settings['smtp_host'] . ', Port=' . $port . ', Username=' . $settings['smtp_username']);
        
        // 分析常见错误并提供解决建议
        if (strpos($error_msg, 'SMTP connect() failed') !== false) {
            error_log("分析：SMTP连接失败 - 可能是主机或端口配置错误");
        } elseif (strpos($error_msg, 'SMTP AUTH') !== false || strpos($error_msg, 'Authentication') !== false) {
            error_log("分析：SMTP认证失败 - 可能是用户名或密码错误");
        } elseif (strpos($error_msg, 'tls') !== false || strpos($error_msg, 'ssl') !== false) {
            error_log("分析：SSL/TLS连接失败 - 可能是加密方式配置错误");
        }
        
        return false;
    }
}

// 修复版的sendVerificationEmail函数 - 使用优化的邮件格式
function sendVerificationEmailFixed($email, $code) {
    $settings = getSystemSettings();
    
    error_log("🔥 发送登录验证码邮件");
    error_log("目标邮箱: $email");
    error_log("验证码: $code");
    
    if (empty($settings['smtp_host']) || empty($settings['smtp_username']) || empty($settings['smtp_password'])) {
        error_log('❌ SMTP配置不完整，无法发送邮件');
        return false;
    }
    
    // 智能检测PHPMailer路径
    $phpmailer_paths = [
        __DIR__ . '/PHPMailer/src/PHPMailer.php',
        __DIR__ . '/PHPMailer/PHPMailer.php'
    ];
    
    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            $base_dir = dirname($path);
            if (file_exists($base_dir . '/Exception.php')) {
                require_once $base_dir . '/Exception.php';
            }
            if (file_exists($base_dir . '/PHPMailer.php')) {
                require_once $base_dir . '/PHPMailer.php';
            }
            if (file_exists($base_dir . '/SMTP.php')) {
                require_once $base_dir . '/SMTP.php';
            }
            $phpmailer_found = true;
            error_log("PHPMailer库加载成功: $path");
            break;
        }
    }
    
    if (!$phpmailer_found) {
        error_log('PHPMailer库未找到');
        return false;
    }
    
    // 根据不同版本使用不同的类名
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $encryption_starttls = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $encryption_smtps = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        error_log("使用命名空间版本的PHPMailer");
    } elseif (class_exists('PHPMailer')) {
        $mail = new PHPMailer(true);
        $encryption_starttls = 'tls';
        $encryption_smtps = 'ssl';
        error_log("使用传统版本的PHPMailer");
    } else {
        error_log('PHPMailer类未找到');
        return false;
    }
    
    try {
        // 服务器设置
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $settings['smtp_username'];
        $mail->Password = $settings['smtp_password'];
        
        // 根据端口选择加密方式
        $port = intval($settings['smtp_port']);
        
        error_log("配置SMTP - 主机: " . $settings['smtp_host'] . ", 端口: $port");
        
        if ($port == 465) {
            $mail->SMTPSecure = $encryption_smtps;
            error_log("使用SSL加密 (端口465)");
        } elseif ($port == 587) {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (端口587)");
        } elseif ($port == 25) {
            $mail->SMTPSecure = false;
            $mail->SMTPAuth = true;
            error_log("使用无加密连接 (端口25)");
        } else {
            $mail->SMTPSecure = $encryption_starttls;
            error_log("使用STARTTLS加密 (其他端口)");
        }
        
        $mail->Port = $port;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = 30;
        $mail->SMTPDebug = 0;
        
        // 发件人和收件人
        $mail->setFrom($settings['smtp_username'], '小梅花AI客服系统');
        $mail->addAddress($email);
        
        // 邮件内容 - 使用新的优化格式
        $mail->isHTML(true);
        $mail->Subject = '小梅花AI客服系统 - 您的后台登录验证码';
        
        // 优化后的邮件HTML内容
        $mail->Body = '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; overflow: hidden;">
            <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); padding: 40px; text-align: center;">
                <div style="background: linear-gradient(135deg, #ff6b9d, #c44569); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center; font-size: 32px; color: white;">
                    🌸
                </div>
                <h1 style="color: white; font-size: 24px; margin-bottom: 20px;">后台登录验证</h1>
                <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
                    您正在登录后台管理系统<br>
                    为了保护您的账户安全，请使用以下验证码完成登录验证。
                </p>
                
                <!-- 验证码显示区域 -->
                <div style="background: rgba(255, 255, 255, 0.15); border-radius: 15px; padding: 30px; margin: 30px 0; border: 2px solid rgba(255, 255, 255, 0.2);">
                    <p style="color: white; margin: 0 0 15px 0; font-size: 18px; font-weight: bold;">🔐 您的验证码</p>
                    <div style="font-size: 36px; font-weight: bold; color: #FFD700; letter-spacing: 8px; font-family: monospace; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin: 20px 0;">' . htmlspecialchars($code) . '</div>
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 15px 0 0 0; font-size: 14px;">⏰ 验证码有效期：20秒</p>
                    <p style="color: rgba(255, 255, 255, 0.7); margin: 5px 0 0 0; font-size: 12px;">请及时使用，过期后需要重新获取</p>
                </div>
                
                <!-- 安全提示 -->
                <div style="background: rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; margin: 20px 0;">
                    <h3 style="color: white; margin-bottom: 15px; font-size: 16px;">🔒 安全提示</h3>
                    <div style="text-align: left; color: rgba(255, 255, 255, 0.8); font-size: 14px; line-height: 1.6;">
                        <p style="margin: 8px 0;">• 请勿将验证码告诉他人</p>
                        <p style="margin: 8px 0;">• 如非本人操作，请立即检查账户安全</p>
                        <p style="margin: 8px 0;">• 验证码仅用于本次登录，请及时使用</p>
                    </div>
                </div>
                
                <!-- 操作信息 -->
                <div style="background: rgba(255, 255, 255, 0.05); border-radius: 8px; padding: 15px; margin: 20px 0;">
                    <p style="color: rgba(255, 255, 255, 0.7); margin: 5px 0; font-size: 12px;">操作时间: ' . date('Y-m-d H:i:s') . '</p>
                    <p style="color: rgba(255, 255, 255, 0.7); margin: 5px 0; font-size: 12px;">操作类型: 安全登录</p>
                </div>
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.2);">
                    <p style="color: rgba(255, 255, 255, 0.7); font-size: 14px;">
                        此邮件由小梅花AI客服系统自动发送，请勿回复。
                    </p>
                </div>
            </div>
        </div>';
        
        // 纯文本版本
        $mail->AltBody = "小梅花AI客服系统 - 后台登录验证\n\n" . 
                        "您正在登录后台管理系统\n" . 
                        "为了保护您的账户安全，请使用以下验证码完成登录验证。\n\n" . 
                        "您的验证码：" . $code . "\n" . 
                        "有效期：20秒\n\n" . 
                        "操作时间：" . date('Y-m-d H:i:s') . "\n" . 
                        "操作类型：安全登录\n\n" . 
                        "安全提示：\n" . 
                        "• 请勿将验证码告诉他人\n" . 
                        "• 如非本人操作，请立即检查账户安全\n" . 
                        "• 验证码仅用于本次登录，请及时使用\n\n" . 
                        "此邮件由小梅花AI客服系统自动发送，请勿回复。";
        
        error_log("🔥 开始发送登录验证码邮件到: $email");
        $mail->send();
        error_log("🎉 登录验证码邮件发送成功: $email");
        return true;
    } catch (Exception $e) {
        $error_msg = $e->getMessage();
        error_log("❌ 登录验证码邮件发送失败 - 错误信息: " . $error_msg);
        error_log("❌ 登录验证码邮件发送失败 - PHPMailer错误: " . $mail->ErrorInfo);
        
        // 分析常见错误并提供解决建议
        if (strpos($error_msg, 'SMTP connect() failed') !== false) {
            error_log("SMTP连接失败 - 可能是主机或端口配置错误");
        } elseif (strpos($error_msg, 'SMTP AUTH') !== false || strpos($error_msg, 'Authentication') !== false) {
            error_log("SMTP认证失败 - 可能是用户名或密码错误");
        } elseif (strpos($error_msg, 'tls') !== false || strpos($error_msg, 'ssl') !== false) {
            error_log("SSL/TLS连接失败 - 可能是加密方式配置错误");
        }
        
        return false;
    }
}

?>

