<?php
// 导入环境变量管理系统
require_once dirname(__DIR__) . '/api/env_manager.php';

// 初始化环境管理器并加载环境变量
$env_manager = EnvManager::getInstance();
$env_manager->load();

// 使用环境变量获取数据库配置
$db_config = $env_manager->getDatabaseConfig();

// === MySQL数据库连接配置 ===
$mysql_config = [
    'host' => $db_config['host'],
    'port' => $db_config['port'],
    'dbname' => $db_config['dbname'],
    'username' => $db_config['user'],
    'password' => $db_config['pass'],
    'charset' => 'utf8mb4'
];

try {
    // 使用MySQL数据库
    $dsn = "mysql:host={$mysql_config['host']};port={$mysql_config['port']};dbname={$mysql_config['dbname']};charset={$mysql_config['charset']}";
    $pdo = new PDO($dsn, $mysql_config['username'], $mysql_config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    // 禁用预处理语句模拟，确保使用真正的预处理语句
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
    
    // 创建基本的管理员用户表（MySQL语法）
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `admin_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    
    // 创建API密钥表（MySQL语法）
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `api_keys` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `key_name` varchar(255) NOT NULL,
            `api_key` varchar(255) NOT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `created_by` int(11) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `api_key` (`api_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
    
    // 检查是否有管理员用户，如果没有则创建默认用户 - 使用预处理语句
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM admin_users");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        // 创建默认管理员账户 - 使用预处理语句防止SQL注入
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, password, email) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute(['admin', $defaultPassword, '<EMAIL>']);
    }
    
    echo "<!-- MySQL数据库连接成功 -->\n";
    
} catch (PDOException $e) {
    die("MySQL数据库连接失败: " . $e->getMessage() . "\n请检查数据库配置参数");
}
?> 