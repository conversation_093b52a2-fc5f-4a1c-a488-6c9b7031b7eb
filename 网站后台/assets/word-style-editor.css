/* Word风格富文本编辑器样式 */

.word-editor-container {
    background: rgba(0, 0, 0, 0.6);
    border-radius: 12px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 工具栏样式 */
.word-toolbar {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    backdrop-filter: blur(10px);
}

/* 工具栏分组 */
.toolbar-group {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 8px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar-group:last-child {
    border-right: none;
}

/* 字体选择器 */
.font-selector {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    min-width: 80px;
    cursor: pointer;
    position: relative;
    user-select: none;
}

.font-selector:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.font-selector:after {
    content: '▼';
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 8px;
    opacity: 0.7;
}

/* 字体大小控制组 */
.size-control-group {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 2px;
}

.size-display {
    color: white;
    font-size: 12px;
    font-weight: 500;
    min-width: 24px;
    text-align: center;
    padding: 0 4px;
    user-select: none;
}

/* 字体大小加减按钮 */
.size-increase, .size-decrease {
    min-width: 28px !important;
    height: 28px !important;
    padding: 4px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.1) !important;
}

.size-increase:hover, .size-decrease:hover {
    background: rgba(255, 107, 157, 0.3) !important;
    border-color: rgba(255, 107, 157, 0.5) !important;
    color: #ff6b9d !important;
    transform: translateY(-1px) !important;
}

.size-increase:active, .size-decrease:active {
    transform: translateY(0) !important;
    background: rgba(255, 107, 157, 0.5) !important;
}

/* 工具栏按钮 */
.toolbar-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: white;
    padding: 6px 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    font-weight: 500;
}

.toolbar-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.toolbar-btn.active {
    background: rgba(255, 107, 157, 0.3);
    border-color: rgba(255, 107, 157, 0.5);
    color: #ff6b9d;
}

/* 颜色按钮特殊样式 */
.color-btn {
    position: relative;
    padding: 4px 6px;
}

.color-btn .color-indicator {
    width: 20px;
    height: 3px;
    background: currentColor;
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 1px;
}

/* 颜色选择器面板 */
.color-picker-panel {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px;
    display: none;
    z-index: 1000;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.color-picker-panel.show {
    display: block;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    margin-bottom: 8px;
}

.color-item {
    width: 24px;
    height: 24px;
    border-radius: 3px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.color-item:hover {
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

/* 编辑区域 */
.word-editor-content {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 15px;
    min-height: 200px;
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    outline: none;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
}

.word-editor-content:focus {
    border-color: rgba(255, 107, 157, 0.5);
    box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.2);
}

/* 字体大小选项已移除，改用加减按钮 */

/* 字体选项 */
.font-options {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    padding: 4px 0;
    display: none;
    z-index: 1000;
    min-width: 140px;
    backdrop-filter: blur(15px);
    max-height: 200px;
    overflow-y: auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.font-options.show {
    display: block;
}

.font-option {
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    user-select: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    white-space: nowrap;
}

.font-option:last-child {
    border-bottom: none;
}

.font-option:hover {
    background: rgba(255, 107, 157, 0.3);
    color: #ff6b9d;
}

/* 字体选项的字体预览 */
.font-option[data-font="Microsoft YaHei"] {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif !important;
}

.font-option[data-font="SimSun"] {
    font-family: "SimSun", "宋体", serif !important;
}

.font-option[data-font="SimHei"] {
    font-family: "SimHei", "黑体", sans-serif !important;
}

.font-option[data-font="KaiTi"] {
    font-family: "KaiTi", "楷体", serif !important;
}

.font-option[data-font="Arial"] {
    font-family: Arial, sans-serif !important;
}

.font-option[data-font="Times New Roman"] {
    font-family: "Times New Roman", serif !important;
}

.font-option[data-font="Helvetica"] {
    font-family: Helvetica, Arial, sans-serif !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .word-toolbar {
        padding: 6px 8px;
        gap: 4px;
    }
    
    .toolbar-group {
        padding: 0 4px;
    }
    
    .toolbar-btn {
        min-width: 28px;
        height: 28px;
        padding: 4px 6px;
        font-size: 12px;
    }
    
    .font-selector, .size-selector {
        min-width: 60px;
        font-size: 11px;
        padding: 3px 6px;
    }
}

/* 工具提示 */
.toolbar-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1001;
    pointer-events: none;
}

/* 分隔线 */
.toolbar-separator {
    width: 1px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 4px;
}

/* 活动状态指示器 */
.toolbar-btn.bold.active {
    font-weight: 700;
}

.toolbar-btn.italic.active {
    font-style: italic;
}

.toolbar-btn.underline.active {
    text-decoration: underline;
}
