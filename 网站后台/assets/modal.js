/**
 * 自定义弹窗组件
 * 功能：替换原生confirm()函数，实现居中显示的美观弹窗
 * 版本：v2.1.0
 */

class CustomModal {
    constructor() {
        this.currentModal = null;
        this.init();
    }

    init() {
        // 创建弹窗容器
        this.createModalContainer();
        // 覆盖原生confirm函数
        this.overrideNativeConfirm();
    }

    createModalContainer() {
        // 创建弹窗HTML结构
        const modalHTML = `
            <div id="customModal" class="modal-dialog">
                <div class="modal-header">
                    <div class="modal-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="modal-title">确认操作</h3>
                </div>
                <div class="modal-body">
                    <p class="modal-message">您确定要执行此操作吗？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="modal-btn modal-btn-cancel" data-action="cancel">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="modal-btn modal-btn-confirm" data-action="confirm">
                        <i class="fas fa-check"></i> 确定
                    </button>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // 绑定事件
        this.bindEvents();
    }

    bindEvents() {
        const modal = document.getElementById('customModal');
        const cancelBtn = modal.querySelector('[data-action="cancel"]');
        const confirmBtn = modal.querySelector('[data-action="confirm"]');

        // 取消按钮
        cancelBtn.addEventListener('click', () => {
            this.hide(false);
        });

        // 确认按钮
        confirmBtn.addEventListener('click', () => {
            this.hide(true);
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentModal) {
                this.hide(false);
            }
        });
    }

    show(options = {}) {
        return new Promise((resolve) => {
            const modal = document.getElementById('customModal');
            const icon = modal.querySelector('.modal-icon');
            const title = modal.querySelector('.modal-title');
            const message = modal.querySelector('.modal-message');
            const confirmBtn = modal.querySelector('[data-action="confirm"]');

            // 设置弹窗内容
            title.textContent = options.title || '确认操作';
            message.textContent = options.message || '您确定要执行此操作吗？';

            // 设置图标和样式
            const iconClass = this.getIconClass(options.type);
            const iconElement = icon.querySelector('i');
            
            // 清除旧的图标类
            iconElement.className = '';
            iconElement.className = iconClass;
            
            // 设置图标容器样式
            icon.className = `modal-icon ${options.type || 'warning'}`;

            // 设置确认按钮样式
            confirmBtn.className = `modal-btn ${this.getConfirmButtonClass(options.type)}`;
            confirmBtn.innerHTML = `<i class="${this.getConfirmIconClass(options.type)}"></i> ${options.confirmText || '确定'}`;

            // 显示弹窗
            modal.classList.add('active');
            this.currentModal = resolve;

            // 焦点管理
            setTimeout(() => {
                confirmBtn.focus();
            }, 300);
        });
    }

    hide(result) {
        const modal = document.getElementById('customModal');
        modal.classList.remove('active');
        
        if (this.currentModal) {
            this.currentModal(result);
            this.currentModal = null;
        }
    }

    getIconClass(type) {
        const iconMap = {
            'warning': 'fas fa-exclamation-triangle',
            'danger': 'fas fa-trash-alt',
            'success': 'fas fa-check-circle',
            'info': 'fas fa-info-circle',
            'question': 'fas fa-question-circle'
        };
        return iconMap[type] || iconMap['warning'];
    }

    getConfirmButtonClass(type) {
        const buttonMap = {
            'warning': 'modal-btn-confirm',
            'danger': 'modal-btn-danger',
            'success': 'modal-btn-success',
            'info': 'modal-btn-confirm'
        };
        return buttonMap[type] || buttonMap['warning'];
    }

    getConfirmIconClass(type) {
        const iconMap = {
            'warning': 'fas fa-check',
            'danger': 'fas fa-trash-alt',
            'success': 'fas fa-check',
            'info': 'fas fa-check'
        };
        return iconMap[type] || iconMap['warning'];
    }

    // 覆盖原生confirm函数
    overrideNativeConfirm() {
        // 保存原生confirm函数的引用
        window.originalConfirm = window.confirm;
        
        // 替换confirm函数
        window.confirm = (message) => {
            // 分析消息内容，确定弹窗类型
            const type = this.detectMessageType(message);
            
            return this.show({
                title: this.getTitle(type, message),
                message: message,
                type: type,
                confirmText: this.getConfirmText(type),
            });
        };
    }

    detectMessageType(message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('删除') || lowerMessage.includes('delete')) {
            return 'danger';
        } else if (lowerMessage.includes('保存') || lowerMessage.includes('save')) {
            return 'success';
        } else if (lowerMessage.includes('退出') || lowerMessage.includes('logout')) {
            return 'warning';
        } else {
            return 'warning';
        }
    }

    getTitle(type, message) {
        const titleMap = {
            'danger': '删除确认',
            'success': '保存确认',
            'warning': '操作确认',
            'info': '信息提示'
        };

        // 根据消息内容自定义标题
        if (message.includes('删除')) {
            return '删除确认';
        } else if (message.includes('保存')) {
            return '保存确认';
        } else if (message.includes('退出')) {
            return '退出确认';
        } else if (message.includes('清空')) {
            return '清空确认';
        }

        return titleMap[type] || '操作确认';
    }

    getConfirmText(type) {
        const textMap = {
            'danger': '删除',
            'success': '保存',
            'warning': '确定',
            'info': '确定'
        };
        return textMap[type] || '确定';
    }
}

// 自定义弹窗函数
window.showModal = function(options) {
    if (!window.customModalInstance) {
        window.customModalInstance = new CustomModal();
    }
    return window.customModalInstance.show(options);
};

// 简化的弹窗函数
window.showConfirm = function(message, type = 'warning') {
    return window.showModal({
        message: message,
        type: type
    });
};

window.showDeleteConfirm = function(message = '确定要删除这个项目吗？') {
    return window.showModal({
        title: '删除确认',
        message: message,
        type: 'danger',
        confirmText: '删除'
    });
};

// 添加样式
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        #customModal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            opacity: 0;
            visibility: hidden;
            z-index: 9999;
            width: 90%;
            max-width: 450px;
            background: rgba(30, 30, 50, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            color: white;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }
        
        #customModal.active {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            visibility: visible;
        }
        
        .modal-header {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.05);
        }
        
        .modal-icon {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            animation: modal-icon-pulse 1.5s infinite;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
        }
        
        .modal-icon.warning {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
        }
        
        .modal-icon.danger {
            background: linear-gradient(135deg, #f87171, #ef4444);
        }
        
        .modal-icon.success {
            background: linear-gradient(135deg, #4ade80, #22c55e);
        }
        
        .modal-icon.info {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
        }
        
        @keyframes modal-icon-pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.9; }
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin: 0;
            flex-grow: 1;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }
        
        .modal-body {
            padding: 25px 20px;
        }
        
        .modal-message {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.95);
            margin: 0;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            padding: 15px 20px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
        }
        
        .modal-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 10px 22px;
            border: none;
            border-radius: 10px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .modal-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .modal-btn:hover::before {
            left: 0;
        }
        
        .modal-btn-confirm {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            color: white;
        }
        
        .modal-btn-confirm:hover {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(59, 130, 246, 0.4);
        }
        
        .modal-btn-cancel {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .modal-btn-cancel:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        }
        
        .modal-btn-danger {
            background: linear-gradient(135deg, #f87171, #ef4444);
            color: white;
        }
        
        .modal-btn-danger:hover {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(239, 68, 68, 0.4);
        }
        
        .modal-btn-success {
            background: linear-gradient(135deg, #4ade80, #22c55e);
            color: white;
        }
        
        .modal-btn-success:hover {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(34, 197, 94, 0.4);
        }
        
        @media (max-width: 768px) {
            #customModal {
                width: 95%;
                max-width: 400px;
            }
            
            .modal-header,
            .modal-body,
            .modal-footer {
                padding: 15px;
            }
            
            .modal-footer {
                flex-direction: row;
                flex-wrap: wrap;
            }
            
            .modal-btn {
                flex: 1;
                min-width: 120px;
            }
        }
    `;
    document.head.appendChild(style);
});

// 添加事件监听器，防止事件冒泡
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('customModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
    
    // 初始化自定义弹窗
    if (!window.customModalInstance) {
        window.customModalInstance = new CustomModal();
    }
});

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CustomModal;
} 