<?php
/**
 * 心跳接口
 * 用于保持连接活跃和监控客户端状态
 * 
 * @version 1.0.0
 */

// 定义API访问常量
define('API_ACCESS', true);

// 错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/logs/api_errors.log');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 加载必要的文件
require_once __DIR__ . '/api_base.php';
require_once __DIR__ . '/config.php';

/**
 * 心跳处理类
 */
class HeartbeatHandler extends ApiBase {
    /**
     * 处理心跳请求
     */
    public function handleHeartbeat() {
        // 检查请求方法
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求数据
        $requestData = $this->request;
        
        // 检查是否为加密数据
        if (isset($requestData['encrypted']) && $requestData['encrypted']) {
            // 解密数据
            $decryptedData = $this->decryptData($requestData['data']);
            if ($decryptedData === null) {
                return $this->respondError('Invalid encrypted data', 400);
            }
            $requestData = $decryptedData;
        }
        
        // 获取必要参数
        $key = $requestData['key'] ?? '';
        $timestamp = $requestData['timestamp'] ?? '';
        $signature = $requestData['signature'] ?? '';
        $clientInfo = $requestData['client_info'] ?? [];
        
        // 验证必要参数
        if (empty($key)) {
            return $this->respondError('Missing required parameters', 400);
        }
        
        // 验证时间戳
        if (!empty($timestamp) && !$this->verifyTimestamp($timestamp)) {
            return $this->respondError('Invalid timestamp', 400, 'INVALID_TIMESTAMP');
        }
        
        // 验证签名
        if (!empty($signature) && !$this->verifySignature($requestData, $timestamp, $signature)) {
            return $this->respondError('Invalid signature', 400, 'INVALID_SIGNATURE');
        }
        
        // 处理心跳
        try {
            // 验证卡密
            $stmt = $this->db->prepare("
                SELECT * FROM license_keys 
                WHERE key_value = ? AND status = 'active' AND expiry_date > NOW()
            ");
            $stmt->execute([$key]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                return $this->respondError('Invalid license key', 403);
            }
            
            // 更新最后心跳时间
            $stmt = $this->db->prepare("
                UPDATE license_keys 
                SET last_heartbeat_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$keyData['id']]);
            
            // 记录心跳日志
            $stmt = $this->db->prepare("
                INSERT INTO heartbeat_logs 
                (key_id, ip_address, user_agent, client_info, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $keyData['id'],
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode($clientInfo)
            ]);
            
            // 生成新的安全令牌
            $securityToken = bin2hex(random_bytes(16));
            
            // 准备响应数据
            $responseData = [
                'success' => true,
                'message' => '心跳成功',
                'security_token' => $securityToken
            ];
            
            // 加密响应数据
            if ($this->config->get('encryption_enabled')) {
                $encryptedData = $this->encryptData($responseData);
                return $this->respondSuccess([
                    'encrypted' => true,
                    'version' => $this->config->get('encryption_version', 'v2'),
                    'data' => $encryptedData
                ]);
            } else {
                return $this->respondSuccess($responseData);
            }
            
        } catch (PDOException $e) {
            error_log("心跳处理失败: " . $e->getMessage());
            return $this->respondError('Server error', 500);
        }
    }

    /**
     * 加密数据
     */
    private function encryptData($data) {
        if (is_array($data)) {
            $data = json_encode($data);
        }
        
        // 使用base64编码（实际项目中应使用更安全的加密方式）
        return base64_encode($data);
    }

    /**
     * 解密数据
     */
    private function decryptData($encryptedData) {
        try {
            // 解密数据（实际项目中应使用更安全的解密方式）
            $data = base64_decode($encryptedData);
            return json_decode($data, true);
        } catch (Exception $e) {
            error_log("解密数据失败: " . $e->getMessage());
            return null;
        }
    }
}

// 创建心跳处理器实例并处理请求
$handler = new HeartbeatHandler();
$handler->handleHeartbeat(); 