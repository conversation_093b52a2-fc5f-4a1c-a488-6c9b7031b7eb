<?php
/**
 * Mac M芯片更新API - 修复版
 * 基于Intel API的工作版本
 */

// 引入数据库配置
require_once __DIR__ . '/../includes/db.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理函数
function sendError($message, $code = 400, $data = null) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'data' => $data,
        'platform' => 'macos',
        'architecture' => 'm1',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'platform' => 'macos',
        'architecture' => 'm1',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取数据库连接
function getDatabase() {
    global $pdo, $connection_success;
    if (!$connection_success || !$pdo) {
        throw new Exception('数据库连接失败');
    }
    return $pdo;
}

// 版本号验证
function isValidVersion($version) {
    return preg_match('/^\d+\.\d+\.\d+$/', $version);
}

// 版本比较
function compareVersionsRobust($version1, $version2) {
    return version_compare($version1, $version2, '>');
}

// 记录访问日志
function logAccess($pdo, $action, $version, $userAgent = '', $extra = []) {
    // 简化的日志记录
    error_log("M芯片API - {$action}: {$version} - {$userAgent}");
}

// 检测Mac架构
function detectMacArchitecture($userAgent) {
    $ua = strtolower($userAgent);
    
    // M芯片特征检测
    $m1Indicators = ['arm64', 'apple silicon', 'arm64_64'];
    
    $confidence = 0;
    foreach ($m1Indicators as $indicator) {
        if (strpos($ua, $indicator) !== false) {
            $confidence += 30;
        }
    }
    
    // macOS版本推断
    if (preg_match('/mac os x (\d+)_(\d+)/', $ua, $matches)) {
        $major = intval($matches[1]);
        if ($major >= 11) {
            $confidence += 20;
        }
    }
    
    return [
        'is_likely_m1' => $confidence >= 30,
        'confidence' => $confidence,
        'user_agent' => $userAgent
    ];
}

/**
 * 处理M芯片更新检查
 */
function handleMacM1UpdateCheck($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // 检测架构
    $detection = detectMacArchitecture($userAgent);
    
    // 记录访问日志
    logAccess($pdo, 'check_update', $currentVersion, $userAgent);
    
    try {
        // 获取最新发布的版本（必须有M芯片下载链接）
        $stmt = $pdo->prepare("
            SELECT *, version as version, title as title, description as description,
                   dmg_m1_download_url as dmg_m1_download_url
            FROM app_updates
            WHERE status = 'published'
            AND dmg_m1_download_url IS NOT NULL
            AND dmg_m1_download_url != ''
            AND TRIM(dmg_m1_download_url) != ''
            AND (platform = 'macos' OR platform = 'all')
            AND version IS NOT NULL
            AND version != ''
            AND TRIM(version) != ''
            AND version REGEXP '^[0-9]+\.[0-9]+\.[0-9]+$'
            ORDER BY version DESC, created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $latestUpdate = $stmt->fetch();

        if (!$latestUpdate) {
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'latest_version' => $currentVersion,
                'message' => '暂无Mac M芯片版本可用更新',
                'architecture_detection' => $detection
            ], '无可用更新');
            return;
        }

        // 验证版本号格式
        if (!isValidVersion($latestUpdate['version'])) {
            error_log("macOS M芯片API发现无效版本号: " . $latestUpdate['version']);
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'latest_version' => $currentVersion,
                'message' => '版本信息异常，请联系管理员',
                'architecture_detection' => $detection
            ], '版本信息异常');
            return;
        }
        
        // 版本比较
        $hasUpdate = compareVersionsRobust($latestUpdate['version'], $currentVersion);
        
        // 如果版本号完全相同，强制返回无更新
        if ($latestUpdate['version'] === $currentVersion) {
            $hasUpdate = false;
        }
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $latestUpdate['version'],
            'architecture_detection' => $detection
        ];
        
        if ($hasUpdate) {
            // 获取下载链接
            $downloadUrl = $latestUpdate['dmg_m1_download_url'];
            
            if (!$downloadUrl) {
                sendError('Mac M芯片安装包下载链接不可用', 404);
                return;
            }
            
            // 记录发现更新日志
            logAccess($pdo, 'found_update', $currentVersion);
            
            $response['update_info'] = [
                'id' => $latestUpdate['id'],
                'title' => $latestUpdate['title'] ?? "Mac M芯片版本 {$latestUpdate['version']}",
                'description' => $latestUpdate['description'] ?? '新版本更新',
                'force_update' => (bool)($latestUpdate['force_update'] ?? false),
                'download_url' => $downloadUrl,
                'version' => $latestUpdate['version'],
                'created_at' => $latestUpdate['created_at'],
                'architecture' => 'm1'
            ];
            
            // 如果检测到不是M芯片，给出警告
            if (!$detection['is_likely_m1']) {
                $response['warning'] = '检测到您可能不是M芯片Mac，此版本专为M芯片优化，如遇问题请使用Intel版本';
            }
        }
        
        sendSuccess($response, $hasUpdate ? '发现Mac M芯片新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        error_log('Mac M芯片更新检查失败: ' . $e->getMessage());
        error_log('错误详情: ' . $e->getTraceAsString());
        
        // 检查是否是数据库连接问题
        if (strpos($e->getMessage(), 'SQLSTATE') !== false) {
            sendError('数据库连接失败，请稍后重试', 503);
        } else {
            sendError('检查更新失败: ' . $e->getMessage(), 500);
        }
    }
}

// 主逻辑
try {
    $pdo = getDatabase();
    
    $action = $_GET['action'] ?? 'check';
    
    switch ($action) {
        case 'check':
            handleMacM1UpdateCheck($pdo);
            break;
        default:
            sendError('不支持的操作');
    }
    
} catch (Exception $e) {
    error_log('M芯片API初始化失败: ' . $e->getMessage());
    sendError('服务暂时不可用，请稍后重试', 503);
}
?>
