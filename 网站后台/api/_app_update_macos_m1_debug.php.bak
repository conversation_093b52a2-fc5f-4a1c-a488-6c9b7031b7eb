<?php
/**
 * Mac M芯片 APP更新独立API接口 - 彻底重写版本
 * 使用最简单的数据库查询避免字段名问题
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'platform' => 'macos',
        'architecture' => 'm1',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'platform' => 'macos',
        'architecture' => 'm1',
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 直接连接MySQL数据库
function getDatabase() {
    try {
        $pdo = new PDO(
            "mysql:host=127.0.0.1;port=3306;dbname=xiaomeihuakefu_c;charset=utf8mb4",
            "xiaomeihuakefu_c",
            "7Da5F1Xx995cxYz8",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_TIMEOUT => 10
            ]
        );
        
        // 设置时区
        $pdo->exec("SET time_zone = '+08:00'");
        
        return $pdo;
    } catch (Exception $e) {
        sendError('数据库连接失败: ' . $e->getMessage(), 503);
    }
}

/**
 * 检查Mac M芯片更新 - 简化版本
 */
function handleMacM1UpdateCheck($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    
    try {
        // 首先检查表结构
        $stmt = $pdo->query("DESCRIBE app_updates");
        $fields = $stmt->fetchAll();
        $field_names = array_column($fields, 'Field');
        
        error_log('Available database fields: ' . implode(', ', $field_names));
        
        // 使用最简单的查询
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE status = 'published' LIMIT 5");
        $stmt->execute();
        $all_updates = $stmt->fetchAll();
        
        error_log('Sample data: ' . json_encode($all_updates));
        
        if (empty($all_updates)) {
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'latest_version' => $currentVersion,
                'message' => '暂无Mac M芯片版本可用更新',
                'debug_info' => [
                    'available_fields' => $field_names,
                    'total_records' => 0
                ]
            ], '无可用更新');
        }
        
        // 查找有效的版本字段
        $sample = $all_updates[0];
        $version_field = null;
        $possible_version_fields = ['version', 'app_version', 'version_name', 'title'];
        
        foreach ($possible_version_fields as $field) {
            if (isset($sample[$field]) && !empty($sample[$field])) {
                $version_field = $field;
                break;
            }
        }
        
        if (!$version_field) {
            sendError('无法找到版本字段', 500);
        }
        
        error_log('Using version field: ' . $version_field);
        
        // 查找最新版本
        $latest_version = $sample[$version_field] ?? '1.0.0';
        
        // 简单的版本比较
        $hasUpdate = version_compare($latest_version, $currentVersion, '>');
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $latest_version,
            'debug_info' => [
                'available_fields' => $field_names,
                'used_version_field' => $version_field,
                'sample_data' => $sample
            ]
        ];
        
        if ($hasUpdate) {
            $response['update_info'] = [
                'id' => $sample['id'],
                'title' => $sample['title'] ?? $sample['version_name'] ?? 'Mac M芯片版本',
                'description' => $sample['description'] ?? $sample['version_description'] ?? '新版本更新',
                'download_url' => $sample['macos_m1_download_url'] ?? 'https://example.com/update.dmg',
                'architecture' => 'm1'
            ];
        }
        
        sendSuccess($response, $hasUpdate ? '发现Mac M芯片新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        error_log('Mac M芯片更新检查失败: ' . $e->getMessage());
        error_log('错误详情: ' . $e->getTraceAsString());
        sendError('检查更新失败: ' . $e->getMessage(), 500);
    }
}

// 获取数据库连接
$pdo = getDatabase();

// API路由处理
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? 'check';

switch ($method) {
    case 'GET':
        switch ($action) {
            case 'check':
                handleMacM1UpdateCheck($pdo);
                break;
            case 'info':
                sendSuccess([
                    'api_name' => 'Mac M芯片 APP更新API - 调试版本',
                    'version' => '1.0.0',
                    'platform' => 'macos',
                    'architecture' => 'm1'
                ], 'Mac M芯片更新API调试版本就绪');
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    default:
        sendError('仅支持GET请求', 405);
}

/**
 * 强化版本比较函数
 */
function compareVersionsRobust($version1, $version2) {
    $v1 = normalizeVersion($version1);
    $v2 = normalizeVersion($version2);
    
    if ($v1 === $v2) {
        return false;
    }
    
    return version_compare($v1, $v2, '>');
}

/**
 * 标准化版本号格式
 */
function normalizeVersion($version) {
    if (empty($version)) {
        return '0.0.0';
    }
    
    $clean = preg_replace('/^v/', '', trim($version));
    $parts = explode('.', $clean);
    
    while (count($parts) < 3) {
        $parts[] = '0';
    }
    
    $parts = array_map(function($part) {
        return preg_replace('/[^0-9]/', '', $part) ?: '0';
    }, $parts);
    
    $parts = array_slice($parts, 0, 3);
    
    return implode('.', $parts);
}
?>