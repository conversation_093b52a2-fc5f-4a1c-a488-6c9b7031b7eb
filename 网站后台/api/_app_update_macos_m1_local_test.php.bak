<?php
/**
 * 本地测试版Mac M1 APP更新API
 * 使用简化的模拟数据来测试更新功能
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'platform' => 'macos',
        'architecture' => 'm1',
        'environment' => 'local_test',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'platform' => 'macos',
        'architecture' => 'm1',
        'environment' => 'local_test',
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function compareVersions($version1, $version2) {
    return version_compare($version1, $version2, '>');
}

function handleMacM1UpdateCheck() {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    
    // 模拟数据库中的版本信息
    $mockVersions = [
        [
            'id' => 1,
            'version' => '3.0.0',
            'title' => '小梅花AI智能客服 v3.0.0',
            'description' => '当前稳定版本',
            'status' => 'published',
            'platform' => 'all',
            'force_update' => false,
            'macos_download_url' => null,
            'created_at' => '2025-08-04 09:00:00'
        ],
        [
            'id' => 2,
            'version' => '4.0.0',
            'title' => '小梅花AI智能客服 v4.0.0',
            'description' => '新版本更新，优化了性能和用户体验',
            'status' => 'published',
            'platform' => 'macos',
            'force_update' => true,
            'macos_download_url' => 'https://xiaomeihuakefu.cn/lanzou_api.php?url=https%3A%2F%2Fwwke.lanzoue.com%2FiLkyG32kxuhg&type=down',
            'release_notes' => '修复了循环更新问题，优化了更新检测逻辑',
            'created_at' => '2025-08-04 08:30:00'
        ]
    ];
    
    // 查找最新的Mac版本
    $latestUpdate = null;
    foreach ($mockVersions as $version) {
        if ($version['status'] === 'published' && 
            ($version['platform'] === 'macos' || $version['platform'] === 'all') &&
            $version['macos_download_url'] !== null) {
            if (!$latestUpdate || compareVersions($version['version'], $latestUpdate['version'])) {
                $latestUpdate = $version;
            }
        }
    }
    
    if (!$latestUpdate) {
        sendSuccess([
            'has_update' => false,
            'current_version' => $currentVersion,
            'latest_version' => $currentVersion,
            'message' => '暂无Mac M芯片版本可用更新'
        ], '无可用更新');
    }
    
    // 版本比较
    $hasUpdate = compareVersions($latestUpdate['version'], $currentVersion);
    
    $response = [
        'has_update' => $hasUpdate,
        'current_version' => $currentVersion,
        'latest_version' => $latestUpdate['version'],
        'test_info' => [
            'environment' => 'local_test',
            'mock_data' => true,
            'total_versions' => count($mockVersions)
        ]
    ];
    
    if ($hasUpdate) {
        $response['update_info'] = [
            'id' => $latestUpdate['id'],
            'title' => $latestUpdate['title'],
            'description' => $latestUpdate['description'],
            'force_update' => $latestUpdate['force_update'],
            'download_url' => $latestUpdate['macos_download_url'],
            'backup_url' => null,
            'architecture' => 'm1',
            'created_at' => $latestUpdate['created_at'],
            'release_notes' => $latestUpdate['release_notes'] ?? null
        ];
    }
    
    sendSuccess($response, $hasUpdate ? '发现Mac M芯片新版本' : '已是最新版本');
}

// API路由处理
$action = $_GET['action'] ?? 'check';

switch ($action) {
    case 'check':
        handleMacM1UpdateCheck();
        break;
    case 'info':
        sendSuccess([
            'api_name' => 'Mac M芯片 APP更新API - 本地测试版',
            'version' => '1.0.0',
            'platform' => 'macos',
            'architecture' => 'm1',
            'environment' => 'local_test',
            'endpoints' => [
                'check' => '检查更新 (?action=check&version=3.0.0)',
                'info' => '获取API信息 (?action=info)'
            ]
        ], 'Mac M芯片更新API本地测试版就绪');
        break;
    default:
        sendError('未知的操作类型');
}
?>