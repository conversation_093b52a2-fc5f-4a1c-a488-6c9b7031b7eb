<?php
/**
 * Mac M芯片 APP更新独立API接口
 * 专门为Mac M芯片用户提供更新检查和下载服务
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'platform' => 'macos',
        'architecture' => 'm1',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'platform' => 'macos',
        'architecture' => 'm1',
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 直接连接MySQL数据库
function getDatabase() {
    try {
        $pdo = new PDO(
            "mysql:host=127.0.0.1;port=3306;dbname=xiaomeihuakefu_c;charset=utf8mb4",
            "xiaomeihuakefu_c",
            "7Da5F1Xx995cxYz8",
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_TIMEOUT => 10
            ]
        );
        
        // 设置时区
        $pdo->exec("SET time_zone = '+08:00'");
        
        return $pdo;
    } catch (Exception $e) {
        sendError('服务暂时不可用，请稍后重试', 503);
    }
}

// 记录访问日志
function logAccess($pdo, $action, $version = null, $userAgent = null, $extra_data = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO update_access_logs 
            (platform, action, client_version, user_agent, ip_address, extra_data, access_time) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            'macos_m1',
            $action,
            $version,
            $userAgent ?: $_SERVER['HTTP_USER_AGENT'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $extra_data ? json_encode($extra_data) : null
        ]);
    } catch (Exception $e) {
        error_log('记录访问日志失败: ' . $e->getMessage());
    }
}

// 检测用户是否真的是M芯片Mac
function detectMacArchitecture($userAgent) {
    $ua = strtolower($userAgent);
    
    // M芯片特征检测
    $m1Indicators = [
        'arm64',
        'apple silicon',
        'arm64_64'
    ];
    
    $confidence = 0;
    foreach ($m1Indicators as $indicator) {
        if (strpos($ua, $indicator) !== false) {
            $confidence += 30;
        }
    }
    
    // macOS版本推断
    if (preg_match('/mac os x (\d+)_(\d+)/', $ua, $matches)) {
        $major = intval($matches[1]);
        $minor = intval($matches[2]);
        
        // macOS 11.0+ 更可能是M芯片
        if ($major >= 11) {
            $confidence += 20;
        }
    }
    
    return [
        'is_likely_m1' => $confidence >= 30,
        'confidence' => $confidence,
        'user_agent' => $userAgent
    ];
}

/**
 * 检查Mac M芯片更新
 */
function handleMacM1UpdateCheck($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

    // 检测架构
    $detection = detectMacArchitecture($userAgent);

    // 记录访问日志
    logAccess($pdo, 'check_update', $currentVersion, $userAgent);

    try {
        // 获取最新发布的版本（必须有M芯片下载链接）- 简化版本
        $stmt = $pdo->prepare("
            SELECT *, version as version, title as title, description as description,
                   dmg_m1_download_url as dmg_m1_download_url, NULL as dmg_m1_backup_url
            FROM app_updates
            WHERE status = 'published'
            AND dmg_m1_download_url IS NOT NULL
            AND dmg_m1_download_url != ''
            AND TRIM(dmg_m1_download_url) != ''
            AND (platform = 'macos' OR platform = 'all')
            AND version IS NOT NULL
            AND version != ''
            AND TRIM(version) != ''
            AND version REGEXP '^[0-9]+\.[0-9]+\.[0-9]+$'
            ORDER BY version DESC, created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $latestUpdate = $stmt->fetch();

        if (!$latestUpdate) {
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'latest_version' => $currentVersion,
                'message' => '暂无Mac M芯片版本可用更新',
                'architecture_detection' => $detection
            ], '无可用更新');
            return;
        }

        // 记录实际数据库字段用于调试
        error_log('Latest update data: ' . json_encode($latestUpdate));

        // 尝试从可能的字段中获取版本号
        $version_field = $latestUpdate['version'] ??
                        $latestUpdate['title'] ?? '1.0.0';

        // 验证版本号格式
        if (!isValidVersion($version_field)) {
            error_log("macOS M1 API发现无效版本号: " . $version_field);
            logAccess($pdo, 'invalid_version', $currentVersion, $userAgent, [
                'invalid_version' => $version_field,
                'raw_data' => $latestUpdate
            ]);
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'latest_version' => $currentVersion,
                'message' => '版本信息异常，请联系管理员',
                'architecture_detection' => $detection
            ], '版本信息异常');
        }
        
        // 增强版本比较逻辑
        $hasUpdate = compareVersionsRobust($version_field, $currentVersion);
        
        // 额外的安全检查：如果版本号完全相同，强制返回无更新
        if (normalizeVersion($version_field) === normalizeVersion($currentVersion)) {
            $hasUpdate = false;
            logAccess($pdo, 'same_version_detected', $currentVersion, $userAgent, [
                'latest_version' => $version_field,
                'normalized_current' => normalizeVersion($currentVersion),
                'normalized_latest' => normalizeVersion($version_field)
            ]);
        }
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $version_field,
            'architecture_detection' => $detection,
            'version_comparison_result' => [
                'normalized_current' => normalizeVersion($currentVersion),
                'normalized_latest' => normalizeVersion($version_field),
                'comparison_method' => 'robust'
            ]
        ];
        
        if ($hasUpdate) {
            // 优先使用M芯片直链，备用链接作为fallback
            $downloadUrl = $latestUpdate['dmg_m1_download_url'] ?: $latestUpdate['dmg_m1_backup_url'];
            $backupUrl = $latestUpdate['dmg_m1_backup_url'];
            
            $response['update_info'] = [
                'id' => $latestUpdate['id'],
                'title' => $latestUpdate['title'] ?? "Mac M芯片版本 {$version_field}",
                'description' => $latestUpdate['description'] ?? '新版本更新',
                'force_update' => (bool)($latestUpdate['force_update'] ?? false),
                'download_url' => $downloadUrl,
                'backup_url' => $backupUrl,
                'architecture' => 'm1',
                'file_size' => null, // 可以后续添加文件大小检测
                'created_at' => $latestUpdate['created_at'],
                'release_notes' => $latestUpdate['release_notes']
            ];
            
            // 记录发现更新日志
            logAccess($pdo, 'found_update', $currentVersion);
            
            // 如果检测到不是M芯片，给出警告
            if (!$detection['is_likely_m1']) {
                $response['warning'] = '检测到您可能不是M芯片Mac，此版本专为M芯片优化，如遇问题请使用Intel版本';
            }
        }
        
        sendSuccess($response, $hasUpdate ? '发现Mac M芯片新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        error_log('Mac M芯片更新检查失败: ' . $e->getMessage());
        error_log('错误详情: ' . $e->getTraceAsString());
        error_log('请求参数: ' . json_encode($_GET));
        error_log('用户代理: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'));

        // 检查是否是数据库连接问题
        if (strpos($e->getMessage(), 'SQLSTATE') !== false) {
            sendError('数据库连接失败，请稍后重试', 503);
        } else {
            sendError('检查更新失败: ' . $e->getMessage(), 500);
        }
    }
}

/**
 * 获取Mac M芯片下载链接
 */
function handleMacM1Download($pdo) {
    $updateId = $_GET['update_id'] ?? null;
    $version = $_GET['version'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    if (!$updateId && !$version) {
        sendError('缺少更新ID或版本号参数');
    }
    
    // 检测架构并记录警告
    $detection = detectMacArchitecture($userAgent);
    
    try {
        // 根据ID或版本获取更新信息
        if ($updateId) {
            $stmt = $pdo->prepare("
                SELECT *
                FROM app_updates 
                WHERE id = ? AND status = 'published'
                AND dmg_m1_download_url IS NOT NULL
            ");
            $stmt->execute([$updateId]);
        } else {
            $stmt = $pdo->prepare("
                SELECT *
                FROM app_updates 
                WHERE version = ? AND status = 'published'
                AND dmg_m1_download_url IS NOT NULL
                ORDER BY created_at DESC LIMIT 1
            ");
            $stmt->execute([$version]);
        }
        
        $update = $stmt->fetch();
        
        if (!$update) {
            sendError('Mac M芯片版本不存在或未发布', 404);
        }
        
        // 优先返回M芯片直链
        $downloadUrl = $update['dmg_m1_download_url'] ?: $update['dmg_m1_backup_url'];
        $backupUrl = $update['dmg_m1_backup_url'];
        
        if (!$downloadUrl) {
            sendError('Mac M芯片安装包下载链接不可用', 404);
        }
        
        // 更新下载统计
        $stmt = $pdo->prepare("UPDATE app_updates SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$update['id']]);
        
        // 获取版本字段
        $version_field = $update['version'] ?? $update['title'] ?? '1.0.0';
        $title_field = $update['title'] ?? $version_field;
        
        // 记录下载日志
        logAccess($pdo, 'download', $version_field, $userAgent);
        
        $response = [
            'download_url' => $downloadUrl,
            'backup_url' => $backupUrl,
            'version' => $version_field,
            'title' => $title_field,
            'architecture' => 'm1',
            'file_name' => 'XiaoMeiHua-macOS-M1-v' . $version_field . '.dmg',
            'download_count' => $update['download_count'] + 1,
            'architecture_detection' => $detection
        ];
        
        // 如果检测到不是M芯片，给出警告
        if (!$detection['is_likely_m1']) {
            $response['warning'] = '检测到您可能不是M芯片Mac，此版本专为M芯片优化';
        }
        
        // 如果请求直接下载，进行重定向
        if (isset($_GET['direct']) && $_GET['direct'] == '1') {
            header('Location: ' . $downloadUrl);
            exit;
        }
        
        sendSuccess($response, '获取Mac M芯片下载链接成功');
        
    } catch (Exception $e) {
        error_log('获取Mac M芯片下载链接失败: ' . $e->getMessage());
        error_log('错误详情: ' . $e->getTraceAsString());
        sendError('获取下载链接失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取Mac M芯片版本列表
 */
function handleMacM1VersionList($pdo) {
    try {
        $limit = min(20, max(1, intval($_GET['limit'] ?? 5)));
        
        $stmt = $pdo->prepare("
            SELECT *
            FROM app_updates 
            WHERE status = 'published' 
            AND dmg_m1_download_url IS NOT NULL
            AND (platform = 'macos' OR platform = 'all')
            ORDER BY id DESC, created_at DESC 
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $versions = $stmt->fetchAll();
        
        // 处理版本数据
        foreach ($versions as &$version) {
            $version['has_direct_link'] = !empty($version['dmg_m1_download_url']);
            $version['has_backup_link'] = !empty($version['dmg_m1_backup_url']);
            $version['download_url'] = $version['dmg_m1_download_url'] ?: $version['dmg_m1_backup_url'];
            $version['architecture'] = 'm1';
            
            // 移除敏感信息
            unset($version['dmg_m1_download_url']);
            unset($version['dmg_m1_backup_url']);
        }
        
        sendSuccess([
            'versions' => $versions,
            'total' => count($versions),
            'architecture' => 'm1'
        ], '获取Mac M芯片版本列表成功');
        
    } catch (Exception $e) {
        error_log('获取Mac M芯片版本列表失败: ' . $e->getMessage());
        error_log('错误详情: ' . $e->getTraceAsString());
        sendError('获取版本列表失败: ' . $e->getMessage(), 500);
    }
}

// 获取数据库连接
$pdo = getDatabase();

// 创建访问日志表（如果不存在）
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS update_access_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            platform ENUM('windows', 'macos_m1', 'macos_intel') NOT NULL,
            action VARCHAR(50) NOT NULL,
            client_version VARCHAR(50),
            user_agent TEXT,
            ip_address VARCHAR(45),
            extra_data TEXT,
            access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_platform_action (platform, action),
            INDEX idx_access_time (access_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // 更新表结构
    updateLogTableStructure($pdo);
} catch (Exception $e) {
    error_log('创建访问日志表失败: ' . $e->getMessage());
}

// API路由处理
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? 'check';

switch ($method) {
    case 'GET':
        switch ($action) {
            case 'check':
                handleMacM1UpdateCheck($pdo);
                break;
            case 'download':
                handleMacM1Download($pdo);
                break;
            case 'versions':
                handleMacM1VersionList($pdo);
                break;
            case 'info':
                // 返回API信息
                sendSuccess([
                    'api_name' => 'Mac M芯片 APP更新API',
                    'version' => '1.0.0',
                    'platform' => 'macos',
                    'architecture' => 'm1',
                    'target_devices' => ['MacBook Air M1/M2/M3', 'MacBook Pro M1/M2/M3', 'iMac M1/M3', 'Mac mini M1/M2', 'Mac Studio M1/M2', 'Mac Pro M2'],
                    'endpoints' => [
                        'check' => '检查更新 (?action=check&version=1.0.0)',
                        'download' => '获取下载链接 (?action=download&update_id=1 或 &version=1.0.1)',
                        'versions' => '获取版本列表 (?action=versions&limit=5)',
                        'info' => '获取API信息 (?action=info)'
                    ]
                ], 'Mac M芯片更新API就绪');
                break;
            default:
                sendError('未知的操作类型');
        }
        break;
        
    default:
        sendError('仅支持GET请求', 405);
}

/**
 * 强化版本比较函数 - 解决重复更新提示问题
 */
function compareVersionsRobust($version1, $version2) {
    // 标准化版本号
    $v1 = normalizeVersion($version1);
    $v2 = normalizeVersion($version2);
    
    // 如果版本号完全相同，返回false（无更新）
    if ($v1 === $v2) {
        return false;
    }
    
    // 使用PHP内置函数进行比较
    $result = version_compare($v1, $v2, '>');
    
    return $result;
}

/**
 * 标准化版本号格式
 */
function normalizeVersion($version) {
    if (empty($version)) {
        return '0.0.0';
    }
    
    // 移除可能的前缀（如 v1.0.0）
    $clean = preg_replace('/^v/', '', trim($version));
    
    // 确保版本号格式为 x.y.z
    $parts = explode('.', $clean);
    
    // 补齐缺失的部分
    while (count($parts) < 3) {
        $parts[] = '0';
    }
    
    // 确保每部分都是数字
    $parts = array_map(function($part) {
        return preg_replace('/[^0-9]/', '', $part) ?: '0';
    }, $parts);
    
    // 只取前三位
    $parts = array_slice($parts, 0, 3);
    
    return implode('.', $parts);
}

/**
 * 更新访问日志表结构
 */
function updateLogTableStructure($pdo) {
    try {
        // 检查是否需要添加extra_data列
        $result = $pdo->query("SHOW COLUMNS FROM update_access_logs LIKE 'extra_data'");
        if ($result->rowCount() === 0) {
            $pdo->exec("ALTER TABLE update_access_logs ADD COLUMN extra_data TEXT AFTER ip_address");
        }
    } catch (Exception $e) {
        error_log('更新日志表结构失败: ' . $e->getMessage());
    }
}

/**
 * 验证版本号格式
 */
function isValidVersion($version) {
    if (empty($version) || !is_string($version)) {
        return false;
    }

    // 移除可能的前缀
    $clean = preg_replace('/^v/', '', trim($version));

    // 检查是否符合 x.y.z 格式
    if (!preg_match('/^[0-9]+\.[0-9]+\.[0-9]+$/', $clean)) {
        return false;
    }

    // 检查每个部分是否为有效数字
    $parts = explode('.', $clean);
    foreach ($parts as $part) {
        if (!is_numeric($part) || intval($part) < 0) {
            return false;
        }
    }

    return true;
}

?>