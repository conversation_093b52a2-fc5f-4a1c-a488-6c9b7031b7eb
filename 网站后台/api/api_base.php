<?php
/**
 * API基础类
 * 实现API接口的基本功能，包括请求处理、认证、响应格式化等
 * 
 * @version 1.1.0
 */

// 防止直接访问
if (!defined('API_ACCESS')) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['error' => 'Access denied']);
    exit;
}

class ApiBase {
    protected $config;
    protected $db;
    protected $request;
    protected $response;
    protected $headers;
    protected $method;
    protected $endpoint;
    protected $params;
    protected $token;
    protected $isSecure = false;

    /**
     * 构造函数
     */
    public function __construct() {
        // 设置响应头为JSON
        header('Content-Type: application/json; charset=UTF-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-API-Version, X-Request-Timestamp, X-Request-Signature');
        
        // 如果是OPTIONS请求，直接返回200
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
        
        // 初始化请求数据
        $this->method = $_SERVER['REQUEST_METHOD'];
        $this->headers = $this->getRequestHeaders();
        $this->request = $this->getRequestData();
        
        // 加载配置
        $this->loadConfig();
        
        // 连接数据库
        $this->connectDatabase();
    }

    /**
     * 加载配置
     */
    protected function loadConfig() {
        // 加载API配置
        require_once dirname(__DIR__) . '/api/config.php';
        $this->config = new ApiConfig();
    }

    /**
     * 连接数据库
     */
    protected function connectDatabase() {
        try {
            // 使用配置中的数据库连接信息
            $dbConfig = $this->config->getDatabaseConfig();
            
            $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['dbname']};charset=utf8mb4";
            $this->db = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false // 强制使用真实的预处理语句，防止SQL注入
            ]);
            
            // 设置严格SQL模式
            $this->db->exec("SET SESSION sql_mode = 'STRICT_ALL_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
        } catch (PDOException $e) {
            $this->respondError('Database connection failed', 500);
        }
    }

    /**
     * 获取请求头
     */
    protected function getRequestHeaders() {
        $headers = [];
        foreach ($_SERVER as $key => $value) {
            if (substr($key, 0, 5) === 'HTTP_') {
                $header = str_replace(' ', '-', ucwords(str_replace('_', ' ', strtolower(substr($key, 5)))));
                $headers[$header] = $value;
            }
        }
        return $headers;
    }

    /**
     * 获取请求数据并进行安全过滤
     */
    protected function getRequestData() {
        $data = [];
        
        // 处理GET参数
        if ($this->method === 'GET') {
            // 获取GET数据并进行过滤
            $data = filter_input_array(INPUT_GET, FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?: [];
        }
        
        // 处理POST/PUT/DELETE参数
        if (in_array($this->method, ['POST', 'PUT', 'DELETE'])) {
            $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
            
            // 处理JSON数据
            if (strpos($contentType, 'application/json') !== false) {
                $input = file_get_contents('php://input');
                if (!empty($input)) {
                    $data = json_decode($input, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $this->respondError('Invalid JSON data', 400);
                    }
                    // 递归过滤JSON数据
                    $data = $this->sanitizeArrayData($data);
                }
            } 
            // 处理表单数据
            else if (strpos($contentType, 'application/x-www-form-urlencoded') !== false) {
                // 过滤POST数据
                $data = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?: [];
            }
        }
        
        return $data;
    }

    /**
     * 递归过滤数组数据
     */
    protected function sanitizeArrayData($data) {
        if (!is_array($data)) {
            return $this->sanitizeData($data);
        }
        
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->sanitizeArrayData($value);
            } else {
                $data[$key] = $this->sanitizeData($value);
            }
        }
        
        return $data;
    }

    /**
     * 过滤单个数据项
     */
    protected function sanitizeData($value) {
        if (is_string($value)) {
            // 移除危险字符
            $value = str_replace(['<script>', '</script>'], ['&lt;script&gt;', '&lt;/script&gt;'], $value);
            // 消除SQL注入常见攻击字符
            $value = str_replace(['\'', '"', ';', '--'], ['&#39;', '&quot;', '&#59;', '&#45;&#45;'], $value);
        }
        return $value;
    }

    /**
     * 验证请求签名
     */
    protected function verifySignature($data, $timestamp, $signature) {
        // 如果没有启用签名验证，直接返回true
        if (!$this->config->isSignatureValidationEnabled()) {
            return true;
        }
        
        // 获取API密钥
        $apiKey = $this->config->getApiSecretKey();
        
        // 计算签名
        $signatureBase = json_encode($data) . $timestamp . $apiKey;
        $expectedSignature = hash_hmac('sha256', $signatureBase, $apiKey);
        
        // 使用恒定时间比较防止时序攻击
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * 验证请求时间戳
     */
    protected function verifyTimestamp($timestamp) {
        // 如果没有启用时间戳验证，直接返回true
        if (!$this->config->isTimestampValidationEnabled()) {
            return true;
        }
        
        // 验证时间戳是否为数字
        if (!is_numeric($timestamp)) {
            return false;
        }
        
        // 获取时间戳有效期（秒）
        $timestampTtl = $this->config->getTimestampTtl();
        
        // 验证时间戳是否在有效期内
        $currentTime = time();
        $timeDiff = abs($currentTime - (int)$timestamp);
        
        return $timeDiff <= $timestampTtl;
    }

    /**
     * 验证API令牌
     */
    protected function verifyToken($token) {
        // 如果没有启用令牌验证，直接返回true
        if (!$this->config->isTokenValidationEnabled()) {
            return true;
        }
        
        // 验证令牌格式
        if (empty($token) || !is_string($token) || strlen($token) < 32) {
            return false;
        }
        
        // 验证令牌是否有效（使用参数化查询）
        try {
            $stmt = $this->db->prepare("SELECT * FROM api_tokens WHERE token = ? AND is_active = 1 AND expires_at > NOW()");
            $stmt->execute([$token]);
            $tokenData = $stmt->fetch();
            
            return !empty($tokenData);
        } catch (PDOException $e) {
            error_log("Token verification error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成API令牌
     */
    protected function generateToken($userId, $expiry = '+1 hour') {
        // 验证用户ID
        $userId = filter_var($userId, FILTER_VALIDATE_INT);
        if (!$userId) {
            return null;
        }
        
        // 生成令牌
        $tokenData = [
            'user_id' => $userId,
            'timestamp' => time(),
            'random' => bin2hex(random_bytes(16))
        ];
        
        // 使用JWT格式
        $token = $this->generateJWT($tokenData);
        
        // 计算过期时间
        $expiryDate = date('Y-m-d H:i:s', strtotime($expiry));
        
        // 保存令牌到数据库
        try {
            $stmt = $this->db->prepare("INSERT INTO api_tokens (user_id, token, expires_at, created_at) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$userId, $token, $expiryDate]);
            
            return $token;
        } catch (PDOException $e) {
            error_log("Token generation error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 生成JWT令牌
     */
    protected function generateJWT($payload) {
        // 获取API密钥
        $apiKey = $this->config->getApiSecretKey();
        
        // 创建头部
        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];
        
        // 编码头部和载荷
        $encodedHeader = $this->base64UrlEncode(json_encode($header));
        $encodedPayload = $this->base64UrlEncode(json_encode($payload));
        
        // 创建签名
        $signature = hash_hmac('sha256', "$encodedHeader.$encodedPayload", $apiKey, true);
        $encodedSignature = $this->base64UrlEncode($signature);
        
        // 返回JWT
        return "$encodedHeader.$encodedPayload.$encodedSignature";
    }

    /**
     * Base64Url编码
     */
    protected function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * 响应成功
     */
    protected function respondSuccess($data = [], $message = 'Success', $code = 200) {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
        
        http_response_code($code);
        echo json_encode($response);
        exit;
    }

    /**
     * 响应错误
     */
    protected function respondError($message = 'Error', $code = 400, $errorCode = '') {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if (!empty($errorCode)) {
            $response['error_code'] = $errorCode;
        }
        
        http_response_code($code);
        echo json_encode($response);
        exit;
    }

    /**
     * 记录API请求日志
     */
    protected function logApiRequest($endpoint, $userId = null, $status = 'success', $message = '') {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO api_logs (endpoint, user_id, ip_address, request_data, response_status, message, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            // 过滤要记录的数据，确保不记录敏感信息
            $filteredRequest = $this->request;
            if (isset($filteredRequest['password'])) {
                $filteredRequest['password'] = '******';
            }
            if (isset($filteredRequest['token'])) {
                $filteredRequest['token'] = substr($filteredRequest['token'], 0, 8) . '...';
            }
            
            $requestData = json_encode($filteredRequest);
            $ipAddress = $_SERVER['REMOTE_ADDR'];
            
            $stmt->execute([$endpoint, $userId, $ipAddress, $requestData, $status, $message]);
        } catch (PDOException $e) {
            // 日志记录失败不影响API响应
            error_log("API日志记录失败: " . $e->getMessage());
        }
    }
    
    /**
     * 安全地执行SQL查询
     * 
     * @param string $sql SQL查询语句（包含占位符）
     * @param array $params 查询参数数组
     * @return PDOStatement 查询结果
     */
    protected function safeQuery($sql, $params = []) {
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("SQL查询错误: " . $e->getMessage() . " [SQL: $sql]");
            $this->respondError('Database error', 500);
        }
    }
    
    /**
     * 安全地获取单行结果
     */
    protected function fetchOne($sql, $params = []) {
        $stmt = $this->safeQuery($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 安全地获取所有结果
     */
    protected function fetchAll($sql, $params = []) {
        $stmt = $this->safeQuery($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 安全地获取单个值
     */
    protected function fetchValue($sql, $params = []) {
        $stmt = $this->safeQuery($sql, $params);
        return $stmt->fetchColumn();
    }
} 