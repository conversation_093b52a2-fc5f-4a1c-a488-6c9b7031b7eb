<?php
/**
 * APP更新API接口 - 重构版
 * 支持外部下载链接，移除文件上传功能
 * 支持蓝奏云等外部链接的在线升级
 */

// 引入数据库配置
require_once __DIR__ . '/../includes/db.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 错误处理函数
function sendError($message, $code = 400, $data = null) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取数据库连接
function getDatabase() {
    global $pdo, $connection_success;

    if (!$connection_success || !$pdo) {
        throw new Exception('数据库连接不可用');
    }

    return $pdo;
}

// 初始化数据库表
function initializeTables($pdo) {
    try {
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'app_updates'");
        if ($stmt->rowCount() == 0) {
            // 执行迁移脚本
            $migrationFile = __DIR__ . '/../database_migration_app_updates.sql';
            if (file_exists($migrationFile)) {
                $sql = file_get_contents($migrationFile);
                $pdo->exec($sql);
            }
        }
    } catch (Exception $e) {
        error_log('初始化数据库表失败: ' . $e->getMessage());
    }
}

// 验证版本号格式
function validateVersion($version) {
    return preg_match('/^\d+\.\d+\.\d+$/', $version);
}

// 验证URL格式
function validateUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

// 记录版本检查日志
function logVersionCheck($pdo, $currentVersion, $latestVersion, $platform, $hasUpdate) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO app_version_checks 
            (current_version, latest_version, platform, ip_address, user_agent, has_update) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $currentVersion,
            $latestVersion,
            $platform,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $hasUpdate ? 1 : 0
        ]);
    } catch (Exception $e) {
        error_log('记录版本检查日志失败: ' . $e->getMessage());
    }
}

// 记录下载日志
function logDownload($pdo, $updateId, $platform, $downloadUrl) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO app_download_logs 
            (update_id, platform, ip_address, user_agent, download_url, download_status) 
            VALUES (?, ?, ?, ?, ?, 'started')
        ");
        $stmt->execute([
            $updateId,
            $platform,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $downloadUrl
        ]);
        
        // 更新下载计数
        $stmt = $pdo->prepare("UPDATE app_updates SET download_count = download_count + 1 WHERE id = ?");
        $stmt->execute([$updateId]);
    } catch (Exception $e) {
        error_log('记录下载日志失败: ' . $e->getMessage());
    }
}

// 获取数据库连接
$pdo = getDatabase();
initializeTables($pdo);

// 路由处理
$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['REQUEST_URI'];
$pathParts = explode('/', trim(parse_url($path, PHP_URL_PATH), '/'));

// API路由
switch ($method) {
    case 'GET':
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'check':
                    handleCheckUpdate($pdo);
                    break;
                case 'list':
                    handleListVersions($pdo);
                    break;
                case 'download':
                    handleDownload($pdo);
                    break;
                default:
                    sendError('未知的操作类型');
            }
        } else {
            // 默认检查更新
            handleCheckUpdate($pdo);
        }
        break;
        
    case 'POST':
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create':
                    handleCreateVersion($pdo);
                    break;
                case 'update':
                    handleUpdateVersion($pdo);
                    break;
                case 'delete':
                    handleDeleteVersion($pdo);
                    break;
                default:
                    sendError('未知的操作类型');
            }
        } else {
            sendError('缺少操作类型参数');
        }
        break;
        
    default:
        sendError('不支持的请求方法', 405);
}

/**
 * 检查更新
 */
function handleCheckUpdate($pdo) {
    $currentVersion = $_GET['version'] ?? '1.0.0';
    $platform = strtolower($_GET['platform'] ?? 'all');
    
    // 验证版本号
    if (!validateVersion($currentVersion)) {
        $currentVersion = '1.0.0';
    }
    
    // 验证平台
    if (!in_array($platform, ['windows', 'macos', 'linux', 'all'])) {
        $platform = 'all';
    }
    
    try {
        // 获取最新发布的版本 - 增强版本验证
        $stmt = $pdo->prepare("
            SELECT * FROM app_updates
            WHERE status = 'published'
            AND (platform = ? OR platform = 'all')
            AND version IS NOT NULL
            AND version != ''
            AND TRIM(version) != ''
            AND version REGEXP '^[0-9]+\.[0-9]+\.[0-9]+$'
            ORDER BY version DESC, created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$platform]);
        $latestUpdate = $stmt->fetch();

        if (!$latestUpdate) {
            // 记录检查日志
            logVersionCheck($pdo, $currentVersion, null, $platform, false);
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'message' => '暂无可用更新'
            ], '无可用更新');
        }

        // 验证版本号格式
        if (!isValidVersion($latestUpdate['version'])) {
            error_log("发现无效版本号: " . $latestUpdate['version']);
            sendSuccess([
                'has_update' => false,
                'current_version' => $currentVersion,
                'message' => '版本信息异常，请联系管理员'
            ], '版本信息异常');
        }

        // 比较版本号
        $hasUpdate = version_compare($latestUpdate['version'], $currentVersion, '>');
        
        // 准备下载链接
        $downloadUrls = [];
        if ($hasUpdate) {
            if ($latestUpdate['exe_download_url']) {
                $downloadUrls['windows'] = $latestUpdate['exe_download_url'];
            }
            
            // 处理macOS下载链接 - 支持新的架构特定字段
            if ($latestUpdate['dmg_download_url']) {
                $downloadUrls['macos'] = $latestUpdate['dmg_download_url'];
            } elseif ($latestUpdate['dmg_m1_download_url']) {
                $downloadUrls['macos'] = $latestUpdate['dmg_m1_download_url'];
            } elseif ($latestUpdate['dmg_intel_download_url']) {
                $downloadUrls['macos'] = $latestUpdate['dmg_intel_download_url'];
            }
            
            // 如果有架构特定的链接，提供详细信息
            if ($latestUpdate['dmg_m1_download_url'] || $latestUpdate['dmg_intel_download_url']) {
                $downloadUrls['macos_m1'] = $latestUpdate['dmg_m1_download_url'];
                $downloadUrls['macos_intel'] = $latestUpdate['dmg_intel_download_url'];
            }
        }
        
        // 记录检查日志
        logVersionCheck($pdo, $currentVersion, $latestUpdate['version'], $platform, $hasUpdate);
        
        $response = [
            'has_update' => $hasUpdate,
            'current_version' => $currentVersion,
            'latest_version' => $latestUpdate['version']
        ];
        
        if ($hasUpdate) {
            $response['update_info'] = [
                'id' => $latestUpdate['id'],
                'title' => $latestUpdate['title'],
                'description' => $latestUpdate['description'],
                'force_update' => (bool)$latestUpdate['force_update'],
                'download_urls' => $downloadUrls,
                'release_notes' => $latestUpdate['release_notes']
            ];
        }
        
        sendSuccess($response, $hasUpdate ? '发现新版本' : '已是最新版本');
        
    } catch (Exception $e) {
        sendError('检查更新失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 创建新版本
 */
function handleCreateVersion($pdo) {
    // 验证必需参数
    $version = trim($_POST['version'] ?? '');
    $title = trim($_POST['title'] ?? '');
    $description = trim($_POST['description'] ?? '');
    
    if (empty($version) || empty($title) || empty($description)) {
        sendError('版本号、标题和描述不能为空');
    }
    
    // 验证版本号格式
    if (!validateVersion($version)) {
        sendError('版本号格式不正确，应为：主版本号.次版本号.修订号');
    }
    
    // 获取下载链接
    $exeUrl = trim($_POST['exe_download_url'] ?? '');
    $dmgUrl = trim($_POST['dmg_download_url'] ?? '');

    // 至少需要一个下载链接（Windows或macOS）
    if (empty($exeUrl) && empty($dmgUrl)) {
        sendError('至少需要提供一个下载链接（Windows或macOS）');
    }
    
    // 验证URL格式
    if (!empty($exeUrl) && !validateUrl($exeUrl)) {
        sendError('Windows下载链接格式不正确');
    }
    
    if (!empty($dmgUrl) && !validateUrl($dmgUrl)) {
        sendError('macOS下载链接格式不正确');
    }
    
    try {
        // 检查版本是否已存在
        $stmt = $pdo->prepare("SELECT id FROM app_updates WHERE version = ?");
        $stmt->execute([$version]);
        if ($stmt->fetch()) {
            sendError('版本号已存在，请使用不同的版本号');
        }
        
        // 插入新版本
        $stmt = $pdo->prepare("
            INSERT INTO app_updates
            (version, title, description, exe_download_url, dmg_download_url,
             force_update, status, platform)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $success = $stmt->execute([
            $version,
            $title,
            $description,
            $exeUrl ?: null,
            $dmgUrl ?: null,
            isset($_POST['force_update']) ? 1 : 0,
            $_POST['status'] ?? 'draft',
            $_POST['platform'] ?? 'all'
        ]);
        
        if ($success) {
            $newId = $pdo->lastInsertId();
            
            // 获取创建的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$newId]);
            $newVersion = $stmt->fetch();
            
            sendSuccess($newVersion, '版本创建成功');
        } else {
            sendError('版本创建失败', 500);
        }
        
    } catch (Exception $e) {
        sendError('创建版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取版本列表
 */
function handleListVersions($pdo) {
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = max(1, min(100, intval($_GET['limit'] ?? 10)));
        $offset = ($page - 1) * $limit;

        $status = $_GET['status'] ?? '';
        $platform = $_GET['platform'] ?? '';

        // 构建查询条件
        $where = [];
        $params = [];

        if (!empty($status) && in_array($status, ['draft', 'published'])) {
            $where[] = "status = ?";
            $params[] = $status;
        }

        if (!empty($platform) && in_array($platform, ['all', 'windows', 'macos', 'linux'])) {
            $where[] = "(platform = ? OR platform = 'all')";
            $params[] = $platform;
        }

        $whereClause = empty($where) ? '' : 'WHERE ' . implode(' AND ', $where);

        // 获取总数
        $countSql = "SELECT COUNT(*) FROM app_updates $whereClause";
        $stmt = $pdo->prepare($countSql);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();

        // 获取列表
        $listSql = "SELECT * FROM app_updates $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
        $stmt = $pdo->prepare($listSql);
        $stmt->execute($params);
        $versions = $stmt->fetchAll();

        sendSuccess([
            'versions' => $versions,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ], '获取版本列表成功');

    } catch (Exception $e) {
        sendError('获取版本列表失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新版本信息
 */
function handleUpdateVersion($pdo) {
    $id = intval($_POST['id'] ?? 0);
    if ($id <= 0) {
        sendError('无效的版本ID');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();

        if (!$version) {
            sendError('版本不存在', 404);
        }

        // 准备更新数据
        $updateFields = [];
        $updateParams = [];

        if (isset($_POST['title'])) {
            $updateFields[] = "title = ?";
            $updateParams[] = trim($_POST['title']);
        }

        if (isset($_POST['description'])) {
            $updateFields[] = "description = ?";
            $updateParams[] = trim($_POST['description']);
        }

        if (isset($_POST['exe_download_url'])) {
            $exeUrl = trim($_POST['exe_download_url']);
            if (!empty($exeUrl) && !validateUrl($exeUrl)) {
                sendError('Windows下载链接格式不正确');
            }
            $updateFields[] = "exe_download_url = ?";
            $updateParams[] = $exeUrl ?: null;
        }

        if (isset($_POST['dmg_download_url'])) {
            $dmgUrl = trim($_POST['dmg_download_url']);
            if (!empty($dmgUrl) && !validateUrl($dmgUrl)) {
                sendError('macOS下载链接格式不正确');
            }
            $updateFields[] = "dmg_download_url = ?";
            $updateParams[] = $dmgUrl ?: null;
        }

        if (isset($_POST['exe_file_size'])) {
            $updateFields[] = "exe_file_size = ?";
            $updateParams[] = trim($_POST['exe_file_size']);
        }

        if (isset($_POST['dmg_file_size'])) {
            $updateFields[] = "dmg_file_size = ?";
            $updateParams[] = trim($_POST['dmg_file_size']);
        }

        if (isset($_POST['force_update'])) {
            $updateFields[] = "force_update = ?";
            $updateParams[] = $_POST['force_update'] ? 1 : 0;
        }

        if (isset($_POST['status']) && in_array($_POST['status'], ['draft', 'published'])) {
            $updateFields[] = "status = ?";
            $updateParams[] = $_POST['status'];
        }

        if (isset($_POST['platform']) && in_array($_POST['platform'], ['all', 'windows', 'macos', 'linux'])) {
            $updateFields[] = "platform = ?";
            $updateParams[] = $_POST['platform'];
        }

        if (empty($updateFields)) {
            sendError('没有要更新的字段');
        }

        // 执行更新
        $updateParams[] = $id;
        $sql = "UPDATE app_updates SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $success = $stmt->execute($updateParams);

        if ($success) {
            // 获取更新后的版本信息
            $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
            $stmt->execute([$id]);
            $updatedVersion = $stmt->fetch();

            sendSuccess($updatedVersion, '版本更新成功');
        } else {
            sendError('版本更新失败', 500);
        }

    } catch (Exception $e) {
        sendError('更新版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 删除版本
 */
function handleDeleteVersion($pdo) {
    $id = intval($_POST['id'] ?? 0);
    if ($id <= 0) {
        sendError('无效的版本ID');
    }

    try {
        // 检查版本是否存在
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ?");
        $stmt->execute([$id]);
        $version = $stmt->fetch();

        if (!$version) {
            sendError('版本不存在', 404);
        }

        // 删除版本
        $stmt = $pdo->prepare("DELETE FROM app_updates WHERE id = ?");
        $success = $stmt->execute([$id]);

        if ($success) {
            sendSuccess(['deleted_version' => $version], '版本删除成功');
        } else {
            sendError('版本删除失败', 500);
        }

    } catch (Exception $e) {
        sendError('删除版本失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 处理下载请求
 */
function handleDownload($pdo) {
    $updateId = intval($_GET['update_id'] ?? 0);
    $platform = strtolower($_GET['platform'] ?? '');

    if ($updateId <= 0) {
        sendError('无效的更新ID');
    }

    if (!in_array($platform, ['windows', 'macos', 'macos_m1', 'macos_intel'])) {
        sendError('不支持的平台');
    }

    try {
        // 获取版本信息
        $stmt = $pdo->prepare("SELECT * FROM app_updates WHERE id = ? AND status = 'published'");
        $stmt->execute([$updateId]);
        $update = $stmt->fetch();

        if (!$update) {
            sendError('版本不存在或未发布', 404);
        }

        // 获取对应平台的下载链接
        $downloadUrl = null;
        if ($platform === 'windows' && $update['exe_download_url']) {
            $downloadUrl = $update['exe_download_url'];
        } elseif ($platform === 'macos') {
            // 优先级：通用macOS链接 > M1链接 > Intel链接
            if ($update['dmg_download_url']) {
                $downloadUrl = $update['dmg_download_url'];
            } elseif ($update['dmg_m1_download_url']) {
                $downloadUrl = $update['dmg_m1_download_url'];
            } elseif ($update['dmg_intel_download_url']) {
                $downloadUrl = $update['dmg_intel_download_url'];
            }
        } elseif ($platform === 'macos_m1' && $update['dmg_m1_download_url']) {
            $downloadUrl = $update['dmg_m1_download_url'];
        } elseif ($platform === 'macos_intel' && $update['dmg_intel_download_url']) {
            $downloadUrl = $update['dmg_intel_download_url'];
        }

        if (!$downloadUrl) {
            sendError('该平台的安装包不可用', 404);
        }

        // 记录下载日志
        logDownload($pdo, $updateId, $platform, $downloadUrl);

        // 返回下载链接
        sendSuccess([
            'download_url' => $downloadUrl,
            'version' => $update['version'],
            'title' => $update['title'],
            'platform' => $platform,
            'file_size' => $platform === 'windows' ? $update['exe_file_size'] : $update['dmg_file_size']
        ], '获取下载链接成功');

    } catch (Exception $e) {
        sendError('获取下载链接失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 验证版本号格式
 */
function isValidVersion($version) {
    if (empty($version) || !is_string($version)) {
        return false;
    }

    // 移除可能的前缀
    $clean = preg_replace('/^v/', '', trim($version));

    // 检查是否符合 x.y.z 格式
    if (!preg_match('/^[0-9]+\.[0-9]+\.[0-9]+$/', $clean)) {
        return false;
    }

    // 检查每个部分是否为有效数字
    $parts = explode('.', $clean);
    foreach ($parts as $part) {
        if (!is_numeric($part) || intval($part) < 0) {
            return false;
        }
    }

    return true;
}

?>
