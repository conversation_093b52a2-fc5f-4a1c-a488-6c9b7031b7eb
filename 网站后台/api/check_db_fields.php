<?php
/**
 * 数据库字段检查工具
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

try {
    $pdo = new PDO(
        "mysql:host=127.0.0.1;port=3306;dbname=xiaomeihuakefu_c;charset=utf8mb4",
        "xiaomeihuakefu_c",
        "7Da5F1Xx995cxYz8",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // 检查表结构
    $stmt = $pdo->query("DESCRIBE app_updates");
    $fields = $stmt->fetchAll();
    
    // 检查现有数据
    $stmt = $pdo->query("SELECT * FROM app_updates LIMIT 5");
    $data = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'fields' => $fields,
        'sample_data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>