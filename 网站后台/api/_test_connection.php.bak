<?php
/**
 * 简单的连接测试API
 * 用于验证服务器是否正常工作
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取当前时间
$current_time = date('Y-m-d H:i:s');

// 响应数据
$response = [
    'success' => true,
    'message' => '服务器连接正常',
    'timestamp' => $current_time,
    'server_info' => [
        'php_version' => PHP_VERSION,
        'server_time' => $current_time,
        'memory_usage' => memory_get_usage(true),
        'method' => $_SERVER['REQUEST_METHOD']
    ]
];

// 返回JSON响应
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>