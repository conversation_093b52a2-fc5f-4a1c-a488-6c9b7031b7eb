<?php
/**
 * 统一数据库配置文件
 * 为所有API提供统一的MySQL数据库连接
 */

// 禁用错误输出，确保只输出JSON
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

/**
 * 数据库配置类
 */
class DatabaseConfig {
    private static $instance = null;
    private $pdo = null;
    private $config = null;
    
    // 数据库配置 - 支持多种环境
    private $dbConfigs = [
        // 生产环境配置
        'production' => [
            'host' => '127.0.0.1',
            'port' => '3306',
            'dbname' => 'xiaomeihuakefu_c',
            'username' => 'xiaomeihuakefu_c',
            'password' => '7Da5F1Xx995cxYz8',
            'charset' => 'utf8mb4'
        ],
        // 外部服务器配置
        'external' => [
            'host' => '***************',
            'port' => '8684',
            'dbname' => 'xiaomeihuakefu_c',
            'username' => 'xiaomeihuakefu_c',
            'password' => '7Da5F1Xx995cxYz8',
            'charset' => 'utf8mb4'
        ],
        // 本地开发环境配置
        'development' => [
            'host' => '127.0.0.1',
            'port' => '3306',
            'dbname' => 'xiaomeihua_local',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4'
        ]
    ];
    
    private function __construct() {
        $this->initializeConnection();
    }
    
    /**
     * 获取单例实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 初始化数据库连接
     */
    private function initializeConnection() {
        // 按优先级尝试连接
        $connectionOrder = ['production', 'external', 'development'];
        
        foreach ($connectionOrder as $configName) {
            $config = $this->dbConfigs[$configName];
            
            try {
                $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}";
                $pdo = new PDO($dsn, $config['username'], $config['password'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_TIMEOUT => 3,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
                ]);
                
                // 设置时区
                $pdo->exec("SET time_zone = '+08:00'");
                
                // 测试连接
                $stmt = $pdo->query("SELECT 1");
                if ($stmt->fetchColumn() == 1) {
                    $this->pdo = $pdo;
                    $this->config = $config;
                    error_log("数据库连接成功: {$configName} - {$config['host']}:{$config['port']}");
                    break;
                }
                
            } catch (PDOException $e) {
                error_log("数据库连接失败 ({$configName}): " . $e->getMessage());
                continue;
            }
        }
    }
    
    /**
     * 获取数据库连接
     */
    public function getConnection() {
        return $this->pdo;
    }
    
    /**
     * 获取当前配置
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * 检查连接是否可用
     */
    public function isConnected() {
        if (!$this->pdo) {
            return false;
        }
        
        try {
            $stmt = $this->pdo->query("SELECT 1");
            return $stmt->fetchColumn() == 1;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * 重新连接数据库
     */
    public function reconnect() {
        $this->pdo = null;
        $this->config = null;
        $this->initializeConnection();
        return $this->isConnected();
    }
    
    /**
     * 执行查询
     */
    public function query($sql, $params = []) {
        if (!$this->pdo) {
            throw new Exception('数据库连接不可用');
        }
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("SQL执行失败: " . $e->getMessage() . " - SQL: " . $sql);
            throw $e;
        }
    }
    
    /**
     * 获取单行数据
     */
    public function getRow($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 获取所有数据
     */
    public function getAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 执行更新/插入/删除
     */
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 获取最后插入的ID
     */
    public function lastInsertId() {
        if (!$this->pdo) {
            return false;
        }
        return $this->pdo->lastInsertId();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        if ($this->pdo) {
            return $this->pdo->beginTransaction();
        }
        return false;
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        if ($this->pdo) {
            return $this->pdo->commit();
        }
        return false;
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        if ($this->pdo) {
            return $this->pdo->rollback();
        }
        return false;
    }
}

/**
 * 全局函数 - 获取数据库实例
 */
function getDatabase() {
    return DatabaseConfig::getInstance();
}

/**
 * 全局函数 - 获取数据库连接
 */
function getDatabaseConnection() {
    $db = DatabaseConfig::getInstance();
    return $db->getConnection();
}

/**
 * 全局函数 - 检查数据库是否可用
 */
function isDatabaseAvailable() {
    $db = DatabaseConfig::getInstance();
    return $db->isConnected();
}
?>
