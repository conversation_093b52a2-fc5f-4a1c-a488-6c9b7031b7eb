/* General body styling */
body, html {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    background-color: #f0f2f5;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
}

/* Main container */
.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

/* Title bar styling */
.title-bar {
    -webkit-app-region: drag;
    height: 40px;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    padding-left: 10px;
    border-bottom: 1px solid #dcdcdc;
    flex-shrink: 0;
    position: relative;
    width: 100%;
}

.traffic-lights {
    display: flex;
    align-items: center;
    -webkit-app-region: no-drag;
}

.light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.light.red { background-color: #ff5f57; }
.light.yellow { background-color: #ffbd2e; }
.light.green { background-color: #28c940; }

.title {
    margin-left: 10px;
    font-weight: bold;
    color: #333;
}

/* Windows控制按钮样式 */
.win-controls {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    height: 100%;
    -webkit-app-region: no-drag;
}

.win-control-btn {
    width: 46px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #555;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
}

.win-control-btn:hover {
    background-color: #e6e6e6;
}

#win-close-btn:hover {
    background-color: #e81123;
    color: white;
}

#win-minimize-btn {
    font-weight: bold;
    font-size: 16px;
}

/* Main content area */
.main-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
    width: 100%;
}

/* Tab container styling */
.tab-container {
    display: flex;
    align-items: center;
    background-color: #e9e9e9;
    padding: 5px 5px 0 5px;
    flex-shrink: 0;
    overflow-x: auto;
    width: 100%;
    box-sizing: border-box;
}

.tab {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    background-color: #f3f3f3;
    border: 1px solid #ccc;
    border-bottom: none;
    border-radius: 5px 5px 0 0;
    margin-right: 4px;
    cursor: pointer;
    position: relative;
    max-width: 200px;
}

.tab.active {
    background-color: #ffffff;
    border-bottom: 1px solid #ffffff;
}

.tab-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 10px;
}

.tab-close {
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
}

/* Webview container styling */
.webview-container {
    flex-grow: 1;
    position: relative;
    width: 100%;
    height: 100%;
}

webview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    visibility: hidden; /* Hide by default */
    right: 0;
    bottom: 0;
}

webview.active {
    visibility: visible; /* Show only the active webview */
} 