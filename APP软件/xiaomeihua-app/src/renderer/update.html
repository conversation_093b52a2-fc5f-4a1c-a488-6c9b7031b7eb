<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI智能客服<版本升级></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: transparent;
            color: #333;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
            min-height: 480px;
            margin: 0;
            padding: 0;
        }

        .update-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 30px 20px 20px 20px; /* 减少顶部边距，向上移动内容 */
            background: white;
            margin: 0;
            min-height: 420px;
            justify-content: space-between;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: opacity 0.2s ease-in-out;
            position: relative;
        }

        .update-main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .app-logo {
            width: 100px; /* 从120px减少到100px，稍微缩小logo */
            height: 100px; /* 从120px减少到100px，稍微缩小logo */
            margin: 15px auto 15px; /* 减少上下边距，向上移动 */
            background: url('../assets/logo.png') center/contain no-repeat;
            border-radius: 20px; /* 相应调整圆角 */
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
        }

        .update-title {
            text-align: center;
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px; /* 减少底部边距 */
            letter-spacing: 0.5px;
        }



        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e0e6ed, transparent);
            margin-bottom: 15px; /* 减少底部边距 */
        }

        .update-content {
            flex: 1;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px; /* 减少内边距 */
            margin-bottom: 20px; /* 减少底部边距 */
            font-size: 14px;
            line-height: 1.6;
            color: #555;
            overflow-y: auto;
            max-height: 180px; /* 稍微减少最大高度 */
            min-height: 100px; /* 减少最小高度 */
        }

        .update-content h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .update-content ul {
            padding-left: 20px;
        }

        .update-content li {
            margin-bottom: 5px;
        }

        .update-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        /* 居中单个按钮的样式 */
        .centered-btn {
            max-width: 200px;
            width: 200px;
        }

        .update-btn {
            flex: 1;
            height: 44px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .primary-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .primary-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .secondary-btn {
            background: #e9ecef;
            color: #6c757d;
        }

        .secondary-btn:hover {
            background: #dee2e6;
        }

        .progress-container {
            display: none;
            margin-top: 15px; /* 减少顶部边距 */
            margin-bottom: 15px; /* 增加底部边距，确保文字显示完全 */
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 12px; /* 稍微增加底部边距 */
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 5px; /* 增加底部边距，确保文字不被截断 */
        }

        .status-text {
            text-align: center;
            margin-top: 10px; /* 减少顶部边距 */
            margin-bottom: 10px; /* 增加底部边距 */
            font-size: 14px;
            color: #667eea;
            font-weight: 500;
        }

        .hidden {
            display: none !important;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px; /* 稍微减少内边距 */
            border-radius: 8px;
            margin-top: 10px; /* 减少顶部边距 */
            margin-bottom: 10px; /* 增加底部边距 */
            border: 1px solid #f5c6cb;
            font-size: 14px;
            text-align: center;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px; /* 稍微减少内边距 */
            border-radius: 8px;
            margin-top: 10px; /* 减少顶部边距 */
            margin-bottom: 10px; /* 增加底部边距 */
            border: 1px solid #c3e6cb;
            font-size: 14px;
            text-align: center;
        }

        /* 滚动条样式 */
        .update-content::-webkit-scrollbar {
            width: 6px;
        }

        .update-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .update-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .update-content::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 移除自定义关闭按钮样式，使用macOS原生交通灯按钮 */

        /* 拖拽功能样式 */
        .update-container {
            -webkit-app-region: drag;
        }

        /* 禁用所有子元素的拖拽，然后单独启用需要拖拽的区域 */
        .update-container * {
            -webkit-app-region: no-drag;
        }

        /* 启用拖拽的区域 */
        .update-main-content,
        .update-title,
        .update-content,
        .divider {
            -webkit-app-region: drag;
        }

        /* 确保按钮和交互元素不可拖拽 */
        .update-btn,
        .progress-container,
        .status-text,
        .error-message,
        .success-message {
            -webkit-app-region: no-drag;
        }

        /* 防止鼠标在安装过程中变成彩色圆球 */
        .installing {
            cursor: default !important;
        }

        .installing * {
            cursor: default !important;
        }

        /* 安装过程中的特殊样式 */
        .installing .progress-container {
            cursor: default !important;
        }

        .installing .progress-text {
            cursor: default !important;
        }


    </style>
</head>
<body>
    <div class="update-container" id="updateContainer" style="opacity: 0; visibility: hidden;">
        <div class="update-main-content">
            <div class="app-logo"></div>
            <div class="update-title" id="updateTitle">新版本</div>
            
            <div class="divider"></div>
            
            <div class="update-content" id="updateContent">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <div class="update-actions" id="updateActions">
            <button class="update-btn primary-btn centered-btn" onclick="startUpdate()" id="updateBtn">
                立即更新
            </button>
        </div>

        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备下载...</div>
        </div>

        <div class="status-text hidden" id="statusText"></div>
        <div class="error-message hidden" id="errorMessage"></div>
        <div class="success-message hidden" id="successMessage"></div>
    </div>

    <script>
        let updateInfo = null;
        let isDownloading = false;
        let autoUpdateTimer = null;
        let countdownTimer = null;
        let countdown = 5;

        // 平台检测和UI初始化 - 现在使用原生macOS标题栏
        function initializePlatformUI() {
            // 现在使用原生macOS标题栏和交通灯按钮，无需自定义关闭按钮
            console.log('使用原生macOS标题栏和交通灯按钮');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePlatformUI();
        });

        // 监听更新信息
        window.updateAPI.onUpdateInfo((data) => {
            console.log('收到更新信息:', data);
            updateInfo = data;
            displayUpdateInfo(data);
        });

        // 监听下载进度
        window.updateAPI.onDownloadProgress((progress) => {
            console.log('下载进度:', progress);

            // 使用自定义状态消息或默认消息（只显示百分比）
            let statusMessage;
            if (progress.status) {
                statusMessage = progress.status;
            } else {
                statusMessage = `更新中 ${progress.percent}%`;
            }

            updateProgress(progress.percent, statusMessage);
        });

        // 监听下载开始
        window.updateAPI.onStartDownload(() => {
            console.log('开始下载');
            showProgress();
            updateProgress(0, '更新中 0%');
        });

        // 监听下载完成
        window.updateAPI.onDownloadComplete(() => {
            console.log('下载完成');
            updateProgress(100, '更新中 100%');
        });

        // 监听安装开始
        window.updateAPI.onStartInstall(() => {
            console.log('开始安装');
            updateProgress(100, '更新中 100%');

            // 添加安装状态样式，防止鼠标变成彩色圆球
            document.body.classList.add('installing');
            document.getElementById('updateContainer').classList.add('installing');
        });

        // 监听安装完成
        window.updateAPI.onInstallComplete(() => {
            console.log('安装完成');
            updateProgress(100, '更新中 100%');

            // 移除安装状态样式
            document.body.classList.remove('installing');
            document.getElementById('updateContainer').classList.remove('installing');

            // 不需要手动调用重启，app-updater.js会自动处理
        });

        // 监听下载错误
        window.updateAPI.onDownloadError((error) => {
            console.error('下载错误:', error);
            showError(`更新失败：${error}`);
            hideProgress();
            resetActions();
        });

        function displayUpdateInfo(info) {
            if (info) {
                // 设置更新标题
                document.getElementById('updateTitle').textContent = '新版本';
                
                // 直接显示更新内容
                document.getElementById('updateContent').innerHTML = formatUpdateContent(info);
                
                // 显示容器，避免闪烁
                const container = document.getElementById('updateContainer');
                container.style.opacity = '1';
                container.style.visibility = 'visible';
                
                // 强制更新时的处理（现在按钮已经是居中的单个按钮）
                if (info.mandatory) {
                    const updateBtn = document.getElementById('updateBtn');
                    if (updateBtn) {
                        updateBtn.textContent = '立即更新';
                    }

                    // 强制更新时，原生关闭按钮仍然可用，但关闭会退出应用
                }
                
                // 启动5秒倒计时自动更新
                startAutoUpdateCountdown();
            }
        }
        
        function startAutoUpdateCountdown() {
            const updateBtn = document.getElementById('updateBtn');
            if (!updateBtn) return;
            
            // 清除之前的计时器
            if (autoUpdateTimer) clearTimeout(autoUpdateTimer);
            if (countdownTimer) clearInterval(countdownTimer);
            
            countdown = 5;
            updateBtn.textContent = `立即更新 (${countdown}s)`;
            
            // 每秒更新倒计时显示
            countdownTimer = setInterval(() => {
                countdown--;
                if (countdown > 0) {
                    updateBtn.textContent = `立即更新 (${countdown}s)`;
                } else {
                    clearInterval(countdownTimer);
                    updateBtn.textContent = '立即更新';
                }
            }, 1000);
            
            // 5秒后自动点击更新
            autoUpdateTimer = setTimeout(() => {
                if (!isDownloading) {
                    console.log('5秒倒计时结束，自动开始更新');
                    startUpdate();
                }
            }, 5000);
        }

        function formatUpdateContent(info) {
            let content = `<h3>版本 ${info.version}</h3>`;
            
            if (info.content) {
                // 处理更新内容，支持简单的HTML标记
                content += `<div>${info.content}</div>`;
            } else {
                content += `<p>发现新版本，建议立即更新以获得更好的使用体验。</p>`;
            }

            return content;
        }

        function startUpdate() {
            if (isDownloading) return;
            
            // 清除自动更新倒计时
            if (autoUpdateTimer) {
                clearTimeout(autoUpdateTimer);
                autoUpdateTimer = null;
            }
            if (countdownTimer) {
                clearInterval(countdownTimer);
                countdownTimer = null;
            }
            
            isDownloading = true;
            console.log('用户手动开始更新');
            window.updateAPI.startUpdate();
        }

        function cancelUpdate() {
            // 现在没有"稍后提醒"按钮，这个函数主要用于处理窗口关闭
            // 检查是否为强制更新
            if (updateInfo && updateInfo.mandatory) {
                // 强制更新时不允许取消，直接退出应用
                console.log('强制更新：用户尝试关闭窗口，退出应用');
                window.updateAPI.closeUpdateWindow(); // 这会触发应用退出
                return;
            }

            console.log('用户关闭更新窗口');
            window.updateAPI.cancelUpdate();
        }

        function showProgress() {
            document.getElementById('updateActions').classList.add('hidden');
            document.getElementById('progressContainer').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.classList.remove('hidden');
            
            // 隐藏其他消息
            document.getElementById('successMessage').classList.add('hidden');
            document.getElementById('statusText').classList.add('hidden');
        }

        function showSuccess(message) {
            const successEl = document.getElementById('successMessage');
            successEl.textContent = message;
            successEl.classList.remove('hidden');
            
            // 隐藏其他消息
            document.getElementById('errorMessage').classList.add('hidden');
            document.getElementById('statusText').classList.add('hidden');
        }

        function showStatus(message) {
            const statusEl = document.getElementById('statusText');
            statusEl.textContent = message;
            statusEl.classList.remove('hidden');
            
            // 隐藏其他消息
            document.getElementById('errorMessage').classList.add('hidden');
            document.getElementById('successMessage').classList.add('hidden');
        }

        function resetActions() {
            isDownloading = false;
            document.getElementById('updateActions').classList.remove('hidden');
            
            // 重置按钮状态
            const updateBtn = document.getElementById('updateBtn');
            updateBtn.innerHTML = '立即更新';
            updateBtn.disabled = false;
        }

        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 防止选择文本
        document.addEventListener('selectstart', (e) => {
            e.preventDefault();
        });
    </script>
</body>
</html>