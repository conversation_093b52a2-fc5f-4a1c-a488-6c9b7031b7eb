const { autoUpdater } = require('electron-updater');
const { app, dialog, shell, BrowserWindow } = require('electron');
const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const axios = require('axios');

class AppUpdater {
  constructor() {
    this.updateWindow = null;
    this.isChecking = false;
    this.downloadProgress = 0;
    this.isDownloading = false;
    this.updateInfo = null;
    this.downloadUrl = null;
    this.downloadPath = null;
    this.logFile = path.join(app.getPath('userData'), 'update.log');
    this.downloadDir = path.join(app.getPath('userData'), 'downloads');

    // 更新状态管理
    this.updateStatusFile = path.join(app.getPath('userData'), 'update_status.json');
    this.lastUpdateCheck = this.loadUpdateStatus();

    // 确保下载目录存在
    if (!fs.existsSync(this.downloadDir)) {
      fs.mkdirSync(this.downloadDir, { recursive: true });
    }

    this.setupEventListeners();
    this.log('AppUpdater 初始化完成');
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(`[AppUpdater] ${message}`);

    try {
      fs.appendFileSync(this.logFile, logMessage);
    } catch (error) {
      console.error('写入日志失败:', error);
    }
  }

  // 加载更新状态
  loadUpdateStatus() {
    try {
      if (fs.existsSync(this.updateStatusFile)) {
        const data = fs.readFileSync(this.updateStatusFile, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      this.log(`加载更新状态失败: ${error.message}`);
    }
    return {
      lastVersion: null,
      lastCheckTime: null,
      updateCompleted: false
    };
  }

  // 保存更新状态
  saveUpdateStatus(status) {
    try {
      fs.writeFileSync(this.updateStatusFile, JSON.stringify(status, null, 2));
      this.log(`更新状态已保存: ${JSON.stringify(status)}`);
    } catch (error) {
      this.log(`保存更新状态失败: ${error.message}`);
    }
  }

  // 标记更新完成
  markUpdateCompleted(version) {
    const status = {
      lastVersion: version,
      lastCheckTime: new Date().toISOString(),
      updateCompleted: true,
      completedTimestamp: Date.now()  // 添加时间戳用于更精确的时间控制
    };
    this.saveUpdateStatus(status);
    this.lastUpdateCheck = status;
    this.log(`✅ 更新完成标记已保存: ${version} (时间戳: ${status.completedTimestamp})`);
  }

  setupEventListeners() {
    // 配置自动更新器
    autoUpdater.autoDownload = false;
    autoUpdater.autoInstallOnAppQuit = false;
    
    // 监听更新事件
    autoUpdater.on('checking-for-update', () => {
      this.log('开始检查更新...');
    });

    autoUpdater.on('update-available', (info) => {
      this.log(`发现新版本: ${info.version}`);
      this.updateInfo = info;
    });

    autoUpdater.on('update-not-available', (info) => {
      this.log('当前已是最新版本');
    });

    autoUpdater.on('error', (err) => {
      this.log(`更新检查失败: ${err.message}`);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      this.downloadProgress = Math.round(progressObj.percent);
      this.log(`下载进度: ${this.downloadProgress}%`);
      
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('download-progress', {
          percent: this.downloadProgress,
          bytesPerSecond: progressObj.bytesPerSecond,
          total: progressObj.total,
          transferred: progressObj.transferred
        });
      }
    });

    autoUpdater.on('update-downloaded', (info) => {
      this.log('更新下载完成');
      this.isDownloading = false;
      
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('download-complete');
      }
    });
  }

  /**
   * 精准检测当前平台和架构
   * 返回对应的API端点URL
   */
  getPlatformInfo() {
    const platform = process.platform;
    const arch = process.arch;

    this.log(`🔍 检测平台信息: platform=${platform}, arch=${arch}`);

    if (platform === 'win32') {
      return {
        platform: 'windows',
        architecture: 'x64',
        apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_windows.php',
        platformKey: 'windows'
      };
    } else if (platform === 'darwin') {
      if (arch === 'arm64') {
        // M系列芯片 - 使用专用M芯片API
        return {
          platform: 'macos',
          architecture: 'm1',
          apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_m1.php',
          platformKey: 'macos_m1'
        };
      } else {
        // Intel芯片 - 使用专用API端点
        return {
          platform: 'macos',
          architecture: 'intel',
          apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_intel.php',
          platformKey: 'macos_intel'
        };
      }
    } else {
      throw new Error(`不支持的平台: ${platform}`);
    }
  }

  /**
   * 从后台API获取更新信息
   */
  async getUpdateInfoFromBackend() {
    try {
      const currentVersion = app.getVersion();
      const platformInfo = this.getPlatformInfo();
      
      this.log(`📡 开始检查更新: ${platformInfo.platformKey}`);
      this.log(`📱 当前版本: ${currentVersion}`);
      this.log(`🌐 API地址: ${platformInfo.apiUrl}`);
      this.log(`🏗️  架构信息: ${platformInfo.architecture} (${platformInfo.platform})`);
      
      // 构造请求参数
      const params = {
        action: 'check',
        version: currentVersion,
        platform: platformInfo.platformKey,  // 添加平台信息
        architecture: platformInfo.architecture  // 添加架构信息
      };
      
      // 设置请求配置
      const config = {
        method: 'GET',
        url: platformInfo.apiUrl,
        params: params,
        timeout: 15000, // 15秒超时
        headers: {
          'User-Agent': `XiaoMeiHua-App/${currentVersion} (${platformInfo.platform}; ${platformInfo.architecture})`,
          'Accept': 'application/json',
          'Cache-Control': 'no-cache'
        }
      };
      
      this.log(`🚀 发送请求: ${JSON.stringify(params)}`);
      
      // 发送请求
      const response = await axios(config);
      
      this.log(`📥 收到响应: status=${response.status}`);
      this.log(`📄 响应数据: ${JSON.stringify(response.data, null, 2)}`);
      
      // 验证响应格式
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('API响应格式无效');
      }
      
      // 处理API错误响应
      if (!response.data.success) {
        const errorMsg = response.data.message || '后台返回错误';
        this.log(`❌ API返回错误: ${errorMsg}`);
        
        // 如果是数据库相关错误，标记为暂时性错误，不触发更新
        if (errorMsg.includes('SQLSTATE') || errorMsg.includes('Column not found') || 
            errorMsg.includes('数据库') || errorMsg.includes('检查更新失败')) {
          this.log('🚫 检测到数据库错误，跳过此次更新检查');
          return {
            hasUpdate: false,
            currentVersion: currentVersion,
            latestVersion: currentVersion,
            skipReason: 'database_error',
            errorMessage: errorMsg
          };
        }
        
        throw new Error(errorMsg);
      }
      
      const data = response.data.data;
      if (!data) {
        throw new Error('API响应缺少data字段');
      }
      
      // 检查是否有更新
      const hasUpdate = data.has_update;
      this.log(`🔍 更新检查结果: ${hasUpdate ? '发现新版本' : '已是最新版本'}`);
      this.log(`📊 API返回数据: 当前版本=${data.current_version}, 最新版本=${data.latest_version}`);
      
      if (hasUpdate && data.update_info) {
        const updateInfo = data.update_info;
        
        // 从download_urls中选择适合的下载链接
        let downloadUrl = null;
        let backupUrl = null;

        if (updateInfo.download_urls) {
          if (platformInfo.architecture === 'm1') {
            // M芯片用户：只有在有M芯片专用链接时才提供更新
            if (updateInfo.download_urls.macos_m1) {
              downloadUrl = updateInfo.download_urls.macos_m1;
              this.log(`🍎 M芯片用户，使用M芯片专用下载链接`);
            } else {
              this.log(`⚠️  M芯片用户，但无M芯片专用下载链接，跳过更新`);
              return {
                hasUpdate: false,
                currentVersion: currentVersion,
                latestVersion: currentVersion,
                skipReason: 'no_m1_download',
                message: '当前版本无M芯片专用下载链接'
              };
            }
          } else if (platformInfo.architecture === 'intel') {
            // Intel芯片优先使用Intel专用链接
            downloadUrl = updateInfo.download_urls.macos_intel || updateInfo.download_urls.macos;
            this.log(`💻 Intel芯片用户，使用下载链接: ${downloadUrl ? 'Intel专用' : '通用macOS'}`);
          } else if (platformInfo.platform === 'windows' && updateInfo.download_urls.windows) {
            downloadUrl = updateInfo.download_urls.windows;
          }
        }
        
        // 如果没有从download_urls中找到，尝试直接使用download_url字段（兼容性）
        if (!downloadUrl && updateInfo.download_url) {
          downloadUrl = updateInfo.download_url;
        }
        
        // 验证版本号
        const latestVersion = data.latest_version;
        if (!latestVersion || !this.isValidVersionFormat(latestVersion)) {
          this.log(`⚠️ 发现无效版本号: ${latestVersion}，跳过更新`);
          return {
            hasUpdate: false,
            currentVersion: currentVersion,
            latestVersion: currentVersion,
            skipReason: 'invalid_version_format',
            errorMessage: `无效的版本号格式: ${latestVersion}`
          };
        }

        // 额外的版本比较验证
        if (this.isVersionMatch(currentVersion, latestVersion)) {
          this.log(`⚠️ 版本号相同，但后台返回有更新，可能是数据异常: 当前${currentVersion} vs 最新${latestVersion}`);
          return {
            hasUpdate: false,
            currentVersion: currentVersion,
            latestVersion: latestVersion,
            skipReason: 'version_match_conflict',
            errorMessage: '版本比较异常，请联系管理员'
          };
        }

        // 验证下载链接
        if (!downloadUrl) {
          this.log(`⚠️ 没有找到适合的下载链接，跳过更新`);
          return {
            hasUpdate: false,
            currentVersion: currentVersion,
            latestVersion: latestVersion,
            skipReason: 'no_download_url',
            errorMessage: '没有找到适合的下载链接'
          };
        }

        this.log(`📦 新版本信息:`);
        this.log(`   版本号: ${latestVersion}`);
        this.log(`   标题: ${updateInfo.title}`);
        this.log(`   强制更新: ${updateInfo.force_update}`);
        this.log(`   下载链接: ${downloadUrl}`);
        this.log(`   备用链接: ${backupUrl || updateInfo.backup_url || '无'}`);

        return {
          hasUpdate: true,
          updateInfo: {
            version: latestVersion,
            title: updateInfo.title,
            description: updateInfo.description,
            releaseNotes: updateInfo.release_notes,
            forceUpdate: updateInfo.force_update,
            downloadUrl: downloadUrl,
            backupUrl: backupUrl || updateInfo.backup_url,
            createdAt: updateInfo.created_at,
            platform: platformInfo.platform,
            architecture: platformInfo.architecture
          }
        };
      } else {
        return {
          hasUpdate: false,
          currentVersion: currentVersion,
          latestVersion: data.latest_version
        };
      }
      
    } catch (error) {
      this.log(`❌ API请求失败: ${error.message}`);
      if (error.response) {
        this.log(`HTTP状态: ${error.response.status}`);
        this.log(`响应数据: ${JSON.stringify(error.response.data)}`);
        
        // 对于500错误（服务器内部错误），不应该触发更新
        if (error.response.status === 500) {
          this.log('🚫 服务器内部错误，跳过此次更新检查');
          return {
            hasUpdate: false,
            currentVersion: app.getVersion(),
            latestVersion: app.getVersion(),
            skipReason: 'server_error',
            errorMessage: error.message
          };
        }
      }
      throw error;
    }
  }

  async checkForUpdates(retryCount = 0) {
    const maxRetries = 2; // 最大重试次数
    
    if (this.isChecking) {
      this.log('正在检查更新中，跳过重复检查');
      return false;
    }

    try {
      this.isChecking = true;
      
      // 检查是否刚刚完成更新（防止循环更新提示）
      const currentVersion = app.getVersion();
      if (this.detectUpdateCompletion(currentVersion)) {
        this.log('✅ 检测到刚完成更新，跳过此次检查避免循环提示');
        this.isChecking = false;
        return false;
      }
      
      // 从后台API获取更新信息
      const result = await this.getUpdateInfoFromBackend();
      
      // 检查是否因为错误而跳过
      if (result.skipReason) {
        this.log(`⚠️ 跳过更新检查，原因: ${result.skipReason}`);
        this.log(`💡 错误信息: ${result.errorMessage}`);
        this.isChecking = false;
        return false;
      }
      
      if (result.hasUpdate) {
        this.updateInfo = result.updateInfo;
        
        // 显示更新弹窗
        this.showUpdateWindow();
        
        this.log(`✅ 发现更新: ${result.updateInfo.version}`);
        this.isChecking = false;
        return true;
      } else {
        this.log(`✅ 已是最新版本: ${currentVersion}`);
        this.isChecking = false;
        return false;
      }
      
    } catch (error) {
      this.log(`❌ 检查更新失败 (尝试 ${retryCount + 1}/${maxRetries + 1}): ${error.message}`);
      
      // 如果是网络相关错误并且还有重试机会，则进行重试
      if (retryCount < maxRetries && (
        error.code === 'ECONNRESET' || 
        error.code === 'ECONNREFUSED' || 
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout') ||
        error.message.includes('Network Error') ||
        error.message.includes('connect ECONNREFUSED')
      )) {
        this.log(`🔄 网络错误，${3}秒后进行第${retryCount + 2}次重试...`);
        this.isChecking = false;
        
        // 等待3秒后重试
        setTimeout(() => {
          this.checkForUpdates(retryCount + 1);
        }, 3000);
        
        return false;
      }
      
      this.log('⚠️ 由于API错误，不显示更新弹窗，避免误导用户');
      this.isChecking = false;
      return false;
    }
  }

  /**
   * 智能检测应用是否刚完成更新 - 解决重复更新提示的致命bug
   * 这是重启后执行的逻辑，必须使用重启后的真实版本号
   */
  detectUpdateCompletion(currentVersion) {
    try {
      // 检查是否有待处理的更新完成标记
      const pendingUpdate = this.checkPendingUpdateCompletion();
      if (pendingUpdate) {
        this.log(`🔍 发现待处理的更新完成标记，预期版本: ${pendingUpdate.expectedVersion}`);
        
        // 检查当前版本是否符合预期的更新版本
        if (this.isVersionMatch(currentVersion, pendingUpdate.expectedVersion)) {
          this.log(`✅ 版本匹配确认，更新确实完成: ${currentVersion}`);
          // 使用重启后的真实版本号标记更新完成
          this.markUpdateCompleted(currentVersion);
          // 清除待处理的更新标记
          this.clearPendingUpdateMark();
          return true;
        } else {
          this.log(`⚠️ 版本不匹配，可能更新失败: 期望${pendingUpdate.expectedVersion}, 实际${currentVersion}`);
          // 清除无效的待处理标记
          this.clearPendingUpdateMark();
        }
      }
      
      // 检查版本是否比上次记录的版本更新
      if (this.lastUpdateCheck.lastVersion &&
          this.compareVersionsStrict(currentVersion, this.lastUpdateCheck.lastVersion) > 0) {
        this.log(`✅ 检测到版本已更新 (${this.lastUpdateCheck.lastVersion} -> ${currentVersion})`);
        this.log(`📱 使用重启后的真实版本号标记更新完成: ${currentVersion}`);
        this.markUpdateCompleted(currentVersion);
        return true;
      }

      // 增强：检查是否版本号发生了任何变化（包括降级的情况）
      if (this.lastUpdateCheck.lastVersion &&
          this.lastUpdateCheck.lastVersion !== currentVersion) {
        this.log(`🔍 检测到版本号变化: ${this.lastUpdateCheck.lastVersion} -> ${currentVersion}`);

        // 更新最后已知版本，避免重复检查
        this.lastUpdateCheck.lastVersion = currentVersion;
        this.saveUpdateStatus(this.lastUpdateCheck);

        // 如果是版本降级或相同版本，也跳过更新检查一段时间
        this.log(`📝 版本变化已记录，跳过本次更新检查`);
        return true;
      }

      return false;
    } catch (error) {
      this.log(`更新完成检测失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查是否有待处理的更新完成标记
   */
  checkPendingUpdateCompletion() {
    try {
      const pendingFile = path.join(app.getPath('userData'), 'pending_update.json');
      if (fs.existsSync(pendingFile)) {
        const data = fs.readFileSync(pendingFile, 'utf8');
        const pending = JSON.parse(data);
        
        // 检查是否在合理时间范围内（1小时内）
        const timeDiff = Date.now() - pending.timestamp;
        if (timeDiff < 60 * 60 * 1000) { // 1小时
          return pending;
        } else {
          this.log('⏰ 待处理更新标记已过期，清除');
          this.clearPendingUpdateMark();
        }
      }
    } catch (error) {
      this.log(`检查待处理更新失败: ${error.message}`);
    }
    return null;
  }

  /**
   * 标记待处理的更新完成（更新过程中调用）
   */
  markPendingUpdateCompletion(expectedVersion) {
    try {
      const pendingFile = path.join(app.getPath('userData'), 'pending_update.json');
      const pendingData = {
        expectedVersion: expectedVersion,
        timestamp: Date.now(),
        created: new Date().toISOString()
      };
      fs.writeFileSync(pendingFile, JSON.stringify(pendingData, null, 2));
      this.log(`📝 已标记待处理更新完成: ${expectedVersion}`);
    } catch (error) {
      this.log(`标记待处理更新失败: ${error.message}`);
    }
  }

  /**
   * 清除待处理的更新标记
   */
  clearPendingUpdateMark() {
    try {
      const pendingFile = path.join(app.getPath('userData'), 'pending_update.json');
      if (fs.existsSync(pendingFile)) {
        fs.unlinkSync(pendingFile);
        this.log('🗑️ 已清除待处理更新标记');
      }
    } catch (error) {
      this.log(`清除待处理更新标记失败: ${error.message}`);
    }
  }

  /**
   * 检查两个版本号是否匹配（考虑格式差异）
   */
  isVersionMatch(version1, version2) {
    if (!version1 || !version2) return false;
    
    // 直接字符串比较
    if (version1 === version2) return true;
    
    // 标准化后比较
    const norm1 = this.normalizeVersion(version1);
    const norm2 = this.normalizeVersion(version2);
    
    const match = norm1 === norm2;
    this.log(`🔍 版本匹配检查: ${version1}(${norm1}) vs ${version2}(${norm2}) = ${match}`);
    return match;
  }

  compareVersionsStrict(version1, version2) {
    this.log(`严格比较版本: ${version1} vs ${version2}`);
    
    // 处理空值情况
    if (!version1 || !version2) {
      this.log('版本号为空，无法比较');
      return 0;
    }

    // 首先进行字符串完全匹配检查
    if (version1.toString() === version2.toString()) {
      this.log('版本号字符串完全相同，返回0');
      return 0;
    }

    // 标准化版本号
    const v1 = this.normalizeVersion(version1);
    const v2 = this.normalizeVersion(version2);
    
    // 标准化后再次检查
    if (v1 === v2) {
      this.log(`标准化后版本号相同: ${v1} === ${v2}，返回0`);
      return 0;
    }
    
    const v1parts = v1.split('.').map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? 0 : num;
    });
    const v2parts = v2.split('.').map(part => {
      const num = parseInt(part, 10);
      return isNaN(num) ? 0 : num;
    });
    
    // 确保两个版本号数组长度相同
    const maxLength = Math.max(v1parts.length, v2parts.length);
    while (v1parts.length < maxLength) v1parts.push(0);
    while (v2parts.length < maxLength) v2parts.push(0);
    
    this.log(`严格解析后的版本号: [${v1parts.join(', ')}] vs [${v2parts.join(', ')}]`);
    
    for (let i = 0; i < maxLength; i++) {
      const v1part = v1parts[i];
      const v2part = v2parts[i];
      
      if (v1part > v2part) {
        this.log(`严格版本比较结果: ${v1} > ${v2} (返回 1)`);
        return 1;
      }
      if (v1part < v2part) {
        this.log(`严格版本比较结果: ${v1} < ${v2} (返回 -1)`);
        return -1;
      }
    }
    
    this.log(`严格版本比较结果: ${v1} = ${v2} (返回 0)`);
    return 0;
  }

  normalizeVersion(version) {
    if (!version) return '0.0.0';
    
    // 移除可能的前缀（如 v1.0.0）
    let clean = version.toString().replace(/^v/, '').trim();
    
    // 分割版本号
    const parts = clean.split('.');
    
    // 补齐缺失的部分
    while (parts.length < 3) {
      parts.push('0');
    }
    
    // 确保每部分都是数字，移除非数字字符
    const normalizedParts = parts.slice(0, 3).map(part => {
      const num = parseInt(part.replace(/[^0-9]/g, ''), 10);
      return isNaN(num) ? 0 : num;
    });
    
    const normalized = normalizedParts.join('.');
    this.log(`版本标准化: ${version} -> ${normalized}`);
    return normalized;
  }

  /**
   * 验证版本号格式是否有效
   */
  isValidVersionFormat(version) {
    if (!version || typeof version !== 'string') {
      this.log(`版本格式验证失败: 版本号为空或不是字符串 - ${version}`);
      return false;
    }

    // 移除可能的前缀
    const clean = version.replace(/^v/, '').trim();

    // 检查是否符合 x.y.z 格式
    if (!/^[0-9]+\.[0-9]+\.[0-9]+$/.test(clean)) {
      this.log(`版本格式验证失败: 不符合x.y.z格式 - ${version}`);
      return false;
    }

    // 检查每个部分是否为有效数字
    const parts = clean.split('.');
    for (const part of parts) {
      const num = parseInt(part, 10);
      if (isNaN(num) || num < 0) {
        this.log(`版本格式验证失败: 包含无效数字部分 - ${version}`);
        return false;
      }
    }

    this.log(`版本格式验证通过: ${version}`);
    return true;
  }

  showUpdateWindow() {
    if (this.updateWindow && !this.updateWindow.isDestroyed()) {
      this.updateWindow.focus();
      return;
    }

    this.log('创建更新弹窗');
    
    this.updateWindow = new BrowserWindow({
      width: 500,
      height: 480,
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      frame: true, // 启用原生窗口框架以显示macOS原生关闭按钮
      titleBarStyle: 'hiddenInset', // macOS专用：隐藏标题栏但保留交通灯按钮
      transparent: false,
      backgroundColor: '#ffffff',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'update-preload.js')
      },
      icon: path.join(__dirname, 'assets/logo.png'),
      show: false
    });

    // 加载更新窗口HTML
    this.updateWindow.loadFile(path.join(__dirname, 'renderer/update.html'));

    this.updateWindow.once('ready-to-show', () => {
      this.updateWindow.show();
      this.updateWindow.center();
      
      // 立即发送更新信息，避免页面闪烁
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('update-info', this.updateInfo);
      }
    });

    this.updateWindow.on('closed', () => {
      this.updateWindow = null;
    });

    // 修改窗口关闭逻辑：任何情况下关闭都退出应用
    this.updateWindow.on('close', (event) => {
      this.log('用户关闭更新窗口，退出应用');
      // 不阻止窗口关闭
      // 直接退出整个应用
      const { app } = require('electron');
      setTimeout(() => {
        app.quit();
      }, 100);
    });
  }

  async startUpdate() {
    if (this.isDownloading || !this.updateInfo) {
      this.log('正在下载或没有更新信息');
      return;
    }

    try {
      this.isDownloading = true;
      this.log(`🚀 开始更新到版本: ${this.updateInfo.version}`);
      this.log(`📦 下载链接: ${this.updateInfo.downloadUrl}`);
      
      // 通知UI开始下载
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('start-download');
      }
      
      // 下载文件
      let downloadedFilePath;
      try {
        downloadedFilePath = await this.downloadFile(this.updateInfo.downloadUrl);
      } catch (primaryError) {
        this.log(`❌ 主下载链接失败: ${primaryError.message}`);
        
        // 尝试备用下载链接
        if (this.updateInfo.backupUrl) {
          this.log(`🔄 尝试备用下载链接: ${this.updateInfo.backupUrl}`);
          try {
            downloadedFilePath = await this.downloadFile(this.updateInfo.backupUrl, true);
          } catch (backupError) {
            this.log(`❌ 备用下载链接也失败: ${backupError.message}`);
            throw new Error('所有下载链接都失败');
          }
        } else {
          throw primaryError;
        }
      }
      
      this.log(`✅ 文件下载完成: ${downloadedFilePath}`);
      
      // 安装更新
      await this.installUpdate(downloadedFilePath);
      
    } catch (error) {
      this.log(`❌ 更新失败: ${error.message}`);
      this.isDownloading = false;
      
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('download-error', error.message);
      }
    }
  }

  /**
   * 下载更新文件
   */
  async downloadFile(url, isUsingBackup = false) {
    return new Promise((resolve, reject) => {
      try {
        const platformInfo = this.getPlatformInfo();
        const fileExtension = platformInfo.platform === 'windows' ? 'exe' : 'dmg';
        const fileName = `update_${Date.now()}.${fileExtension}`;
        const filePath = path.join(this.downloadDir, fileName);
        
        this.log(`📥 开始下载文件到: ${filePath}`);
        if (isUsingBackup) {
          this.log('⚠️ 注意：正在使用备用下载链接');
        }
        
        const writer = fs.createWriteStream(filePath);
        
        axios({
          method: 'GET',
          url: url,
          responseType: 'stream',
          timeout: 300000, // 5分钟超时
          maxRedirects: 20,
          maxContentLength: 500 * 1024 * 1024, // 500MB最大文件大小
          maxBodyLength: 500 * 1024 * 1024,
          validateStatus: function (status) {
            return status >= 200 && status < 400;
          },
          headers: {
            'User-Agent': `XiaoMeiHua-App/${app.getVersion()} (${platformInfo.platform}; ${platformInfo.architecture})`,
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
          }
        }).then(response => {
          this.log(`📊 下载响应状态: ${response.status}`);
          this.log(`🌐 最终下载URL: ${response.request.res.responseUrl || url}`);
          this.log(`📄 响应内容类型: ${response.headers['content-type']}`);
          
          const totalLength = parseInt(response.headers['content-length'], 10);
          let downloadedLength = 0;
          
          if (totalLength > 0) {
            this.log(`📊 文件总大小: ${(totalLength / 1024 / 1024).toFixed(2)} MB`);
          }
          
          response.data.on('data', (chunk) => {
            downloadedLength += chunk.length;
            if (totalLength > 0) {
              const progress = Math.round((downloadedLength / totalLength) * 100);
              this.downloadProgress = progress;

              if (this.updateWindow && !this.updateWindow.isDestroyed()) {
                const statusText = isUsingBackup ? `使用备用链接更新中 ${progress}%` : `更新中 ${progress}%`;
                this.updateWindow.webContents.send('download-progress', {
                  percent: progress,
                  downloaded: downloadedLength,
                  total: totalLength,
                  status: statusText
                });
              }
              
              // 每下载10%打印一次日志
              if (progress % 10 === 0 && progress > 0) {
                const logPrefix = isUsingBackup ? '备用链接下载进度' : '下载进度';
                this.log(`${logPrefix}: ${progress}% (${(downloadedLength / 1024 / 1024).toFixed(2)} MB)`);
              }
            }
          });
          
          response.data.pipe(writer);
          
          writer.on('finish', () => {
            this.log(`✅ 文件下载完成: ${filePath}`);
            // 验证文件是否存在且大小合理
            if (fs.existsSync(filePath)) {
              const stats = fs.statSync(filePath);
              this.log(`📊 下载文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
              if (stats.size > 1024 * 1024) { // 至少1MB
                this.downloadPath = filePath;
                resolve(filePath);
              } else {
                this.log('❌ 文件大小过小，可能下载错误');
                reject(new Error('下载的文件大小异常，可能下载不完整'));
              }
            } else {
              reject(new Error('下载的文件不存在'));
            }
          });
          
          writer.on('error', (error) => {
            this.log(`❌ 文件写入失败: ${error.message}`);
            reject(error);
          });
          
        }).catch(error => {
          this.log(`❌ 下载请求失败: ${error.message}`);
          if (error.response) {
            this.log(`HTTP状态: ${error.response.status}`);
            this.log(`响应头: ${JSON.stringify(error.response.headers)}`);
          }
          reject(error);
        });
        
      } catch (error) {
        this.log(`❌ 下载文件失败: ${error.message}`);
        reject(error);
      }
    });
  }

  /**
   * 安装更新文件
   */
  async installUpdate(filePath) {
    try {
      this.log(`🔧 开始安装更新: ${filePath}`);
      
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('start-install');
      }

      const platformInfo = this.getPlatformInfo();
      
      if (platformInfo.platform === 'windows') {
        // Windows: 执行EXE安装程序
        await this.executeWindowsInstaller(filePath);
      } else if (platformInfo.platform === 'macos') {
        // macOS: 自动挂载DMG并安装
        await this.mountDmgAndInstall(filePath);
      } else {
        throw new Error(`不支持的平台: ${platformInfo.platform}`);
      }

    } catch (error) {
      this.log(`❌ 安装更新失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * Windows安装程序执行
   */
  async executeWindowsInstaller(exePath) {
    return new Promise((resolve, reject) => {
      try {
        this.log(`🔧 执行Windows安装程序: ${exePath}`);
        
        // 标记待处理的更新完成（Windows安装前）
        if (this.updateInfo && this.updateInfo.version) {
          this.markPendingUpdateCompletion(this.updateInfo.version);
          this.log(`📝 已标记Windows待处理更新: ${this.updateInfo.version}`);
        }
        
        // 通知安装完成
        if (this.updateWindow && !this.updateWindow.isDestroyed()) {
          this.updateWindow.webContents.send('install-complete');
        }
        
        // 启动安装程序
        const installer = spawn(exePath, [], {
          detached: true,
          stdio: 'ignore'
        });
        
        installer.unref();
        
        // Windows安装程序会自动处理，这里直接退出当前应用
        setTimeout(() => {
          app.quit();
        }, 2000);
        
        resolve();
        
      } catch (error) {
        this.log(`❌ Windows安装失败: ${error.message}`);
        reject(error);
      }
    });
  }

  /**
   * macOS DMG挂载和安装
   */
  async mountDmgAndInstall(dmgPath) {
    return new Promise((resolve, reject) => {
      try {
        this.log(`🔧 挂载DMG文件: ${dmgPath}`);

        // 验证DMG文件是否存在
        if (!fs.existsSync(dmgPath)) {
          throw new Error(`DMG文件不存在: ${dmgPath}`);
        }

        // 获取文件信息
        const stats = fs.statSync(dmgPath);
        this.log(`📊 DMG文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);

        if (stats.size < 1024 * 1024) { // 小于1MB可能是错误文件
          throw new Error('DMG文件大小异常，可能下载不完整');
        }

        // 使用异步方式执行安装，避免阻塞主线程
        this.performAsyncInstallation(dmgPath, resolve, reject);

      } catch (error) {
        this.log(`❌ DMG安装失败: ${error.message}`);
        reject(error);
      }
    });
  }

  /**
   * 异步执行macOS安装过程
   */
  async performAsyncInstallation(dmgPath, resolve, reject) {
    try {
      const { spawn } = require('child_process');

      let mountOutput = '';
      const mountProcess = spawn('hdiutil', ['attach', dmgPath, '-nobrowse', '-quiet'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      mountProcess.stdout.on('data', (data) => {
        mountOutput += data.toString();
      });

      mountProcess.stderr.on('data', (data) => {
        this.log(`挂载错误输出: ${data.toString()}`);
      });

      mountProcess.on('close', async (code) => {
        if (code !== 0) {
          // 尝试强制挂载
          this.log('尝试强制挂载...');
          const forceMountProcess = spawn('hdiutil', ['attach', dmgPath, '-nobrowse', '-quiet', '-force'], {
            stdio: ['pipe', 'pipe', 'pipe']
          });

          let forceMountOutput = '';
          forceMountProcess.stdout.on('data', (data) => {
            forceMountOutput += data.toString();
          });

          forceMountProcess.on('close', async (forceCode) => {
            if (forceCode !== 0) {
              reject(new Error('DMG挂载失败'));
              return;
            }

            mountOutput = forceMountOutput;
            await this.continueInstallation(mountOutput, dmgPath, resolve, reject);
          });
        } else {
          this.log('✅ DMG挂载成功');
          this.log(`挂载输出: ${mountOutput}`);
          await this.continueInstallation(mountOutput, dmgPath, resolve, reject);
        }
      });

    } catch (error) {
      reject(error);
    }
  }

  /**
   * 继续macOS安装过程
   */
  async continueInstallation(mountOutput, dmgPath, resolve, reject) {
    try {
      // 从挂载输出中提取挂载点
      let mountPoint = null;
      if (mountOutput) {
        const lines = mountOutput.split('\n');
        for (const line of lines) {
          if (line.includes('/Volumes/')) {
            const match = line.match(/\/Volumes\/[^\s]+/);
            if (match) {
              mountPoint = match[0];
              break;
            }
          }
        }
      }

      // 如果没有从输出中找到，尝试查找最新的挂载点
      if (!mountPoint) {
        this.log('从输出中未找到挂载点，搜索/Volumes目录');
        const volumesDir = '/Volumes';
        const volumes = fs.readdirSync(volumesDir);

        // 查找包含app文件的挂载点
        for (const volume of volumes) {
          const volumePath = path.join(volumesDir, volume);
          try {
            if (fs.existsSync(volumePath) && fs.statSync(volumePath).isDirectory()) {
              const files = fs.readdirSync(volumePath);
              const appFile = files.find(file => file.endsWith('.app'));
              if (appFile) {
                mountPoint = volumePath;
                this.log(`✅ 找到挂载点: ${mountPoint}`);
                break;
              }
            }
          } catch (err) {
            // 忽略无法访问的卷
            continue;
          }
        }
      }

      if (!mountPoint || !fs.existsSync(mountPoint)) {
        throw new Error('无法找到DMG挂载点');
      }

      // 查找应用程序文件
      const files = fs.readdirSync(mountPoint);
      const appFile = files.find(file => file.endsWith('.app'));

      if (!appFile) {
        throw new Error('在DMG中未找到应用程序');
      }

      const appPath = path.join(mountPoint, appFile);
      this.log(`✅ 找到应用程序: ${appPath}`);

      // 异步复制应用到Applications目录
      await this.asyncCopyApplication(appPath, mountPoint, dmgPath, resolve, reject);

    } catch (error) {
      this.log(`❌ 安装过程失败: ${error.message}`);
      reject(error);
    }
  }

  /**
   * 异步复制应用程序到Applications目录
   */
  async asyncCopyApplication(appPath, mountPoint, dmgPath, resolve, reject) {
    try {
      const appsDir = '/Applications';
      const appName = path.basename(appPath);
      const targetPath = path.join(appsDir, appName);

      // 如果目标已存在，先删除
      if (fs.existsSync(targetPath)) {
        this.log(`🗑️ 删除旧版本: ${targetPath}`);

        // 使用异步方式删除旧版本
        const { spawn } = require('child_process');
        const rmProcess = spawn('rm', ['-rf', targetPath], {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        await new Promise((rmResolve, rmReject) => {
          rmProcess.on('close', (code) => {
            if (code === 0) {
              this.log('✅ 旧版本删除成功');
              rmResolve();
            } else {
              rmReject(new Error('删除旧版本失败'));
            }
          });
        });
      }

      this.log(`📁 复制应用到: ${targetPath}`);

      // 使用异步方式复制应用
      const cpProcess = spawn('cp', ['-R', appPath, appsDir], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      await new Promise((cpResolve, cpReject) => {
        cpProcess.on('close', (code) => {
          if (code === 0) {
            this.log('✅ 应用复制成功');
            cpResolve();
          } else {
            cpReject(new Error('应用复制失败'));
          }
        });
      });

      // 验证复制是否成功
      if (!fs.existsSync(targetPath)) {
        throw new Error('应用复制失败');
      }

      // 异步卸载DMG和清理文件
      await this.asyncCleanup(mountPoint, dmgPath);

      // 通知安装完成
      if (this.updateWindow && !this.updateWindow.isDestroyed()) {
        this.updateWindow.webContents.send('install-complete');
        this.updateWindow.webContents.send('download-progress', {
          percent: 100,
          downloaded: 0,
          total: 0,
          status: '更新中 100%'
        });
      }

      this.log('✅ 应用安装成功，准备重启');
      this.isDownloading = false;

      // 标记待处理的更新完成（在重启前）
      if (this.updateInfo && this.updateInfo.version) {
        this.markPendingUpdateCompletion(this.updateInfo.version);
        this.log(`📝 已标记待处理更新: ${this.updateInfo.version}，重启后将验证`);
      }

      // 立即重启应用
      this.restartApp(targetPath);

      resolve();

    } catch (error) {
      this.log(`❌ 复制应用失败: ${error.message}`);
      reject(error);
    }
  }

  /**
   * 异步清理DMG和临时文件
   */
  async asyncCleanup(mountPoint, dmgPath) {
    try {
      // 卸载DMG
      this.log(`🔄 卸载DMG: ${mountPoint}`);
      const { spawn } = require('child_process');

      const detachProcess = spawn('hdiutil', ['detach', mountPoint, '-quiet'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      await new Promise((detachResolve) => {
        detachProcess.on('close', (code) => {
          if (code === 0) {
            this.log('✅ DMG卸载成功');
          } else {
            this.log('⚠️ DMG卸载失败，但继续');
          }
          detachResolve();
        });
      });

      // 删除下载的DMG文件
      try {
        fs.unlinkSync(dmgPath);
        this.log('🗑️ 清理下载文件完成');
      } catch (unlinkError) {
        this.log(`⚠️ 清理文件失败: ${unlinkError.message}`);
      }

    } catch (error) {
      this.log(`⚠️ 清理过程失败: ${error.message}`);
    }
  }












  restartApp(appPath = null) {
    try {
      this.log('准备重启应用程序');

      // 关闭更新窗口
      this.closeUpdateWindow();

      if (process.platform === 'darwin' && appPath) {
        // macOS: 使用简化的重启方式，避免多实例问题
        this.log(`重启到新版本应用: ${appPath}`);
        this.performSimpleRestart(appPath);
      } else {
        // 其他平台或没有指定路径时直接退出
        this.log('退出当前应用');
        app.exit(0);
      }

    } catch (error) {
      this.log(`重启应用失败: ${error.message}`);
      // 确保无论如何都要退出应用
      setTimeout(() => {
        app.exit(0);
      }, 500);
    }
  }

  // 执行简化的应用重启，避免多实例问题
  performSimpleRestart(appPath) {
    try {
      this.log('开始执行简化的应用重启');
      
      // 验证新应用是否存在
      if (!fs.existsSync(appPath)) {
        throw new Error(`新应用不存在: ${appPath}`);
      }

      // 使用延迟启动机制，确保当前应用完全退出后再启动新应用
      this.scheduleDelayedRestart(appPath);
      
      // 立即关闭当前应用
      this.log('立即关闭当前应用');
      setTimeout(() => {
        app.exit(0);
      }, 100);

    } catch (error) {
      this.log(`简化重启失败: ${error.message}`);
      // 最后的保险措施
      setTimeout(() => {
        app.exit(0);
      }, 500);
    }
  }

  // 安排延迟重启，确保当前应用完全退出后再启动新应用
  scheduleDelayedRestart(appPath) {
    const { spawn } = require('child_process');
    
    try {
      this.log(`安排延迟重启: ${appPath}`);
      
      // 创建一个独立的脚本来延迟启动新应用
      const restartScript = `
        #!/bin/bash
        sleep 2
        open "${appPath}"
      `;
      
      const scriptPath = path.join(app.getPath('userData'), 'restart.sh');
      fs.writeFileSync(scriptPath, restartScript);
      fs.chmodSync(scriptPath, '755');
      
      // 启动独立的重启脚本
      const restartProcess = spawn('bash', [scriptPath], {
        detached: true,
        stdio: 'ignore'
      });
      
      restartProcess.unref();
      this.log('✅ 延迟重启脚本已启动');
      
    } catch (error) {
      this.log(`安排延迟重启失败: ${error.message}`);
      // 备用方案：直接使用系统命令
      try {
        const { spawn } = require('child_process');
        const delayedOpen = spawn('bash', ['-c', `sleep 2 && open "${appPath}"`], {
          detached: true,
          stdio: 'ignore'
        });
        delayedOpen.unref();
        this.log('✅ 备用延迟重启已启动');
      } catch (backupError) {
        this.log(`备用延迟重启也失败: ${backupError.message}`);
      }
    }
  }

  // 已删除launchNewAppImmediately方法，使用新的延迟重启机制

  // 立即关闭所有窗口
  closeAllWindowsImmediately() {
    try {
      this.log('立即关闭所有窗口...');
      const allWindows = require('electron').BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        if (!window.isDestroyed()) {
          try {
            window.destroy(); // 使用destroy而不是close，立即销毁窗口
          } catch (destroyError) {
            this.log(`销毁窗口失败: ${destroyError.message}`);
          }
        }
      });
      this.updateWindow = null;
      this.log('所有窗口已立即关闭');
    } catch (error) {
      this.log(`关闭窗口失败: ${error.message}`);
    }
  }

  // 已删除setupBackupLaunchMechanism方法，避免多实例问题



  // 强制退出当前应用
  forceExitCurrentApp() {
    this.log('🔄 强制退出当前应用，新应用应该已经启动');

    try {
      // 确保所有窗口都已关闭
      const allWindows = require('electron').BrowserWindow.getAllWindows();
      if (allWindows.length > 0) {
        this.log(`还有 ${allWindows.length} 个窗口未关闭，立即关闭`);
        allWindows.forEach(window => {
          if (!window.isDestroyed()) {
            window.destroy();
          }
        });
      }

      // 立即退出，不等待
      this.log('执行 app.exit(0)');
      app.exit(0);
    } catch (exitError) {
      this.log(`app.exit()失败: ${exitError.message}`);
      try {
        this.log('执行 process.exit(0)');
        process.exit(0);
      } catch (processExitError) {
        this.log(`process.exit()失败: ${processExitError.message}`);
      }
    }
  }



  // 安全退出当前应用（保留原有方法，用于非更新场景）
  exitCurrentApp() {
    this.log('开始安全退出当前应用');

    try {
      // 关闭所有窗口
      const allWindows = require('electron').BrowserWindow.getAllWindows();
      allWindows.forEach(window => {
        if (!window.isDestroyed()) {
          try {
            window.close();
          } catch (closeError) {
            this.log(`关闭窗口失败: ${closeError.message}`);
          }
        }
      });

      // 延迟退出，确保窗口完全关闭
      setTimeout(() => {
        this.log('执行应用退出');
        try {
          app.quit();
        } catch (quitError) {
          this.log(`app.quit()失败，使用app.exit(): ${quitError.message}`);
          app.exit(0);
        }
      }, 500);

    } catch (error) {
      this.log(`退出应用失败: ${error.message}`);
      // 最后的保险措施
      setTimeout(() => {
        process.exit(0);
      }, 1000);
    }
  }

  closeUpdateWindow() {
    if (this.updateWindow && !this.updateWindow.isDestroyed()) {
      this.updateWindow.close();
      this.updateWindow = null;

      // 通知主进程更新窗口已关闭，可以启动弹窗管理器
      this.notifyUpdateWindowClosed();
    }
  }

  /**
   * 通知主进程更新窗口已关闭
   */
  notifyUpdateWindowClosed() {
    try {
      // 直接访问全局弹窗管理器实例并启动
      if (global.popupManager && !global.popupManager.isStarted()) {
        console.log('🚀 更新窗口关闭，启动弹窗管理器');
        global.popupManager.start({ clearHistory: false });
      }
    } catch (error) {
      console.error('❌ 通知弹窗管理器启动失败:', error);
    }
  }

  // 获取下载进度
  getDownloadProgress() {
    return this.downloadProgress;
  }

  // 检查是否正在下载
  isUpdateDownloading() {
    return this.isDownloading;
  }






  // 取消下载
  cancelUpdate() {
    this.isDownloading = false;
    this.downloadProgress = 0;
    this.closeUpdateWindow();
    this.log('用户取消更新');
  }
}

module.exports = AppUpdater;