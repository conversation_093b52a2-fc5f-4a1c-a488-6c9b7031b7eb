/**
 * 弹窗预加载脚本
 * 为弹窗窗口提供API通信接口
 */

const { contextBridge, ipcRenderer } = require('electron');

// 向渲染进程暴露API
contextBridge.exposeInMainWorld('popupAPI', {
    // 获取当前弹窗数据
    getCurrentPopup: () => ipcRenderer.invoke('get-current-popup'),
    
    // 关闭弹窗
    closePopup: (popupId) => ipcRenderer.invoke('popup-close', popupId),
    
    // 记录弹窗点击
    clickPopup: (popupId, action) => ipcRenderer.invoke('popup-click', popupId, action),
    
    // 打开外部链接
    openExternal: (url) => ipcRenderer.invoke('open-external', url)
});

// 注释掉重复的DOM监听器，因为popup.html中已经有了
// document.addEventListener('DOMContentLoaded', async () => {
//     console.log('📄 DOM加载完成，开始初始化弹窗');
//     // 这部分逻辑已经移到popup.html中
// });

// renderPopup函数已经移到popup.html中，这里不再需要

// 添加一些样式优化
document.addEventListener('DOMContentLoaded', () => {
    // 添加拖拽功能
    const titleBar = document.getElementById('popup-title-bar');
    if (titleBar) {
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        
        titleBar.addEventListener('mousedown', (e) => {
            isDragging = true;
            dragOffset.x = e.clientX;
            dragOffset.y = e.clientY;
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - dragOffset.x;
                const deltaY = e.clientY - dragOffset.y;
                // 通过IPC通知主进程移动窗口
                // 这里需要添加相应的IPC处理
            }
        });
        
        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
    }
    
    // 添加动画效果
    const container = document.getElementById('popup-container');
    if (container) {
        container.style.opacity = '0';
        container.style.transform = 'scale(0.8)';
        
        // 渐入动画
        setTimeout(() => {
            container.style.transition = 'all 0.3s ease-out';
            container.style.opacity = '1';
            container.style.transform = 'scale(1)';
        }, 100);
    }
});