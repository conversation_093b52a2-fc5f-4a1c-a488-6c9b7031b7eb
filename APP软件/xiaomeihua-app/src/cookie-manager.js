/**
 * cookie-manager.js
 * 增强的Cookie管理模块，确保微信网页登录状态长期保持有效
 * 基于参考代码的成功方法重新实现
 */

const { app, session } = require('electron');
const path = require('path');
const fs = require('fs');

// Cookie存储路径 - 延迟初始化，等app ready后再设置
let COOKIES_DIR = null;
let COOKIES_FILE = null;
const DOMAIN_COOKIES_PREFIX = 'domain-cookies-';

// 初始化存储路径的函数
function initializeStoragePaths() {
  if (!COOKIES_DIR) {
    try {
      COOKIES_DIR = path.join(app.getPath('userData'), 'persistent-cookies');
      COOKIES_FILE = path.join(COOKIES_DIR, 'cookies.json');

      // 确保目录存在
      if (!fs.existsSync(COOKIES_DIR)) {
        fs.mkdirSync(COOKIES_DIR, { recursive: true });
        console.log('✅ 创建Cookie存储目录:', COOKIES_DIR);

        // 【Windows平台优化】设置目录权限
        if (process.platform === 'win32') {
          try {
            // 确保当前用户有完全控制权限
            const { execSync } = require('child_process');
            const username = require('os').userInfo().username;
            execSync(`icacls "${COOKIES_DIR}" /grant "${username}:(OI)(CI)F" /T`, { stdio: 'ignore' });
            console.log('✅ Windows平台Cookie目录权限设置完成');
          } catch (permError) {
            console.warn('⚠️ Windows平台权限设置失败，但不影响基本功能:', permError.message);
          }
        }
      }

      // 测试目录写入权限
      const testFile = path.join(COOKIES_DIR, 'test-write.tmp');
      try {
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        console.log('✅ Cookie存储目录写入权限正常:', COOKIES_DIR);
      } catch (writeError) {
        console.error('❌ Cookie存储目录写入权限异常:', writeError);
        throw writeError;
      }
    } catch (error) {
      console.error('❌ 初始化主存储路径失败:', error);

      // 【Windows平台备用路径】使用AppData目录
      const os = require('os');
      if (process.platform === 'win32') {
        COOKIES_DIR = path.join(os.homedir(), 'AppData', 'Local', 'xiaomeihua-cookies');
      } else {
        COOKIES_DIR = path.join(os.homedir(), '.xiaomeihua-cookies');
      }

      COOKIES_FILE = path.join(COOKIES_DIR, 'cookies.json');

      if (!fs.existsSync(COOKIES_DIR)) {
        fs.mkdirSync(COOKIES_DIR, { recursive: true });
      }

      console.log('✅ 使用备用Cookie存储目录:', COOKIES_DIR);
    }
  }
}

// 关键域名列表
const KEY_DOMAINS = [
  'weixin.qq.com',
  'store.weixin.qq.com',
  'shop.weixin.qq.com',
  'wx.qq.com',
  'channels.weixin.qq.com',
  'filehelper.weixin.qq.com'
];

// 关键Cookie名称关键词
const KEY_COOKIE_KEYWORDS = [
  'login',
  'ticket',
  'token',
  'auth',
  'session',
  'wxticket',
  'pass',
  'user',
  'id',
  'key',
  'wx',
  'qq'
];

// Cookie管理器类
class CookieManager {
  constructor() {
    this.sessionInstance = null; // 默认session
    this.sessionInstances = new Map(); // 存储多个session实例
    this.cookieUpdateInterval = null;
    this.initialized = false;
    this.sessionListeners = new Map(); // 存储每个session的监听器
  }

  /**
   * 获取或创建session实例
   */
  getOrCreateSession(partition = 'default') {
    if (partition === 'default') {
      return this.sessionInstance || session.defaultSession;
    }

    if (!this.sessionInstances.has(partition)) {
      console.log(`🔄 创建新的session分区: ${partition}`);
      const sessionInstance = session.fromPartition(partition);
      this.sessionInstances.set(partition, sessionInstance);

      // 为新session设置Cookie拦截
      this.setupCookieInterceptionForSession(sessionInstance, partition);
    }

    return this.sessionInstances.get(partition);
  }

  /**
   * 初始化Cookie管理器
   */
  async initialize() {
    if (this.initialized) return;

    console.log('🔄 初始化Cookie管理器...');

    try {
      // 确保app已经ready
      if (!app.isReady()) {
        console.log('App尚未准备好，等待app ready事件...');
        await new Promise(resolve => app.once('ready', resolve));
      }

      // 初始化存储路径
      initializeStoragePaths();

      // 现在可以安全地获取默认session
      this.sessionInstance = session.defaultSession;

      // 配置默认session的Cookie设置拦截
      this.setupCookieInterceptionForSession(this.sessionInstance, 'default');

      // 预先创建所有已知店铺的session分区
      await this.initializeShopSessions();

      // 恢复保存的Cookie到所有session
      await this.restoreAllCookies();

      // 设置定期保存和更新Cookie
      this.startPeriodicCookieUpdate();

      this.initialized = true;
      console.log('✅ Cookie管理器初始化完成');
    } catch (error) {
      console.error('❌ Cookie管理器初始化失败:', error);
    }
  }

  /**
   * 初始化所有店铺的session分区
   */
  async initializeShopSessions() {
    try {
      // 获取店铺信息
      const Store = require('electron-store');
      const store = new Store({
        name: 'xiaomeihua-config',
        encryptionKey: 'xiaomeihua-secure-key-2025',
        clearInvalidConfig: true,
        serialize: (value) => JSON.stringify(value, null, 2),
        deserialize: (value) => {
          try {
            return JSON.parse(value);
          } catch (error) {
            console.error('反序列化配置失败:', error);
            return {};
          }
        }
      });
      const shopInfo = store.get('shop_info');

      if (shopInfo && shopInfo.shops && Array.isArray(shopInfo.shops)) {
        console.log(`🏪 发现 ${shopInfo.shops.length} 个店铺，预先创建session分区...`);

        for (const shop of shopInfo.shops) {
          const partition = `persist:shop_${shop.id}`;
          console.log(`🔧 预先创建店铺session分区: ${partition} (${shop.name})`);
          this.getOrCreateSession(partition);
        }
      } else {
        console.log('📋 未发现店铺信息，跳过店铺session预创建');
      }
    } catch (error) {
      console.error('❌ 初始化店铺session失败:', error);
    }
  }

  /**
   * 为指定session配置Cookie拦截，防止Cookie过期
   */
  setupCookieInterceptionForSession(sessionInstance, partition) {
    if (!sessionInstance) {
      console.error(`无法设置Cookie拦截：session未初始化 (${partition})`);
      return;
    }

    console.log(`🔧 为session分区设置Cookie拦截: ${partition}`);

    // 拦截响应头中的Set-Cookie，修改过期时间
    sessionInstance.webRequest.onHeadersReceived((details, callback) => {
      if (!this.isKeyDomain(details.url)) {
        return callback({ responseHeaders: details.responseHeaders });
      }

      console.log(`🔍 [${partition}] 拦截到关键域名请求: ${details.url}`);
      const responseHeaders = { ...details.responseHeaders };

      // 处理Set-Cookie头
      if (responseHeaders['set-cookie']) {
        const cookies = responseHeaders['set-cookie'];
        const modifiedCookies = cookies.map(cookie => {
          // 移除过期时间，或设置为10年
          return cookie
            .replace(/; expires=[^;]+/gi, '')
            .replace(/; max-age=[^;]+/gi, '; max-age=315360000'); // 10年
        });

        responseHeaders['set-cookie'] = modifiedCookies;
        console.log(`🍪 [${partition}] 修改了 ${cookies.length} 个Cookie的过期时间 (域名: ${new URL(details.url).hostname})`);
      }

      callback({ responseHeaders });
    });

    // 监听Cookie变化事件
    const cookieListener = async (event, cookie, cause, removed) => {
      if (removed) return;

      // 如果是关键域名的Cookie变化，立即保存
      if (this.isKeyDomain(cookie.domain) || this.isKeyCookie(cookie.name)) {
        console.log(`🍪 [${partition}] Cookie变化 (${cause}): ${cookie.name} 在 ${cookie.domain}`);
        await this.saveCookiesForSession(sessionInstance, partition);
      }
    };

    sessionInstance.cookies.on('changed', cookieListener);

    // 存储监听器引用，以便后续清理
    this.sessionListeners.set(partition, cookieListener);
  }

  /**
   * 判断是否是关键域名
   */
  isKeyDomain(urlOrDomain) {
    if (!urlOrDomain) return false;
    
    // 提取域名
    let domain = urlOrDomain;
    if (urlOrDomain.startsWith('http')) {
      try {
        domain = new URL(urlOrDomain).hostname;
      } catch (e) {
        return false;
      }
    }
    
    // 检查是否是关键域名
    return KEY_DOMAINS.some(keyDomain => domain.includes(keyDomain));
  }

  /**
   * 判断是否是关键Cookie
   */
  isKeyCookie(cookieName) {
    if (!cookieName) return false;
    return KEY_COOKIE_KEYWORDS.some(keyword => 
      cookieName.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * 保存所有session的Cookie到文件
   */
  async saveCookies() {
    // 确保存储路径已初始化
    if (!COOKIES_DIR) {
      initializeStoragePaths();
    }

    try {
      console.log('💾 保存所有session的Cookie...');

      let totalCookies = 0;
      const allSessionCookies = {};

      // 保存默认session的Cookie
      if (this.sessionInstance) {
        const defaultCookies = await this.sessionInstance.cookies.get({});
        allSessionCookies['default'] = defaultCookies;
        totalCookies += defaultCookies.length;
        console.log(`📊 默认session包含 ${defaultCookies.length} 个Cookie`);
      }

      // 保存所有店铺session的Cookie
      for (const [partition, sessionInstance] of this.sessionInstances) {
        try {
          const sessionCookies = await sessionInstance.cookies.get({});
          allSessionCookies[partition] = sessionCookies;
          totalCookies += sessionCookies.length;
          console.log(`📊 Session ${partition} 包含 ${sessionCookies.length} 个Cookie`);
        } catch (error) {
          console.error(`获取session ${partition} 的Cookie失败:`, error);
        }
      }

      // 保存所有session的Cookie到主文件
      const mainCookieData = {
        saveTime: Date.now(),
        sessions: allSessionCookies
      };
      fs.writeFileSync(COOKIES_FILE, JSON.stringify(mainCookieData, null, 2));
      console.log(`📁 主Cookie文件已保存: ${COOKIES_FILE}`);

      // 按session分区保存Cookie到单独文件
      for (const [sessionKey, cookies] of Object.entries(allSessionCookies)) {
        const sessionFile = path.join(COOKIES_DIR, `session-${sessionKey}.json`);
        const sessionData = {
          partition: sessionKey,
          saveTime: Date.now(),
          cookies: cookies
        };
        fs.writeFileSync(sessionFile, JSON.stringify(sessionData, null, 2));
        console.log(`📁 Session文件已保存: ${sessionKey} (${cookies.length}个Cookie)`);
      }

      console.log(`✅ 成功保存 ${totalCookies} 个Cookie到文件 (${Object.keys(allSessionCookies).length}个session)`);
      console.log(`📂 Cookie存储目录: ${COOKIES_DIR}`);
    } catch (error) {
      console.error('❌ 保存Cookie失败:', error);
    }
  }

  /**
   * 为特定session保存Cookie
   */
  async saveCookiesForSession(sessionInstance, partition) {
    if (!sessionInstance) {
      console.error(`无法保存Cookie：session未初始化 (${partition})`);
      return;
    }

    // 确保存储路径已初始化
    if (!COOKIES_DIR) {
      initializeStoragePaths();
    }

    try {
      console.log(`💾 保存session ${partition} 的Cookie...`);

      // 获取该session的所有Cookie
      const sessionCookies = await sessionInstance.cookies.get({});

      // 保存到session特定文件
      const sessionFile = path.join(COOKIES_DIR, `session-${partition}.json`);
      const sessionData = {
        partition: partition,
        saveTime: Date.now(),
        cookies: sessionCookies
      };
      fs.writeFileSync(sessionFile, JSON.stringify(sessionData, null, 2));

      console.log(`✅ 成功保存session ${partition} 的 ${sessionCookies.length} 个Cookie`);
    } catch (error) {
      console.error(`❌ 保存session ${partition} 的Cookie失败:`, error);
    }
  }

  /**
   * 恢复所有保存的Cookie到对应的session
   */
  async restoreAllCookies() {
    // 确保存储路径已初始化
    if (!COOKIES_DIR) {
      initializeStoragePaths();
    }

    try {
      console.log('🔄 恢复所有session的Cookie...');

      let totalRestored = 0;

      // 首先尝试从新格式的主文件恢复
      if (fs.existsSync(COOKIES_FILE)) {
        console.log(`📁 找到主Cookie文件: ${COOKIES_FILE}`);
        const cookiesData = fs.readFileSync(COOKIES_FILE, 'utf-8');
        const data = JSON.parse(cookiesData);

        // 检查是否是新格式（包含sessions字段）
        if (data.sessions) {
          console.log(`📊 新格式主文件包含 ${Object.keys(data.sessions).length} 个session`);

          for (const [sessionKey, cookies] of Object.entries(data.sessions)) {
            const sessionInstance = sessionKey === 'default'
              ? this.sessionInstance
              : this.getOrCreateSession(sessionKey);

            if (sessionInstance && Array.isArray(cookies)) {
              console.log(`🔄 恢复session ${sessionKey} 的 ${cookies.length} 个Cookie`);
              for (const cookie of cookies) {
                try {
                  await this.restoreSingleCookieToSession(cookie, sessionInstance);
                  totalRestored++;
                } catch (err) {
                  console.error(`恢复Cookie ${cookie.name} 到session ${sessionKey} 失败:`, err);
                }
              }
            }
          }
        } else if (Array.isArray(data)) {
          // 兼容旧格式
          console.log(`📊 旧格式主文件包含 ${data.length} 个Cookie，恢复到默认session`);
          if (this.sessionInstance) {
            for (const cookie of data) {
              try {
                await this.restoreSingleCookieToSession(cookie, this.sessionInstance);
                totalRestored++;
              } catch (err) {
                console.error(`恢复Cookie ${cookie.name} 失败:`, err);
              }
            }
          }
        }
      } else {
        console.log(`📁 主Cookie文件不存在: ${COOKIES_FILE}`);
      }

      // 然后从session特定文件恢复
      const files = fs.readdirSync(COOKIES_DIR);
      const sessionFiles = files.filter(file => file.startsWith('session-') && file.endsWith('.json'));

      for (const file of sessionFiles) {
        try {
          const filePath = path.join(COOKIES_DIR, file);
          const sessionData = fs.readFileSync(filePath, 'utf-8');
          const data = JSON.parse(sessionData);

          if (data.partition && data.cookies) {
            const sessionInstance = data.partition === 'default'
              ? this.sessionInstance
              : this.getOrCreateSession(data.partition);

            if (sessionInstance && Array.isArray(data.cookies)) {
              console.log(`🔄 从文件恢复session ${data.partition} 的 ${data.cookies.length} 个Cookie`);
              for (const cookie of data.cookies) {
                try {
                  await this.restoreSingleCookieToSession(cookie, sessionInstance);
                  totalRestored++;
                } catch (err) {
                  // 忽略重复错误
                  if (!err.message.includes('already exists')) {
                    console.error(`从session文件恢复Cookie ${cookie.name} 失败:`, err);
                  }
                }
              }
            }
          }
        } catch (err) {
          console.error(`处理session Cookie文件 ${file} 失败:`, err);
        }
      }

      console.log(`✅ 成功恢复 ${totalRestored} 个Cookie到 ${this.sessionInstances.size + 1} 个session`);
    } catch (error) {
      console.error('❌ 恢复Cookie失败:', error);
    }
  }

  /**
   * 恢复单个Cookie到默认session
   */
  async restoreSingleCookie(cookie) {
    if (!this.sessionInstance) {
      console.error('无法恢复单个Cookie：session未初始化');
      return;
    }

    await this.restoreSingleCookieToSession(cookie, this.sessionInstance);
  }

  /**
   * 恢复单个Cookie到指定session
   */
  async restoreSingleCookieToSession(cookie, sessionInstance) {
    if (!sessionInstance) {
      console.error('无法恢复单个Cookie：session未初始化');
      return;
    }

    // 确保Cookie不会过期
    cookie.expirationDate = Math.floor(Date.now() / 1000) + 315360000; // 10年

    // 确保必要属性存在
    if (cookie.secure === undefined) cookie.secure = true;
    if (cookie.httpOnly === undefined) cookie.httpOnly = true;
    if (!cookie.path) cookie.path = '/';

    // 处理域名问题
    if (cookie.domain && cookie.domain.startsWith('.')) {
      cookie.domain = cookie.domain.substring(1);
    }

    // 构建URL
    const url = cookie.secure
      ? `https://${cookie.domain}${cookie.path}`
      : `http://${cookie.domain}${cookie.path}`;

    // 设置Cookie
    await sessionInstance.cookies.set({
      url: url,
      name: cookie.name,
      value: cookie.value,
      domain: cookie.domain,
      path: cookie.path,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      expirationDate: cookie.expirationDate,
      sameSite: cookie.sameSite || 'no_restriction'
    });
  }

  /**
   * 开始定期更新Cookie
   */
  startPeriodicCookieUpdate() {
    // 清除现有的定时器
    if (this.cookieUpdateInterval) {
      clearInterval(this.cookieUpdateInterval);
    }
    
    // 每5分钟保存一次Cookie
    this.cookieUpdateInterval = setInterval(async () => {
      await this.saveCookies();
      await this.refreshCookies();
    }, 5 * 60 * 1000);
    
    console.log('⏰ 已启动定期Cookie更新');
  }

  /**
   * 刷新所有session中关键Cookie的过期时间
   */
  async refreshCookies() {
    try {
      let totalRefreshed = 0;

      // 刷新默认session的Cookie
      if (this.sessionInstance) {
        const refreshed = await this.refreshCookiesForSession(this.sessionInstance, 'default');
        totalRefreshed += refreshed;
      }

      // 刷新所有店铺session的Cookie
      for (const [partition, sessionInstance] of this.sessionInstances) {
        try {
          const refreshed = await this.refreshCookiesForSession(sessionInstance, partition);
          totalRefreshed += refreshed;
        } catch (error) {
          console.error(`刷新session ${partition} 的Cookie失败:`, error);
        }
      }

      console.log(`🔄 已刷新 ${totalRefreshed} 个关键Cookie的过期时间 (${this.sessionInstances.size + 1}个session)`);
    } catch (error) {
      console.error('刷新Cookie过期时间失败:', error);
    }
  }

  /**
   * 刷新指定session中关键Cookie的过期时间
   */
  async refreshCookiesForSession(sessionInstance, partition) {
    if (!sessionInstance) {
      console.error(`无法刷新Cookie：session未初始化 (${partition})`);
      return 0;
    }

    try {
      // 获取该session的所有Cookie
      const cookies = await sessionInstance.cookies.get({});

      // 筛选关键Cookie
      const keyCookies = cookies.filter(cookie =>
        this.isKeyDomain(cookie.domain) || this.isKeyCookie(cookie.name)
      );

      // 更新每个关键Cookie的过期时间
      for (const cookie of keyCookies) {
        try {
          // 构建URL
          const url = cookie.secure
            ? `https://${cookie.domain}${cookie.path}`
            : `http://${cookie.domain}${cookie.path}`;

          // 设置相同的Cookie但更新过期时间
          await sessionInstance.cookies.set({
            url: url,
            name: cookie.name,
            value: cookie.value,
            domain: cookie.domain,
            path: cookie.path,
            secure: cookie.secure,
            httpOnly: cookie.httpOnly,
            expirationDate: Math.floor(Date.now() / 1000) + 315360000, // 10年
            sameSite: cookie.sameSite || 'no_restriction'
          });
        } catch (err) {
          console.error(`刷新session ${partition} 的Cookie ${cookie.name} 失败:`, err);
        }
      }

      console.log(`🔄 [${partition}] 已刷新 ${keyCookies.length} 个关键Cookie的过期时间`);
      return keyCookies.length;
    } catch (error) {
      console.error(`刷新session ${partition} 的Cookie过期时间失败:`, error);
      return 0;
    }
  }

  /**
   * 手动触发保存Cookie
   */
  async forceSaveCookies() {
    await this.saveCookies();
  }

  /**
   * 【增强】清除登录状态 - 完整清理版本
   */
  async clearLoginState() {
    try {
      console.log('🧹 开始完整清除登录状态...');

      // 确保存储路径已初始化
      if (!COOKIES_DIR) {
        initializeStoragePaths();
      }

      // 1. 清除登录状态文件
      const loginStateFile = path.join(COOKIES_DIR, 'login-state.json');
      if (fs.existsSync(loginStateFile)) {
        fs.unlinkSync(loginStateFile);
        console.log('✅ 已删除登录状态文件');
      }

      // 【新增】2. 清除所有登录相关的持久化文件
      const filesToClear = [
        'login-state.json',
        'session-state.json',
        'auth-state.json',
        'user-state.json',
        'shop-state.json'
      ];

      filesToClear.forEach(fileName => {
        const filePath = path.join(COOKIES_DIR, fileName);
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`✅ 已删除文件: ${fileName}`);
        }
      });

      // 2. 清除会话备份文件
      const sessionBackupFile = path.join(COOKIES_DIR, 'session-backup.json');
      if (fs.existsSync(sessionBackupFile)) {
        fs.unlinkSync(sessionBackupFile);
        console.log('✅ 已删除会话备份文件');
      }

      // 3. 清除所有Cookie备份文件
      if (fs.existsSync(COOKIES_DIR)) {
        const files = fs.readdirSync(COOKIES_DIR);
        files.forEach(file => {
          if (file.includes('backup') || file.includes('cookies-') || file.includes('session-')) {
            const filePath = path.join(COOKIES_DIR, file);
            try {
              fs.unlinkSync(filePath);
              console.log(`✅ 已删除备份文件: ${file}`);
            } catch (err) {
              console.error(`删除备份文件失败: ${file}`, err);
            }
          }
        });
      }

      // 4. 清除用户数据目录下的其他登录相关文件
      const userDataPath = app.getPath('userData');
      const loginRelatedFiles = [
        'session-data.json',
        'cookies-data.json',
        'local-storage-data.json',
        'document-cookies.txt'
      ];

      loginRelatedFiles.forEach(fileName => {
        const filePath = path.join(userDataPath, fileName);
        if (fs.existsSync(filePath)) {
          try {
            fs.unlinkSync(filePath);
            console.log(`✅ 已删除登录文件: ${fileName}`);
          } catch (err) {
            console.error(`删除登录文件失败: ${fileName}`, err);
          }
        }
      });

      // 5. 清除所有Cookie文件（包括按域名分组的）
      const cookieFiles = fs.readdirSync(userDataPath).filter(file =>
        file.startsWith('cookie-') && file.endsWith('.txt')
      );

      cookieFiles.forEach(file => {
        const filePath = path.join(userDataPath, file);
        try {
          fs.unlinkSync(filePath);
          console.log(`✅ 已删除Cookie文件: ${file}`);
        } catch (err) {
          console.error(`删除Cookie文件失败: ${file}`, err);
        }
      });

      console.log('🎯 登录状态完整清除完成');
    } catch (error) {
      console.error('❌ 清除登录状态失败:', error);
    }
  }

  /**
   * 专门清理微信小店相关的会话状态
   * 解决ERR_TOO_MANY_REDIRECTS问题
   */
  async clearWeixinStoreSession() {
    console.log('🔄 清理微信小店会话状态...');

    try {
      // 1. 清理所有微信相关域名的Cookie
      const weixinDomains = [
        'store.weixin.qq.com',
        'filehelper.weixin.qq.com',
        'channels.weixin.qq.com',
        'weixin.qq.com',
        'wx.qq.com',
        'mp.weixin.qq.com'
      ];

      // 【关键修复】完全清理session分区的所有数据
      console.log('🔄 开始完全清理微信小店session分区...');

      // 清理默认session
      if (this.sessionInstance) {
        for (const domain of weixinDomains) {
          try {
            // 获取所有相关Cookie并逐个删除
            const cookies = await this.sessionInstance.cookies.get({ domain: domain });
            for (const cookie of cookies) {
              const url = cookie.secure ? `https://${cookie.domain}` : `http://${cookie.domain}`;
              await this.sessionInstance.cookies.remove(url, cookie.name);
            }
            console.log(`✅ 已清理域名 ${domain} 的 ${cookies.length} 个Cookie`);
          } catch (err) {
            console.log(`⚠️ 清理域名 ${domain} 的Cookie失败:`, err.message);
          }
        }

        // 清理localStorage和sessionStorage
        try {
          await this.sessionInstance.clearStorageData({
            storages: ['localstorage', 'sessionstorage', 'websql', 'indexdb', 'cachestorage']
          });
          console.log('✅ 已清理默认session的存储数据');
        } catch (err) {
          console.log('⚠️ 清理默认session存储数据失败:', err.message);
        }

        // 强制清理HTTP缓存
        try {
          await this.sessionInstance.clearCache();
          console.log('✅ 已清理默认session的HTTP缓存');
        } catch (err) {
          console.log('⚠️ 清理默认session的HTTP缓存失败:', err.message);
        }
      }

      // 2. 清理所有店铺session分区
      for (const [partition, sessionInstance] of this.sessionInstances) {
        try {
          for (const domain of weixinDomains) {
            // 获取所有相关Cookie并逐个删除
            const cookies = await sessionInstance.cookies.get({ domain: domain });
            for (const cookie of cookies) {
              const url = cookie.secure ? `https://${cookie.domain}` : `http://${cookie.domain}`;
              await sessionInstance.cookies.remove(url, cookie.name);
            }
          }

          // 清理该session的存储数据
          await sessionInstance.clearStorageData({
            storages: ['localstorage', 'sessionstorage', 'websql', 'indexdb', 'cachestorage']
          });

          // 强制清理HTTP缓存
          await sessionInstance.clearCache();

          console.log(`✅ 已清理session分区 ${partition} 的微信数据`);
        } catch (err) {
          console.log(`⚠️ 清理session分区 ${partition} 失败:`, err.message);
        }
      }

      // 3. 删除保存的微信相关Cookie文件
      if (COOKIES_DIR && fs.existsSync(COOKIES_DIR)) {
        const files = fs.readdirSync(COOKIES_DIR);
        for (const file of files) {
          if (file.includes('weixin') || file.includes('store') || file.includes('wx')) {
            try {
              const filePath = path.join(COOKIES_DIR, file);
              fs.unlinkSync(filePath);
              console.log(`✅ 已删除Cookie文件: ${file}`);
            } catch (err) {
              console.log(`⚠️ 删除Cookie文件 ${file} 失败:`, err.message);
            }
          }
        }
      }

      // 【关键修复】完全重置微信小店相关的session分区
      console.log('🔄 开始完全重置微信小店session分区...');

      // 4. 获取所有微信小店相关的session分区
      const weixinPartitions = [];
      for (const [partition, sessionInstance] of this.sessionInstances) {
        if (partition.includes('shop_') || partition.includes('weixin')) {
          weixinPartitions.push({ partition, sessionInstance });
        }
      }

      // 5. 完全重置每个微信小店session分区
      for (const { partition, sessionInstance } of weixinPartitions) {
        console.log(`🗑️ 完全重置session分区: ${partition}`);

        try {
          // 清理所有存储数据（包括HTTP认证状态）
          await sessionInstance.clearStorageData({
            storages: [
              'cookies',
              'localstorage',
              'sessionstorage',
              'websql',
              'indexdb',
              'cachestorage',
              'serviceworkers',
              'appcache'
            ]
          });

          // 清理HTTP缓存和连接池
          await sessionInstance.clearCache();

          // 【关键】清理HTTP认证缓存
          if (sessionInstance.clearAuthCache) {
            await sessionInstance.clearAuthCache();
          }

          // 【关键】清理主机解析缓存
          if (sessionInstance.clearHostResolverCache) {
            await sessionInstance.clearHostResolverCache();
          }

          console.log(`✅ session分区 ${partition} 完全重置完成`);
        } catch (err) {
          console.error(`❌ 重置session分区 ${partition} 失败:`, err);
        }
      }

      // 6. 从Cookie管理器中移除这些session分区，强制重新创建
      for (const { partition } of weixinPartitions) {
        this.sessionInstances.delete(partition);
        console.log(`🗑️ 已从Cookie管理器中移除session分区: ${partition}`);
      }

      // 7. 清理session分区的物理文件
      try {
        const { app } = require('electron');
        const userDataPath = app.getPath('userData');
        const partitionsPath = path.join(userDataPath, 'Partitions');

        if (fs.existsSync(partitionsPath)) {
          const partitionDirs = fs.readdirSync(partitionsPath);
          for (const dir of partitionDirs) {
            if (dir.includes('shop_') || dir.includes('weixin')) {
              const partitionPath = path.join(partitionsPath, dir);
              try {
                fs.rmSync(partitionPath, { recursive: true, force: true });
                console.log(`🗑️ 已删除session分区目录: ${dir}`);
              } catch (err) {
                console.log(`⚠️ 删除session分区目录失败: ${dir}`, err.message);
              }
            }
          }
        }
      } catch (err) {
        console.log('⚠️ 清理session分区物理文件失败:', err.message);
      }

      console.log('✅ 微信小店会话状态完全清理完成');
    } catch (error) {
      console.error('❌ 清理微信小店会话状态失败:', error);
    }
  }

  /**
   * 重置微信小店登录状态
   * 在检测到重定向错误时调用
   */
  async resetWeixinStoreLoginState() {
    console.log('🔄 重置微信小店登录状态...');

    try {
      // 1. 先清理所有微信相关会话
      await this.clearWeixinStoreSession();

      // 2. 等待一段时间确保清理完成
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 3. 重新创建干净的微信小店session分区
      await this.recreateWeixinStoreSessionPartitions();

      // 4. 重新初始化微信相关的session配置
      const weixinDomains = [
        'store.weixin.qq.com',
        'filehelper.weixin.qq.com',
        'channels.weixin.qq.com'
      ];

      // 为所有session重新设置微信域名的拦截
      const allSessions = [this.sessionInstance, ...this.sessionInstances.values()];
      for (const sessionInstance of allSessions) {
        if (sessionInstance) {
          // 重新设置Cookie拦截
          this.setupWeixinCookieInterception(sessionInstance);
        }
      }

      console.log('✅ 微信小店登录状态重置完成');
    } catch (error) {
      console.error('❌ 重置微信小店登录状态失败:', error);
    }
  }

  /**
   * 【优化】应用启动时的微信小店状态初始化
   * 智能检测并只清理冲突状态，保留有效登录状态
   */
  async initializeWeixinStoreOnStartup() {
    console.log('🔄 应用启动时初始化微信小店状态...');

    try {
      const Store = require('electron-store');
      const store = new Store({
        name: 'xiaomeihua-config',
        encryptionKey: 'xiaomeihua-secure-key-2025',
        clearInvalidConfig: true,
        serialize: (value) => JSON.stringify(value, null, 2),
        deserialize: (value) => {
          try {
            return JSON.parse(value);
          } catch (error) {
            console.error('反序列化配置失败:', error);
            return {};
          }
        }
      });

      // 检查是否是应用重启后的首次初始化
      const lastShutdownTime = store.get('last_shutdown_time');
      const currentTime = Date.now();

      // 检查是否有异常退出标记
      const hasRedirectError = store.get('weixin_redirect_error');
      const hasLoginError = store.get('weixin_login_error');
      const forceLogout = store.get('logout_detected');

      console.log('🔍 检查退出登录标记:', {
        hasRedirectError,
        hasLoginError,
        forceLogout,
        lastShutdownTime,
        currentTime
      });

      // 如果上次关闭时间在30分钟内，认为是重启
      const isRestart = lastShutdownTime && (currentTime - lastShutdownTime) < 1800000;

      // 判断是否需要清理状态
      let needsClearState = false;
      let clearReason = '';

      if (forceLogout) {
        needsClearState = true;
        clearReason = '检测到用户主动退出登录';
      } else if (hasRedirectError) {
        needsClearState = true;
        clearReason = '检测到重定向错误';
      } else if (hasLoginError) {
        needsClearState = true;
        clearReason = '检测到登录错误';
      } else if (isRestart) {
        // 【关键修复】重启时更保守的登录状态检查
        console.log('🔍 检测到应用重启，检查登录状态有效性...');

        const hasValidLoginState = await this.checkValidWeixinLoginState();
        const loginStateAge = this.getLoginStateAge();

        // 【修复】如果登录状态超过6小时或无效，清理状态确保可以重新登录
        if (!hasValidLoginState || loginStateAge > 21600000) { // 6小时
          needsClearState = true;
          clearReason = `检测到重启且登录状态无效或过期 (年龄: ${Math.round(loginStateAge/1000/60)}分钟)`;
        } else {
          console.log(`✅ 检测到重启但有有效登录状态，保留登录状态 (年龄: ${Math.round(loginStateAge/1000/60)}分钟)`);
        }
      }

      if (needsClearState) {
        console.log(`🔄 ${clearReason}，执行相应的状态清理...`);

        if (forceLogout) {
          // 【关键修复】用户主动退出登录：完全清理登录状态，确保可以重新登录
          console.log('🔄 用户主动退出，执行完全清理...');

          // 完全重置微信小店登录状态（包括保存的状态文件）
          await this.resetWeixinStoreLoginState();

          // 清理当前session状态
          await this.clearWeixinStoreSession();

          console.log('✅ 用户退出登录状态完全清理完成');
        } else {
          // 错误状态：只清理冲突状态，保留登录能力
          console.log('🔄 错误状态清理，保留登录能力...');
          await this.clearWeixinConflictState();
          console.log('✅ 微信小店冲突状态清理完成');
        }

        // 清理错误标记
        store.delete('weixin_redirect_error');
        store.delete('weixin_login_error');

        // 【关键修复】只有在处理完退出登录后才清除退出登录标记
        if (forceLogout) {
          store.delete('logout_detected');
          store.delete('logout_timestamp');
          store.delete('manual_logout');
        }

      } else {
        console.log('📋 无需清理微信小店状态，保持现有状态');

        // 【关键修复】只有在不需要清理状态时才尝试恢复登录状态
        try {
          console.log('🔄 尝试恢复微信小店登录状态...');
          await this.restoreWeixinLoginState();
          console.log('✅ 微信小店登录状态恢复完成');
        } catch (err) {
          console.error('❌ 微信小店登录状态恢复失败:', err);
        }
      }

      // 确保微信域名拦截正常工作
      const allSessions = [this.sessionInstance, ...this.sessionInstances.values()];
      for (const sessionInstance of allSessions) {
        if (sessionInstance) {
          this.setupWeixinCookieInterception(sessionInstance);
        }
      }

      // 记录当前启动时间
      store.set('last_startup_time', currentTime);

    } catch (error) {
      console.error('❌ 应用启动时初始化微信小店状态失败:', error);
    }
  }

  /**
   * 【新增】检查是否有有效的微信小店登录状态
   */
  async checkValidWeixinLoginState() {
    console.log('🔍 检查微信小店登录状态...');

    try {
      // 检查所有session中是否有微信小店的登录相关Cookie
      const loginCookieNames = [
        'ticket',
        'token',
        'session_key',
        'openid',
        'unionid',
        'wxuin',
        'mm_lang',
        'pgv_pvi',
        'pgv_si'
      ];

      let hasValidLogin = false;

      // 检查默认session
      if (this.sessionInstance) {
        const cookies = await this.sessionInstance.cookies.get({ domain: 'store.weixin.qq.com' });
        for (const cookie of cookies) {
          if (loginCookieNames.some(name => cookie.name.toLowerCase().includes(name.toLowerCase()))) {
            console.log(`✅ 在默认session中找到登录Cookie: ${cookie.name}`);
            hasValidLogin = true;
            break;
          }
        }
      }

      // 检查店铺session分区
      if (!hasValidLogin) {
        for (const [partition, sessionInstance] of this.sessionInstances) {
          const cookies = await sessionInstance.cookies.get({ domain: 'store.weixin.qq.com' });
          for (const cookie of cookies) {
            if (loginCookieNames.some(name => cookie.name.toLowerCase().includes(name.toLowerCase()))) {
              console.log(`✅ 在session分区 ${partition} 中找到登录Cookie: ${cookie.name}`);
              hasValidLogin = true;
              break;
            }
          }
          if (hasValidLogin) break;
        }
      }

      console.log(`🔍 微信小店登录状态检查结果: ${hasValidLogin ? '有效' : '无效'}`);
      return hasValidLogin;
    } catch (error) {
      console.error('❌ 检查微信小店登录状态失败:', error);
      return false;
    }
  }

  /**
   * 【新增】只清理微信小店的冲突状态，保留有效登录状态
   */
  async clearWeixinConflictState() {
    console.log('🔄 清理微信小店冲突状态（保留登录状态）...');

    try {
      // 只清理可能导致冲突的特定Cookie，保留登录相关Cookie
      const conflictCookieNames = [
        'redirect_count',
        'error_count',
        'temp_session',
        'csrf_token',
        'verification_token'
      ];

      const weixinDomains = [
        'store.weixin.qq.com',
        'filehelper.weixin.qq.com',
        'channels.weixin.qq.com'
      ];

      // 清理默认session中的冲突Cookie
      if (this.sessionInstance) {
        for (const domain of weixinDomains) {
          const cookies = await this.sessionInstance.cookies.get({ domain: domain });
          for (const cookie of cookies) {
            if (conflictCookieNames.some(name => cookie.name.toLowerCase().includes(name.toLowerCase()))) {
              const url = cookie.secure ? `https://${cookie.domain}` : `http://${cookie.domain}`;
              await this.sessionInstance.cookies.remove(url, cookie.name);
              console.log(`🗑️ 清理冲突Cookie: ${cookie.name} (默认session)`);
            }
          }
        }

        // 清理可能导致冲突的存储数据，但保留登录相关数据
        try {
          await this.sessionInstance.clearStorageData({
            storages: ['websql', 'indexdb'] // 只清理这些，保留localstorage和sessionstorage中的登录信息
          });
          console.log('✅ 已清理默认session的冲突存储数据');
        } catch (err) {
          console.log('⚠️ 清理默认session冲突存储数据失败:', err.message);
        }
      }

      // 清理店铺session分区中的冲突Cookie
      for (const [partition, sessionInstance] of this.sessionInstances) {
        try {
          for (const domain of weixinDomains) {
            const cookies = await sessionInstance.cookies.get({ domain: domain });
            for (const cookie of cookies) {
              if (conflictCookieNames.some(name => cookie.name.toLowerCase().includes(name.toLowerCase()))) {
                const url = cookie.secure ? `https://${cookie.domain}` : `http://${cookie.domain}`;
                await sessionInstance.cookies.remove(url, cookie.name);
                console.log(`🗑️ 清理冲突Cookie: ${cookie.name} (${partition})`);
              }
            }
          }

          // 清理该session的冲突存储数据
          await sessionInstance.clearStorageData({
            storages: ['websql', 'indexdb']
          });

          console.log(`✅ 已清理session分区 ${partition} 的冲突数据`);
        } catch (err) {
          console.log(`⚠️ 清理session分区 ${partition} 冲突数据失败:`, err.message);
        }
      }

      console.log('✅ 微信小店冲突状态清理完成（登录状态已保留）');
    } catch (error) {
      console.error('❌ 清理微信小店冲突状态失败:', error);
    }
  }

  /**
   * 【新增】智能恢复微信小店登录状态
   */
  async restoreWeixinLoginState() {
    console.log('🔄 智能恢复微信小店登录状态...');

    try {
      // 【关键修复】检查状态一致性，确保不会出现冲突
      const Store = require('electron-store');
      const store = new Store({
        name: 'xiaomeihua-config',
        encryptionKey: 'xiaomeihua-secure-key-2025',
        clearInvalidConfig: true,
        serialize: (value) => JSON.stringify(value, null, 2),
        deserialize: (value) => {
          try {
            return JSON.parse(value);
          } catch (error) {
            console.error('反序列化配置失败:', error);
            return {};
          }
        }
      });

      // 检查是否有退出登录标记
      const logoutDetected = store.get('logout_detected');
      const manualLogout = store.get('manual_logout');

      if (logoutDetected || manualLogout) {
        console.log('⚠️ 检测到退出登录标记，不恢复登录状态');
        return false;
      }

      // 检查是否有保存的微信小店登录状态
      const hasValidLogin = await this.checkValidWeixinLoginState();

      if (hasValidLogin) {
        console.log('✅ 检测到有效的微信小店登录状态，无需恢复');
        return true;
      }

      // 尝试从备份文件恢复微信小店相关的Cookie
      // Store已在方法开始时声明，直接使用
      const path = require('path');
      const fs = require('fs');

      if (!COOKIES_DIR || !fs.existsSync(COOKIES_DIR)) {
        console.log('📋 未找到Cookie备份目录');
        return false;
      }

      // 查找微信小店相关的Cookie备份
      const backupFiles = fs.readdirSync(COOKIES_DIR);
      const weixinBackupFile = backupFiles.find(file =>
        file.includes('weixin') || file.includes('store') || file.includes('wx')
      );

      if (weixinBackupFile) {
        console.log(`🔄 找到微信小店Cookie备份: ${weixinBackupFile}`);

        try {
          const backupPath = path.join(COOKIES_DIR, weixinBackupFile);
          const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf8'));

          // 检查备份数据格式
          let cookieArray = [];
          if (Array.isArray(backupData)) {
            cookieArray = backupData;
          } else if (backupData.cookies && Array.isArray(backupData.cookies)) {
            cookieArray = backupData.cookies;
          } else if (typeof backupData === 'object') {
            // 如果是对象格式，尝试提取Cookie数组
            const possibleArrays = Object.values(backupData).filter(Array.isArray);
            if (possibleArrays.length > 0) {
              cookieArray = possibleArrays[0];
            }
          }

          if (cookieArray.length === 0) {
            console.log('📋 备份文件中未找到Cookie数据');
            return false;
          }

          // 恢复微信小店相关的Cookie
          let restoredCount = 0;
          for (const cookieData of cookieArray) {
            if (cookieData && cookieData.domain && cookieData.domain.includes('weixin')) {
              try {
                // 选择合适的session来恢复Cookie
                const sessionInstance = this.getSessionForDomain(cookieData.domain);
                if (sessionInstance) {
                  await sessionInstance.cookies.set(cookieData);
                  restoredCount++;
                }
              } catch (setCookieErr) {
                console.log(`⚠️ 设置Cookie失败: ${cookieData.name}`, setCookieErr.message);
              }
            }
          }

          console.log(`✅ 成功恢复 ${restoredCount} 个微信小店Cookie`);
          return restoredCount > 0;
        } catch (err) {
          console.error('❌ 恢复微信小店Cookie失败:', err);
        }
      }

      console.log('📋 未找到可恢复的微信小店登录状态');
      return false;
    } catch (error) {
      console.error('❌ 智能恢复微信小店登录状态失败:', error);
      return false;
    }
  }

  /**
   * 【新增】根据域名获取合适的session
   */
  getSessionForDomain(domain) {
    // 如果是微信小店域名，优先使用店铺专用session
    if (domain.includes('store.weixin.qq.com')) {
      // 查找店铺相关的session分区
      for (const [partition, sessionInstance] of this.sessionInstances) {
        if (partition.includes('shop_')) {
          return sessionInstance;
        }
      }
    }

    // 默认返回默认session
    return this.sessionInstance;
  }

  /**
   * 【新增】保存微信小店登录状态
   */
  async saveWeixinLoginState() {
    console.log('💾 保存微信小店登录状态...');

    try {
      const path = require('path');
      const fs = require('fs');

      if (!COOKIES_DIR) {
        console.log('⚠️ Cookie存储目录未初始化');
        return;
      }

      // 收集所有微信小店相关的Cookie
      const weixinCookies = [];
      const weixinDomains = ['store.weixin.qq.com', 'weixin.qq.com', 'wx.qq.com'];

      // 从默认session收集
      if (this.sessionInstance) {
        for (const domain of weixinDomains) {
          const cookies = await this.sessionInstance.cookies.get({ domain: domain });
          weixinCookies.push(...cookies);
        }
      }

      // 从店铺session分区收集
      for (const [partition, sessionInstance] of this.sessionInstances) {
        for (const domain of weixinDomains) {
          const cookies = await sessionInstance.cookies.get({ domain: domain });
          weixinCookies.push(...cookies);
        }
      }

      if (weixinCookies.length > 0) {
        const backupPath = path.join(COOKIES_DIR, 'weixin-login-backup.json');
        fs.writeFileSync(backupPath, JSON.stringify(weixinCookies, null, 2));
        console.log(`✅ 已保存 ${weixinCookies.length} 个微信小店Cookie到备份文件`);
      } else {
        console.log('📋 未找到微信小店Cookie，跳过保存');
      }
    } catch (error) {
      console.error('❌ 保存微信小店登录状态失败:', error);
    }
  }

  /**
   * 【新增】重新创建微信小店session分区
   */
  async recreateWeixinStoreSessionPartitions() {
    console.log('🔄 重新创建微信小店session分区...');

    try {
      const Store = require('electron-store');
      const store = new Store({
        name: 'xiaomeihua-config',
        encryptionKey: 'xiaomeihua-secure-key-2025',
        clearInvalidConfig: true,
        serialize: (value) => JSON.stringify(value, null, 2),
        deserialize: (value) => {
          try {
            return JSON.parse(value);
          } catch (error) {
            console.error('反序列化配置失败:', error);
            return {};
          }
        }
      });

      // 获取店铺信息
      const shopInfo = store.get('shop_info');
      if (shopInfo && shopInfo.shops && Array.isArray(shopInfo.shops)) {
        console.log(`🏪 重新创建 ${shopInfo.shops.length} 个店铺的session分区...`);

        for (const shop of shopInfo.shops) {
          const shopId = shop.shop_id || shop.id;
          if (shopId) {
            const partition = `persist:shop_${shopId}`;

            // 检查是否已经存在
            if (!this.sessionInstances.has(partition)) {
              console.log(`🔧 重新创建店铺session分区: ${partition} (${shop.shop_name})`);

              // 创建新的session分区
              const { session } = require('electron');
              const sessionInstance = session.fromPartition(partition);

              // 设置Cookie拦截
              this.setupCookieInterception(sessionInstance, partition);
              this.setupWeixinCookieInterception(sessionInstance);

              // 添加到管理器
              this.sessionInstances.set(partition, sessionInstance);

              console.log(`✅ 店铺session分区 ${partition} 重新创建完成`);
            }
          }
        }
      }

      console.log('✅ 微信小店session分区重新创建完成');
    } catch (error) {
      console.error('❌ 重新创建微信小店session分区失败:', error);
    }
  }

  /**
   * 【关键修复】完全重置微信小店登录状态
   * 解决第二次打开软件退出登录后无法重新登录的问题
   */
  async resetWeixinStoreLoginState() {
    console.log('🔄 完全重置微信小店登录状态...');

    try {
      const Store = require('electron-store');
      const store = new Store({
        name: 'xiaomeihua-config',
        encryptionKey: 'xiaomeihua-secure-key-2025',
        clearInvalidConfig: true,
        serialize: (value) => JSON.stringify(value, null, 2),
        deserialize: (value) => {
          try {
            return JSON.parse(value);
          } catch (error) {
            console.error('反序列化配置失败:', error);
            return {};
          }
        }
      });

      // 1. 清理所有微信小店相关的存储项
      const allKeys = Object.keys(store.store);
      const weixinKeys = allKeys.filter(key =>
        key.includes('weixin') ||
        key.includes('shop') ||
        key.includes('store') ||
        key.includes('login_state') ||
        key.includes('session_state') ||
        key.includes('auth_state')
      );

      console.log(`🗑️ 清理 ${weixinKeys.length} 个微信小店相关存储项...`);
      weixinKeys.forEach(key => {
        store.delete(key);
        console.log(`🗑️ 已清除存储项: ${key}`);
      });

      // 2. 清理所有保存的登录状态文件
      const fs = require('fs');
      const path = require('path');

      if (COOKIES_DIR && fs.existsSync(COOKIES_DIR)) {
        const stateFiles = [
          'login-state.json',
          'session-state.json',
          'auth-state.json',
          'user-state.json',
          'shop-state.json',
          'weixin-login-state.json',
          'weixin-session-state.json'
        ];

        stateFiles.forEach(fileName => {
          const filePath = path.join(COOKIES_DIR, fileName);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`🗑️ 已删除状态文件: ${fileName}`);
          }
        });
      }

      // 3. 清理所有微信小店相关的Cookie备份文件
      if (COOKIES_DIR && fs.existsSync(COOKIES_DIR)) {
        const files = fs.readdirSync(COOKIES_DIR);
        const weixinFiles = files.filter(file =>
          file.includes('weixin') ||
          file.includes('shop') ||
          file.includes('store')
        );

        weixinFiles.forEach(fileName => {
          const filePath = path.join(COOKIES_DIR, fileName);
          try {
            fs.unlinkSync(filePath);
            console.log(`🗑️ 已删除微信小店Cookie文件: ${fileName}`);
          } catch (err) {
            console.log(`⚠️ 删除文件失败: ${fileName}`, err.message);
          }
        });
      }

      // 4. 重置内存中的状态
      this.loginStateRestored = false;
      this.lastLoginCheck = 0;

      console.log('✅ 微信小店登录状态完全重置完成');
    } catch (error) {
      console.error('❌ 重置微信小店登录状态失败:', error);
    }
  }

  /**
   * 【新增】获取登录状态的年龄（毫秒）
   */
  getLoginStateAge() {
    try {
      if (!COOKIES_DIR) {
        initializeStoragePaths();
      }

      const loginStateFile = path.join(COOKIES_DIR, 'login-state.json');
      if (fs.existsSync(loginStateFile)) {
        const loginState = JSON.parse(fs.readFileSync(loginStateFile, 'utf-8'));
        if (loginState.timestamp) {
          return Date.now() - loginState.timestamp;
        }
      }

      return Infinity; // 如果没有登录状态文件，返回无穷大表示过期
    } catch (error) {
      console.error('获取登录状态年龄失败:', error);
      return Infinity;
    }
  }

  /**
   * 为session设置微信专用的Cookie拦截
   */
  setupWeixinCookieInterception(sessionInstance) {
    try {
      // 拦截微信域名的请求，确保Cookie正确设置
      sessionInstance.webRequest.onBeforeSendHeaders((details, callback) => {
        if (this.isWeixinDomain(details.url)) {
          const requestHeaders = { ...details.requestHeaders };

          // 移除可能导致重定向循环的头部
          delete requestHeaders['If-None-Match'];
          delete requestHeaders['If-Modified-Since'];

          // 确保请求头正确
          requestHeaders['Cache-Control'] = 'no-cache';
          requestHeaders['Pragma'] = 'no-cache';

          callback({ requestHeaders });
        } else {
          callback({});
        }
      });

      // 拦截响应，处理重定向
      sessionInstance.webRequest.onHeadersReceived((details, callback) => {
        if (this.isWeixinDomain(details.url)) {
          const responseHeaders = { ...details.responseHeaders };

          // 检查是否是重定向响应
          if (details.statusCode >= 300 && details.statusCode < 400) {
            console.log(`🔍 检测到微信域名重定向: ${details.url} -> ${details.statusCode}`);

            // 如果是循环重定向，阻止它
            if (responseHeaders.location && responseHeaders.location[0] === details.url) {
              console.log('🚫 阻止循环重定向');
              responseHeaders.location = ['about:blank'];
            }
          }

          callback({ responseHeaders });
        } else {
          callback({});
        }
      });

      console.log('✅ 微信Cookie拦截设置完成');
    } catch (error) {
      console.error('❌ 设置微信Cookie拦截失败:', error);
    }
  }

  /**
   * 检查是否是微信相关域名
   */
  isWeixinDomain(url) {
    if (!url) return false;

    const weixinDomains = [
      'store.weixin.qq.com',
      'filehelper.weixin.qq.com',
      'channels.weixin.qq.com',
      'weixin.qq.com',
      'wx.qq.com',
      'mp.weixin.qq.com'
    ];

    return weixinDomains.some(domain => url.includes(domain));
  }

  /**
   * 【彻底重写】清除所有Cookie和Session数据
   */
  async clearAllCookies() {
    try {
      console.log('🧹 开始彻底清除所有Cookie和Session数据...');

      let totalCleared = 0;

      // 【第一步】清除所有持久化Cookie文件
      if (COOKIES_DIR && fs.existsSync(COOKIES_DIR)) {
        console.log('🗂️ 清除所有持久化Cookie文件...');
        try {
          // 删除整个Cookie目录
          fs.rmSync(COOKIES_DIR, { recursive: true, force: true });
          console.log(`✅ 已删除整个Cookie目录: ${COOKIES_DIR}`);

          // 重新创建空目录
          fs.mkdirSync(COOKIES_DIR, { recursive: true });
          console.log(`✅ 已重新创建空Cookie目录`);
        } catch (e) {
          console.log('删除Cookie目录失败，尝试逐个删除文件:', e);

          // 如果删除目录失败，尝试逐个删除文件
          const files = fs.readdirSync(COOKIES_DIR);
          files.forEach(file => {
            const filePath = path.join(COOKIES_DIR, file);
            try {
              fs.unlinkSync(filePath);
              console.log(`✅ 已删除Cookie文件: ${file}`);
            } catch (e) {
              console.log(`删除Cookie文件失败: ${file}`, e);
            }
          });
        }
      }

      // 【第二步】彻底清除默认session的所有数据
      if (this.sessionInstance) {
        console.log('🧹 彻底清除默认session的所有数据...');
        try {
          // 清除所有Cookie
          const defaultCookies = await this.sessionInstance.cookies.get({});
          for (const cookie of defaultCookies) {
            try {
              const url = cookie.secure
                ? `https://${cookie.domain}${cookie.path}`
                : `http://${cookie.domain}${cookie.path}`;
              await this.sessionInstance.cookies.remove(url, cookie.name);
              totalCleared++;
            } catch (err) {
              console.error(`清除默认session Cookie ${cookie.name} 失败:`, err);
            }
          }

          // 清除所有存储数据
          await this.sessionInstance.clearStorageData({
            storages: ['appcache', 'cookies', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'serviceworkers', 'cachestorage']
          });

          // 清除缓存
          await this.sessionInstance.clearCache();

          console.log(`✅ 彻底清除默认session的 ${defaultCookies.length} 个Cookie和所有存储数据`);
        } catch (error) {
          console.error('彻底清除默认session失败:', error);
        }
      }

      // 【第三步】彻底清除所有session分区的数据
      console.log('🧹 彻底清除所有session分区的数据...');
      for (const [partition, sessionInstance] of this.sessionInstances) {
        try {
          console.log(`彻底清除session分区: ${partition}`);

          // 清除所有Cookie
          const sessionCookies = await sessionInstance.cookies.get({});
          for (const cookie of sessionCookies) {
            try {
              const url = cookie.secure
                ? `https://${cookie.domain}${cookie.path}`
                : `http://${cookie.domain}${cookie.path}`;
              await sessionInstance.cookies.remove(url, cookie.name);
              totalCleared++;
            } catch (err) {
              console.error(`清除session ${partition} Cookie ${cookie.name} 失败:`, err);
            }
          }

          // 清除所有存储数据
          await sessionInstance.clearStorageData({
            storages: ['appcache', 'cookies', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'serviceworkers', 'cachestorage']
          });

          // 清除缓存
          await sessionInstance.clearCache();

          console.log(`✅ 彻底清除session ${partition} 的 ${sessionCookies.length} 个Cookie和所有存储数据`);
        } catch (error) {
          console.error(`彻底清除session ${partition} 失败:`, error);
        }
      }

      // 【第四步】清除session实例映射和监听器
      console.log('🧹 清除session实例映射和监听器...');
      this.sessionInstances.clear();
      this.sessionListeners.clear();
      console.log('✅ 已清除所有session实例映射和监听器');

      // 【第五步】停止定期更新
      if (this.cookieUpdateInterval) {
        clearInterval(this.cookieUpdateInterval);
        this.cookieUpdateInterval = null;
        console.log('✅ 已停止Cookie定期更新');
      }

      // 【第六步】重置初始化状态
      this.initialized = false;
      console.log('✅ 已重置Cookie管理器初始化状态');

      console.log(`🎯 彻底清除完成！总共清除 ${totalCleared} 个Cookie和所有相关数据`);
    } catch (error) {
      console.error('❌ 彻底清除所有Cookie失败:', error);
    }
  }

  /**
   * 【新增】保存完整登录状态
   */
  async saveCompleteLoginState() {
    try {
      console.log('💾 保存完整登录状态...');

      // 确保存储路径已初始化
      if (!COOKIES_DIR) {
        initializeStoragePaths();
      }

      // 保存所有Cookie
      await this.saveCookies();

      // 创建登录状态文件
      const loginStateData = {
        timestamp: Date.now(),
        hasLogin: true,
        url: 'weixin.qq.com',
        sessionCount: this.sessionInstances.size + 1
      };

      const loginStateFile = path.join(COOKIES_DIR, 'login-state.json');
      fs.writeFileSync(loginStateFile, JSON.stringify(loginStateData, null, 2));

      // 创建会话备份文件
      const sessionBackupData = {
        timestamp: Date.now(),
        backupType: 'complete',
        sessionInstances: Array.from(this.sessionInstances.keys())
      };

      const sessionBackupFile = path.join(COOKIES_DIR, 'session-backup.json');
      fs.writeFileSync(sessionBackupFile, JSON.stringify(sessionBackupData, null, 2));

      console.log('✅ 完整登录状态保存完成');
    } catch (error) {
      console.error('❌ 保存完整登录状态失败:', error);
    }
  }

  /**
   * 从数据恢复Cookie到浏览器会话
   */
  async restoreCookiesFromData(cookiesData) {
    if (!Array.isArray(cookiesData) || cookiesData.length === 0) {
      console.log('⚠️ 没有Cookie数据需要恢复');
      return;
    }

    console.log(`🔄 开始恢复 ${cookiesData.length} 个Cookie...`);
    let successCount = 0;

    for (const cookieData of cookiesData) {
      try {
        // 为所有session实例恢复Cookie
        const allSessions = [this.sessionInstance, ...this.sessionInstances.values()];

        for (const sessionInstance of allSessions) {
          if (sessionInstance) {
            await sessionInstance.cookies.set({
              url: cookieData.url || `https://${cookieData.domain}`,
              name: cookieData.name,
              value: cookieData.value,
              domain: cookieData.domain,
              path: cookieData.path || '/',
              secure: cookieData.secure || false,
              httpOnly: cookieData.httpOnly || false,
              expirationDate: cookieData.expirationDate
            });
          }
        }

        successCount++;
      } catch (error) {
        console.warn(`⚠️ Cookie恢复失败: ${cookieData.name}`, error.message);
      }
    }

    console.log(`✅ Cookie恢复完成: ${successCount}/${cookiesData.length} 个成功`);
  }

  /**
   * 获取所有Cookie数据
   */
  async getAllCookies() {
    const allCookies = [];

    try {
      // 从默认session获取Cookie
      if (this.sessionInstance) {
        const cookies = await this.sessionInstance.cookies.get({});
        allCookies.push(...cookies);
      }

      // 从其他session实例获取Cookie
      for (const sessionInstance of this.sessionInstances.values()) {
        if (sessionInstance) {
          const cookies = await sessionInstance.cookies.get({});
          allCookies.push(...cookies);
        }
      }

      console.log(`📊 获取到 ${allCookies.length} 个Cookie`);
      return allCookies;
    } catch (error) {
      console.error('❌ 获取Cookie失败:', error);
      return [];
    }
  }
}

// 导出单例
const cookieManager = new CookieManager();
module.exports = cookieManager;
