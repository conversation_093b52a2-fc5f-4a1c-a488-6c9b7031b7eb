const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('开始修复exe文件图标...');

const distPath = path.join(__dirname, '..', 'dist');
const winUnpackedPath = path.join(distPath, 'win-unpacked');
const exePath = path.join(winUnpackedPath, '小梅花AI智能客服.exe');
const iconPath = path.join(__dirname, 'icon.ico');

// 检查文件是否存在
if (!fs.existsSync(exePath)) {
    console.error('错误：找不到exe文件:', exePath);
    process.exit(1);
}

if (!fs.existsSync(iconPath)) {
    console.error('错误：找不到图标文件:', iconPath);
    process.exit(1);
}

console.log('exe文件路径:', exePath);
console.log('图标文件路径:', iconPath);

// 检查是否安装了rcedit
try {
    execSync('rcedit --version', { stdio: 'ignore' });
    console.log('检测到rcedit工具，开始修复图标...');
    
    // 使用rcedit修复图标
    const command = `rcedit "${exePath}" --set-icon "${iconPath}"`;
    console.log('执行命令:', command);
    
    execSync(command, { stdio: 'inherit' });
    console.log('图标修复成功！');
    
} catch (error) {
    console.log('rcedit工具未安装，尝试安装...');
    
    try {
        console.log('正在安装rcedit...');
        execSync('npm install -g rcedit', { stdio: 'inherit' });
        
        console.log('rcedit安装成功，开始修复图标...');
        const command = `rcedit "${exePath}" --set-icon "${iconPath}"`;
        console.log('执行命令:', command);
        
        execSync(command, { stdio: 'inherit' });
        console.log('图标修复成功！');
        
    } catch (installError) {
        console.error('无法安装rcedit工具，请手动安装：');
        console.error('npm install -g rcedit');
        console.error('然后运行：');
        console.error(`rcedit "${exePath}" --set-icon "${iconPath}"`);
        
        // 至少复制图标文件到目录中
        const targetIconPath = path.join(winUnpackedPath, 'icon.ico');
        fs.copyFileSync(iconPath, targetIconPath);
        console.log('图标文件已复制到:', targetIconPath);
    }
}

console.log('图标修复脚本执行完成！');
