; 小梅花AI智能客服简化安装脚本
; 设置权限、防火墙规则和图标

; 安装完成后执行
Function .onInstSuccess
  ; 设置安装目录权限
  nsExec::ExecToLog 'icacls "$INSTDIR" /grant "Everyone:(OI)(CI)F" /T'

  ; 创建并设置用户数据目录权限
  CreateDirectory "$APPDATA\小梅花AI智能客服"
  nsExec::ExecToLog 'icacls "$APPDATA\小梅花AI智能客服" /grant "Everyone:(OI)(CI)F" /T'

  CreateDirectory "$LOCALAPPDATA\小梅花AI智能客服"
  nsExec::ExecToLog 'icacls "$LOCALAPPDATA\小梅花AI智能客服" /grant "Everyone:(OI)(CI)F" /T'

  CreateDirectory "$PROGRAMDATA\小梅花AI智能客服"
  nsExec::ExecToLog 'icacls "$PROGRAMDATA\小梅花AI智能客服" /grant "Everyone:(OI)(CI)F" /T'

  ; 添加防火墙例外
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="小梅花AI智能客服" dir=in action=allow program="$INSTDIR\小梅花AI智能客服.exe"'
  nsExec::ExecToLog 'netsh advfirewall firewall add rule name="小梅花AI智能客服" dir=out action=allow program="$INSTDIR\小梅花AI智能客服.exe"'

  ; 创建桌面快捷方式（带正确图标）
  CreateShortCut "$DESKTOP\小梅花AI智能客服.lnk" "$INSTDIR\小梅花AI智能客服.exe" "" "$INSTDIR\小梅花AI智能客服.exe" 0

  ; 创建开始菜单快捷方式（带正确图标）
  CreateDirectory "$SMPROGRAMS\小梅花AI智能客服"
  CreateShortCut "$SMPROGRAMS\小梅花AI智能客服\小梅花AI智能客服.lnk" "$INSTDIR\小梅花AI智能客服.exe" "" "$INSTDIR\小梅花AI智能客服.exe" 0
  CreateShortCut "$SMPROGRAMS\小梅花AI智能客服\卸载小梅花AI智能客服.lnk" "$INSTDIR\uninst.exe" "" "$INSTDIR\uninst.exe" 0

  ; 刷新图标缓存
  nsExec::ExecToLog 'ie4uinit.exe -ClearIconCache'
  nsExec::ExecToLog 'ie4uinit.exe -show'
FunctionEnd

; 卸载时清理
Function un.onUninstSuccess
  ; 清理防火墙规则
  nsExec::ExecToLog 'netsh advfirewall firewall delete rule name="小梅花AI智能客服"'

  ; 删除快捷方式
  Delete "$DESKTOP\小梅花AI智能客服.lnk"
  Delete "$SMPROGRAMS\小梅花AI智能客服\小梅花AI智能客服.lnk"
  Delete "$SMPROGRAMS\小梅花AI智能客服\卸载小梅花AI智能客服.lnk"
  RMDir "$SMPROGRAMS\小梅花AI智能客服"

  ; 刷新图标缓存
  nsExec::ExecToLog 'ie4uinit.exe -ClearIconCache'
FunctionEnd
