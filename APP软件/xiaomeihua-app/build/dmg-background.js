/**
 * DMG背景图片生成脚本
 * 创建自定义的DMG安装界面背景
 */

const fs = require('fs');
const path = require('path');

// 创建简单的SVG背景图
function createDMGBackground() {
    const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="540" height="380" viewBox="0 0 540 380" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b9d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8fab;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="540" height="380" fill="url(#backgroundGradient)"/>
  
  <!-- 顶部装饰条 -->
  <rect x="0" y="0" width="540" height="4" fill="url(#accentGradient)"/>
  
  <!-- 标题区域 -->
  <rect x="20" y="20" width="500" height="80" rx="10" fill="white" stroke="#dee2e6" stroke-width="1"/>
  
  <!-- 应用图标位置指示 -->
  <circle cx="130" cy="220" r="50" fill="none" stroke="#6c757d" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
  <text x="130" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">拖拽应用</text>
  
  <!-- Applications文件夹指示 -->
  <rect x="360" y="170" width="100" height="100" rx="10" fill="none" stroke="#6c757d" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
  <text x="410" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6c757d">Applications</text>
  
  <!-- 箭头指示 -->
  <path d="M 180 220 Q 285 200 360 220" stroke="#ff6b9d" stroke-width="3" fill="none" marker-end="url(#arrowhead)" opacity="0.7"/>
  
  <!-- 箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ff6b9d"/>
    </marker>
  </defs>
  
  <!-- 标题文字 -->
  <text x="270" y="45" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#495057">小梅花AI智能客服</text>
  <text x="270" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6c757d">将应用拖拽到Applications文件夹完成安装</text>
  
  <!-- 底部信息 -->
  <text x="270" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#adb5bd">© 2025 小梅花AI科技 - 智能客服解决方案</text>
</svg>`;

    return svg;
}

// 创建DMG配置文件
function createDMGConfig() {
    const config = {
        title: "小梅花AI智能客服安装",
        icon: "build/icon.icns",
        iconSize: 100,
        background: "build/dmg-background.png",
        contents: [
            {
                x: 130,
                y: 220,
                type: "file"
            },
            {
                x: 410,
                y: 220,
                type: "link",
                path: "/Applications"
            }
        ],
        window: {
            width: 540,
            height: 380,
            x: 200,
            y: 120
        },
        format: "UDZO", // 压缩格式
        backgroundColor: "#ffffff"
    };
    
    return config;
}

// 生成安装说明HTML
function createInstallInstructions() {
    const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI智能客服 - 安装说明</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            color: #ff6b9d;
            margin: 0;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #ff6b9d;
        }
        .step-number {
            background: #ff6b9d;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        .feature {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .feature::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning::before {
            content: "⚠️ ";
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>小梅花AI智能客服</h1>
        <p>macOS安装指南</p>
    </div>

    <div class="section">
        <h2>📋 系统要求</h2>
        <div class="feature">macOS 10.15 (Catalina) 或更高版本</div>
        <div class="feature">Intel 或 Apple Silicon (M1/M2/M3) 处理器</div>
        <div class="feature">至少 200MB 可用磁盘空间</div>
        <div class="feature">网络连接（用于API通信和更新）</div>
    </div>

    <div class="section">
        <h2>🚀 安装步骤</h2>
        <div class="step">
            <div class="step-number">1</div>
            <div>双击打开下载的DMG文件</div>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <div>将应用程序图标拖拽到Applications文件夹</div>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <div>在Launchpad或Applications文件夹中找到并启动应用</div>
        </div>
        <div class="step">
            <div class="step-number">4</div>
            <div>首次运行时按照提示完成初始设置</div>
        </div>
    </div>

    <div class="section">
        <h2>🔒 安全设置</h2>
        <div class="warning">
            首次运行时，macOS可能显示安全警告。这是正常现象，请按以下步骤操作：
        </div>
        <div class="step">
            <div class="step-number">1</div>
            <div>打开"系统偏好设置" → "安全性与隐私"</div>
        </div>
        <div class="step">
            <div class="step-number">2</div>
            <div>在"通用"标签页中点击"仍要打开"</div>
        </div>
        <div class="step">
            <div class="step-number">3</div>
            <div>或在终端中运行：<code>sudo xattr -rd com.apple.quarantine /Applications/小梅花AI智能客服.app</code></div>
        </div>
    </div>

    <div class="section">
        <h2>✨ 主要功能</h2>
        <div class="feature">AI智能客服系统</div>
        <div class="feature">多店铺管理</div>
        <div class="feature">自动化脚本执行</div>
        <div class="feature">数据同步和备份</div>
        <div class="feature">自动更新功能</div>
        <div class="feature">原生macOS体验</div>
    </div>

    <div class="section">
        <h2>🔧 故障排除</h2>
        <h3>应用无法启动</h3>
        <ul>
            <li>确保系统版本符合要求</li>
            <li>检查是否有足够的磁盘空间</li>
            <li>尝试重新安装应用</li>
            <li>检查安全设置是否正确</li>
        </ul>
        
        <h3>权限问题</h3>
        <ul>
            <li>在"系统偏好设置" → "安全性与隐私" → "隐私"中授予必要权限</li>
            <li>网络访问权限用于API通信</li>
            <li>文件访问权限用于保存配置</li>
        </ul>
    </div>

    <div class="section">
        <h2>📞 技术支持</h2>
        <p>如遇问题，请联系技术支持团队获取帮助。</p>
        <p><strong>版本：</strong>1.0.0</p>
        <p><strong>构建时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
    </div>
</body>
</html>`;

    return html;
}

// 主函数
function generateDMGAssets() {
    const buildDir = path.join(__dirname);
    
    // 创建SVG背景
    const svgContent = createDMGBackground();
    const svgPath = path.join(buildDir, 'dmg-background.svg');
    fs.writeFileSync(svgPath, svgContent, 'utf8');
    console.log('✅ 已生成DMG背景SVG:', svgPath);
    
    // 创建DMG配置
    const dmgConfig = createDMGConfig();
    const configPath = path.join(buildDir, 'dmg-config.json');
    fs.writeFileSync(configPath, JSON.stringify(dmgConfig, null, 2), 'utf8');
    console.log('✅ 已生成DMG配置文件:', configPath);
    
    // 创建安装说明
    const installHtml = createInstallInstructions();
    const htmlPath = path.join(buildDir, 'install-guide.html');
    fs.writeFileSync(htmlPath, installHtml, 'utf8');
    console.log('✅ 已生成安装说明HTML:', htmlPath);
    
    console.log('\n📝 注意事项:');
    console.log('1. SVG文件需要转换为PNG格式才能在DMG中使用');
    console.log('2. 可以使用在线工具或设计软件转换SVG到PNG');
    console.log('3. 推荐PNG尺寸: 540x380像素');
    console.log('4. 将转换后的PNG文件重命名为 dmg-background.png');
}

// 如果直接运行此脚本
if (require.main === module) {
    generateDMGAssets();
}

module.exports = {
    createDMGBackground,
    createDMGConfig,
    createInstallInstructions,
    generateDMGAssets
};
