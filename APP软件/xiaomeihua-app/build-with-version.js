#!/usr/bin/env node

/**
 * 带版本检查的构建脚本
 * 确保所有版本号一致后再进行打包
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 === 小梅花AI智能客服 - 版本一致性构建脚本 ===\n');

// 从版本配置文件读取版本号
function getVersionFromConfig() {
  try {
    const versionConfigPath = path.join(__dirname, 'version-config.js');
    if (fs.existsSync(versionConfigPath)) {
      const versionConfig = require('./version-config.js');
      return versionConfig.VERSION;
    }
  } catch (error) {
    console.error('❌ 读取版本配置文件失败:', error.message);
  }
  return null;
}

// 从package.json读取版本号
function getVersionFromPackageJson() {
  try {
    const packageJsonPath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return packageJson.version;
  } catch (error) {
    console.error('❌ 读取package.json失败:', error.message);
    return null;
  }
}

// 检查所有文件中的版本号是否一致
function checkVersionConsistency() {
  console.log('🔍 检查版本号一致性...');
  
  const configVersion = getVersionFromConfig();
  const packageVersion = getVersionFromPackageJson();
  
  if (!configVersion || !packageVersion) {
    console.error('❌ 无法读取版本号');
    return false;
  }
  
  console.log(`📋 版本配置文件版本: ${configVersion}`);
  console.log(`📋 package.json版本: ${packageVersion}`);
  
  if (configVersion !== packageVersion) {
    console.error('❌ 版本号不一致！');
    console.log('💡 请运行以下命令统一版本号:');
    console.log(`   node update-version.js ${configVersion}`);
    return false;
  }
  
  // 检查其他文件中的版本号
  const filesToCheck = [
    {
      file: 'src/renderer/main.html',
      pattern: /v\d+\.\d+\.\d+/g,
      expectedVersion: `v${configVersion}`
    },
    {
      file: 'src/renderer/login.html',
      pattern: /版本: v\d+\.\d+\.\d+/,
      expectedVersion: `版本: v${configVersion}`
    },
    {
      file: 'rebuild-all.js',
      pattern: /version: '\d+\.\d+\.\d+'/,
      expectedVersion: `version: '${configVersion}'`
    }
  ];
  
  let allConsistent = true;
  
  for (const fileInfo of filesToCheck) {
    const filePath = path.join(__dirname, fileInfo.file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const matches = content.match(fileInfo.pattern);
      
      if (matches) {
        const foundVersions = [...new Set(matches)];
        const isConsistent = foundVersions.length === 1 && foundVersions[0] === fileInfo.expectedVersion;
        
        if (isConsistent) {
          console.log(`✅ ${fileInfo.file}: 版本号一致`);
        } else {
          console.error(`❌ ${fileInfo.file}: 版本号不一致`);
          console.log(`   期望: ${fileInfo.expectedVersion}`);
          console.log(`   实际: ${foundVersions.join(', ')}`);
          allConsistent = false;
        }
      } else {
        console.log(`⚠️  ${fileInfo.file}: 未找到版本号`);
      }
    } else {
      console.log(`⚠️  文件不存在: ${fileInfo.file}`);
    }
  }
  
  return allConsistent;
}

// 执行构建
function runBuild() {
  console.log('\n🔨 开始构建...');
  
  try {
    // 清理旧的构建文件
    console.log('🧹 清理旧的构建文件...');
    execSync('npm run clean', { stdio: 'inherit' });
    
    // 构建macOS版本
    console.log('🍎 构建macOS版本...');
    execSync('npm run build:mac', { stdio: 'inherit' });
    
    // 构建Windows版本
    console.log('🪟 构建Windows版本...');
    execSync('npm run build:win', { stdio: 'inherit' });
    
    console.log('\n🎉 构建完成！');
    
    // 显示构建结果
    const distDir = path.join(__dirname, 'dist');
    if (fs.existsSync(distDir)) {
      console.log('\n📦 构建产物:');
      const files = fs.readdirSync(distDir);
      files.forEach(file => {
        const filePath = path.join(distDir, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
        console.log(`   ${file} (${sizeInMB}MB)`);
      });
    }
    
  } catch (error) {
    console.error('❌ 构建失败:', error.message);
    process.exit(1);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const skipVersionCheck = args.includes('--skip-version-check');
  
  if (!skipVersionCheck) {
    if (!checkVersionConsistency()) {
      console.error('\n❌ 版本号检查失败，构建中止');
      console.log('💡 使用 --skip-version-check 参数可跳过版本检查');
      process.exit(1);
    }
    
    console.log('\n✅ 版本号检查通过');
  } else {
    console.log('⚠️  跳过版本号检查');
  }
  
  runBuild();
}

// 运行主函数
main();
