#!/usr/bin/env node

/**
 * 更新API测试脚本
 * 用于测试M芯片和Intel芯片的更新API是否正确工作
 */

const axios = require('axios');

// 测试配置
const testConfig = {
  version: '1.0.0',
  baseUrl: 'https://xiaomeihuakefu.cn/api',
  timeout: 10000
};

// 测试用例
const testCases = [
  {
    name: 'M芯片Mac更新检查',
    url: `${testConfig.baseUrl}/app_update_macos_m1.php`,
    params: {
      action: 'check',
      version: testConfig.version,
      platform: 'macos_m1',
      architecture: 'm1'
    },
    headers: {
      'User-Agent': 'XiaoMeiHua-App/1.0.0 (macos; m1)'
    },
    expectedFields: ['has_update', 'current_version', 'latest_version']
  },
  {
    name: 'Intel芯片Mac更新检查',
    url: `${testConfig.baseUrl}/app_update_macos_intel.php`,
    params: {
      action: 'check',
      version: testConfig.version,
      platform: 'macos_intel',
      architecture: 'intel'
    },
    headers: {
      'User-Agent': 'XiaoMeiHua-App/1.0.0 (macos; intel)'
    },
    expectedFields: ['has_update', 'current_version', 'latest_version']
  },
  {
    name: 'Windows更新检查',
    url: `${testConfig.baseUrl}/app_update_new.php`,
    params: {
      action: 'check',
      version: testConfig.version,
      platform: 'windows',
      architecture: 'x64'
    },
    headers: {
      'User-Agent': 'XiaoMeiHua-App/1.0.0 (windows; x64)'
    },
    expectedFields: ['has_update', 'current_version', 'latest_version']
  }
];

/**
 * 执行单个测试
 */
async function runSingleTest(testCase) {
  console.log(`\n🧪 测试: ${testCase.name}`);
  console.log(`📡 URL: ${testCase.url}`);
  console.log(`📋 参数: ${JSON.stringify(testCase.params)}`);
  
  try {
    const response = await axios.get(testCase.url, {
      params: testCase.params,
      headers: testCase.headers,
      timeout: testConfig.timeout,
      validateStatus: function (status) {
        return status >= 200 && status < 500; // 允许4xx错误用于测试
      }
    });
    
    console.log(`📊 状态码: ${response.status}`);
    
    if (response.status === 200) {
      const data = response.data;
      console.log(`✅ 响应成功`);
      console.log(`📄 响应数据:`, JSON.stringify(data, null, 2));
      
      // 检查必需字段
      if (data.success) {
        const missingFields = testCase.expectedFields.filter(field => 
          !data.data || !(field in data.data)
        );
        
        if (missingFields.length === 0) {
          console.log(`✅ 所有必需字段都存在`);
          
          // 检查更新逻辑
          if (data.data.has_update) {
            console.log(`🔄 发现更新: ${data.data.current_version} -> ${data.data.latest_version}`);
            
            // 检查下载链接
            if (data.data.update_info && data.data.update_info.download_url) {
              console.log(`📥 下载链接: ${data.data.update_info.download_url}`);
            } else {
              console.log(`⚠️  警告: 有更新但缺少下载链接`);
            }
          } else {
            console.log(`ℹ️  当前已是最新版本`);
          }
          
          return { success: true, data: data };
        } else {
          console.log(`❌ 缺少必需字段: ${missingFields.join(', ')}`);
          return { success: false, error: `缺少字段: ${missingFields.join(', ')}` };
        }
      } else {
        console.log(`❌ API返回错误: ${data.message}`);
        return { success: false, error: data.message };
      }
    } else {
      console.log(`❌ HTTP错误: ${response.status}`);
      console.log(`📄 错误响应:`, response.data);
      return { success: false, error: `HTTP ${response.status}` };
    }
    
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log(`🌐 网络连接被拒绝，请检查网络连接`);
    } else if (error.code === 'ETIMEDOUT') {
      console.log(`⏰ 请求超时，请检查网络连接`);
    }
    
    return { success: false, error: error.message };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 === M芯片和Intel芯片更新API测试 ===');
  console.log(`📋 测试版本: ${testConfig.version}`);
  console.log(`🌐 基础URL: ${testConfig.baseUrl}`);
  
  const results = [];
  
  for (const testCase of testCases) {
    const result = await runSingleTest(testCase);
    results.push({
      name: testCase.name,
      ...result
    });
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 汇总结果
  console.log('\n📊 === 测试结果汇总 ===');
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}: ${result.success ? '通过' : result.error}`);
  });
  
  console.log(`\n🎯 总体结果: ${successCount}/${totalCount} 通过`);
  
  if (successCount === totalCount) {
    console.log('🎉 所有测试通过！M芯片和Intel芯片更新API工作正常。');
  } else {
    console.log('⚠️  部分测试失败，请检查API配置和网络连接。');
  }
  
  return results;
}

/**
 * 测试架构检测
 */
function testArchitectureDetection() {
  console.log('\n🔍 === 本地架构检测测试 ===');
  
  const platform = process.platform;
  const arch = process.arch;
  
  console.log(`当前系统: ${platform} (${arch})`);
  
  let expectedApi = '';
  let expectedDescription = '';
  
  if (platform === 'darwin') {
    if (arch === 'arm64') {
      expectedApi = 'app_update_macos_m1.php';
      expectedDescription = 'Apple Silicon (M系列芯片)';
    } else {
      expectedApi = 'app_update_macos_intel.php';
      expectedDescription = 'Intel 处理器';
    }
  } else if (platform === 'win32') {
    expectedApi = 'app_update_new.php';
    expectedDescription = 'Windows x64';
  }
  
  console.log(`预期API: ${expectedApi}`);
  console.log(`预期描述: ${expectedDescription}`);
  
  return {
    platform,
    arch,
    expectedApi,
    expectedDescription
  };
}

// 主函数
async function main() {
  // 测试架构检测
  const archInfo = testArchitectureDetection();
  
  // 运行API测试
  const results = await runAllTests();
  
  console.log('\n💡 === 修复说明 ===');
  console.log('1. M芯片Mac现在使用专用API: app_update_macos_m1.php');
  console.log('2. Intel Mac现在使用专用API: app_update_macos_intel.php');
  console.log('3. 只有在对应架构有下载链接时才会提示更新');
  console.log('4. 避免了M芯片用户看到Intel版本更新的问题');
  
  return { archInfo, results };
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runAllTests, testArchitectureDetection };
