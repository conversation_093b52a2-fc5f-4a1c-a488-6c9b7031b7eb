﻿# Header, don't edit
NLF v6
# Language ID
1057
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1252
# RTL - anything else than RTL means LTR
-
# Revision date: 2009 April
# Translators:
## <AUTHOR> <EMAIL>, <www.ariel106.cjb.net>
## <EMAIL>
#
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Instalasi Program $(^Name)
# ^UninstallCaption
Penghapusan Program $(^Name)
# ^LicenseSubCaption
: Perihal Lisensi
# ^ComponentsSubCaption
: Pilihan Instalasi
# ^DirSubCaption
: Lokasi Instalasi
# ^InstallingSubCaption
: Proses Instalasi
# ^CompletedSubCaption
: Selesai
# ^UnComponentsSubCaption
: Pilihan Penghapusan
# ^UnDirSubCaption
: Berkas Lokasi yang dihapus
# ^ConfirmSubCaption
: Konfirmasi
# ^UninstallingSubCaption
: Proses Penghapusan
# ^UnCompletedSubCaption
: Selesai
# ^BackBtn
< &Mundur
# ^NextBtn
&Lanjut >
# ^AgreeBtn
Saya &Setuju
# ^AcceptBtn
Saya s&etuju dengan Perihal Lisensi
# ^DontAcceptBtn
Saya &tidak setuju dengan Perihal Lisensi
# ^InstallBtn
&Instal
# ^UninstallBtn
&Hapus
# ^CancelBtn
Batalkan
# ^CloseBtn
&Tutup
# ^BrowseBtn
Ca&ri...
# ^ShowDetailsBtn
Lihat &perincian
# ^ClickNext
Tekan tombol Lanjut untuk melanjutkan.
# ^ClickInstall
Tekan tombol Instal untuk memulai instalasi.
# ^ClickUninstall
Tekan tombol Hapus untuk memulai penghapusan.
# ^Name
Nama
# ^Completed
Selesai
# ^LicenseText
Silahkan membaca lisensi berikut sebelum memulai instalasi $(^NameDA). Jika anda menyetujui dan menerima semua pernyataan, tekan tombol Saya Setuju.
# ^LicenseTextCB
Silahkan membaca lisensi berikut sebelum memulai instalasi $(^NameDA). Jika anda menyetujui dan menerima semua pernyataan, beri tanda centang. $_CLICK
# ^LicenseTextRB
Silahkan membaca lisensi berikut sebelum memulai instalasi $(^NameDA). Jika anda menyetujui dan menerima semua pernyataan, pilihlah salah satu item dibawah ini. $_CLICK
# ^UnLicenseText
Silahkan membaca lisensi berikut sebelum mulai menghapus $(^NameDA). Jika anda menyetujui dan menerima semua pernyataan, tekan tombol Saya Setuju.
# ^UnLicenseTextCB
Silahkan membaca lisensi berikut sebelum mulai menghapus $(^NameDA). Jika anda menyetujui dan menerima semua pernyataan, beri tanda centang. $_CLICK
# ^UnLicenseTextRB
Silahkan membaca lisensi berikut sebelum mulai menghapus $(^NameDA). Jika anda menyetujui dan menerima semua pernyataan, pilihlah salah satu item dibawah ini. $_CLICK
# ^Custom
Tentukan Sendiri
# ^ComponentsText
Beri tanda centang pada komponen yang akan di instal and hilangkan tanda centang pada komponen yang tidak perlu di instal. $_CLICK
# ^ComponentsSubText1
Pilih tipe instalasi:
# ^ComponentsSubText2_NoInstTypes
Pilih komponen-komponen yang akan di instal:
# ^ComponentsSubText2
Atau, pilih komponen tambahan yang akan di instal:
# ^UnComponentsText
Beri tanda centang pada komponen yang akan dihapus and hilangkan tanda centang pada komponen yang tidak ingin dihapus. $_CLICK
# ^UnComponentsSubText1
Pilih tipe penghapusan:
# ^UnComponentsSubText2_NoInstTypes
Pilih komponen-komponen yang ingin dihapus:
# ^UnComponentsSubText2
Atau, pilih komponen tambahan yang ingin dihapus:
# ^DirText
Program $(^NameDA) akan di instal pada lokasi berikut. Untuk memilih lokasi, tekan tombol Cari kemudian pilih lokasi yang anda kehendaki. $_CLICK
# ^DirSubText
Lokasi instalasi
# ^DirBrowseText
Pilih lokasi instalasi program $(^NameDA):
# ^UnDirText
Proses penghapusan program $(^NameDA) dari lokasi instalasi berikut. Untuk memilih lokasi lainnya, tekan tombol Cari kemudian pilih lokasi yang anda kehendaki. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Pilih lokasi instalasi program $(^NameDA) yang akan dihapus:
# ^SpaceAvailable
"Ruang yang tersedia:   "
# ^SpaceRequired
"Ruang yang dibutuhkan: "
# ^UninstallingText
$(^NameDA) akan dihapus dari lokasi berikut. $_CLICK
# ^UninstallingSubText
Proses penghapusan dari:
# ^FileError
Tidak dapat membuka berkas untuk menulis: \r\n\t"$0"\r\nTekan tombol Abort untuk membatalkan instalasi,\r\nRetry untuk mencoba lagi, atau\r\nIgnore untuk melewati file ini.
# ^FileError_NoIgnore
Tidak dapat membuka berkas untuk menulis: \r\n\t"$0"\r\nTekan tombol Retry untuk mencoba lagi, atau\r\nCancel untuk membatalkan instalasi.
# ^CantWrite
"Tidak bisa menulis pada berkas: "
# ^CopyFailed
Gagal menyalin berkas
# ^CopyTo
"Menyalin ke "
# ^Registering
"Memasukkan dalam daftar: "
# ^Unregistering
"Menghapus dari daftar: "
# ^SymbolNotFound
"Tidak dapat menemukan simbol: "
# ^CouldNotLoad
"Tidak dapat memuat: "
# ^CreateFolder
"Membuat tempat menyimpan berkas: "
# ^CreateShortcut
"Membuat shortcut: "
# ^CreatedUninstaller
"Program penghapusan yang dibuat: "
# ^Delete
"Menghapus berkas: "
# ^DeleteOnReboot
"Akan dihapus saat reboot: "
# ^ErrorCreatingShortcut
"Tidak dapat membuat shortcut: "
# ^ErrorCreating
"Ada kesalahan saat membuat: "
# ^ErrorDecompressing
Ada kesalahan saat membuka data! Program Instalasi tidak lengkap?
# ^ErrorRegistering
Ada kesalahan ketika mendaftarkan modul DLL
# ^ExecShell
"Perintah: "
# ^Exec
"Menjalankan: "
# ^Extract
"Proses ekstraksi berkas: "
# ^ErrorWriting
"Ekstraksi: ada kesalahan saat menulis ke berkas "
# ^InvalidOpcode
Program instalasi rusak: kode program tidak lengkap
# ^NoOLE
"OLE tidak ditemukan: "
# ^OutputFolder
"Lokasi tujuan: "
# ^RemoveFolder
"Menghapus lokasi penyimpanan: "
# ^RenameOnReboot
"Memberi nama baru saat reboot: "
# ^Rename
"Memberi nama baru: "
# ^Skipped
"Dilewati: "
# ^CopyDetails
Salin perincian ke Clipboard
# ^LogInstall
Catat proses instalasi
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
