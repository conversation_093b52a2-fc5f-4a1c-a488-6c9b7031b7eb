﻿# Header, don't edit
NLF v6
# Language ID
1092
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1251
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> uly [ <EMAIL> ]
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Урнаштыру $(^Name)
# ^UninstallCaption
Бетерү $(^Name)
# ^LicenseSubCaption
: Лицензия килешүе
# ^ComponentsSubCaption
: Урнаштыру шартлары
# ^DirSubCaption
: Урнаштыру папкасы
# ^InstallingSubCaption
: Файлларны күчермәләү
# ^CompletedSubCaption
: Гамәл тәмамланды
# ^UnComponentsSubCaption
: Бетерү шартлары
# ^UnDirSubCaption
: Бетерү папкасы
# ^ConfirmSubCaption
: Раслау
# ^UninstallingSubCaption
: Файлларны бетерү
# ^UnCompletedSubCaption
: Гамәл тәмамланды
# ^BackBtn
< &Артка
# ^NextBtn
&Алга >
# ^AgreeBtn
Кабул ит&әм
# ^AcceptBtn
Мин &килешү шартларын кабул итәм
# ^DontAcceptBtn
Мин &килешү шартларын кабул итими
# ^InstallBtn
&Урнаштырырга
# ^UninstallBtn
Бе&терергә
# ^CancelBtn
Баш тарту
# ^CloseBtn
&Ябарга
# ^BrowseBtn
К&арарга...
# ^ShowDetailsBtn
&Тулырак...
# ^ClickNext
Дәвам итү өчен 'Алга' төймәсенә басыгыз.
# ^ClickInstall
Программаны урнаштыру өчен 'Урнаштырырга' төймәсенә басыгыз.
# ^ClickUninstall
Программаны бетерү өчен 'Бетерергә' төймәсенә басыгыз.
# ^Name
Исем
# ^Completed
Әзер
# ^LicenseText
$(^NameDA) урнаштыру алдыннан лицензия килешүе белән танышыгыз. Килешү шартларын кабул итсәгез, 'Кабул итәм' төймәсенә басыгыз.
# ^LicenseTextCB
$(^NameDA) урнаштыру алдыннан лицензия килешүе белән танышыгыз. Килешү шартларын кабул итсәгез, түбәндә билге куегыз. $_CLICK
# ^LicenseTextRB
$(^NameDA) урнаштыру алдыннан лицензия килешүе белән танышыгыз. Килешү шартларын кабул итсәгез, түбәндәге вариантлардан беренчесен сайлагыз. $_CLICK
# ^UnLicenseText
$(^NameDA) урнаштыру алдыннан лицензия килешүе белән танышыгыз. Килешү шартларын кабул итсәгез, 'Кабул итәм' төймәсенә басыгыз.
# ^UnLicenseTextCB
$(^NameDA) урнаштыру алдыннан лицензия килешүе белән танышыгыз. Килешү шартларын кабул итсәгез, түбәндә билге куегыз. $_CLICK
# ^UnLicenseTextRB
$(^NameDA) урнаштыру алдыннан лицензия килешүе белән танышыгыз. Килешү шартларын кабул итсәгез, түбәндәге вариантлардан беренчесен сайлагыз. $_CLICK
# ^Custom
Сайлап кына
# ^ComponentsText
Программаның урнаштырырга теләгән компонентларын сайлагыз. $_CLICK
# ^ComponentsSubText1
Урнаштыру төрен сайлагыз:
# ^ComponentsSubText2_NoInstTypes
Урнаштыру өчен программаның компонентларын сайлагыз:
# ^ComponentsSubText2
яки урнаштыру өчен өстәмә компонентлар сайлагыз:
# ^UnComponentsText
Бетерергә теләгән компонентларны сайлагыз. $_CLICK
# ^UnComponentsSubText1
Бетерү төрен сайлагыз:
# ^UnComponentsSubText2_NoInstTypes
Бетерү өчен компонентларны сайлагыз:
# ^UnComponentsSubText2
яки бетерү өчен өстәмә компонентларны сайлагыз:
# ^DirText
Программа $(^NameDA) программасын күрсәтерлән папкага урнаштырачак. Башка папкага урнаштыру өчен, 'Карарга' төймәсенә басыгыз һәм урын күрсәтегез. $_CLICK
# ^DirSubText
Урнаштыру папкасы
# ^DirBrowseText
$(^NameDA) урнаштыру өчен папка сайлагыз:
# ^UnDirText
Программа $(^NameDA) программасын күрсәтелгән папкадан бетерәчәк. Башка папкадан бетерү өчен, 'Карарга' төймәсенә басыгыз һәм урын күрсәтегез. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
$(^NameDA) бетерергә кирәк булган папканы күрсәтегез:
# ^SpaceAvailable
"Дискта буш урын: "
# ^SpaceRequired
"Дискта кирәк урын: "
# ^UninstallingText
$(^NameDA) программасы санагыгыздан бетереләчәк. $_CLICK
# ^UninstallingSubText
Моннан бетерү:
# ^FileError
Яздыру өчен файлны ачып булмый: \r\n\t"$0"\r\n'Туктату': урнаштыруны туктатырга;\r\n"Кабатлау": омтылышны кабатларга;\r\n"Калдыру": бу гамәлне төшереп калдырырга.
# ^FileError_NoIgnore
Яздыру өчен файлны ачып булмый: \r\n\t"$0"\r\n'Кабатлау': омтылышны кабатларга;\r\n'Баш тарту': урнаштыру барышын туктатырга.
# ^CantWrite
"Яздырып булмый: "
# ^CopyFailed
Күчермә ясауда хата
# ^CopyTo
"Монда күчермәләү: "
# ^Registering
"Теркәлү: "
# ^Unregistering
"Теркәүдән баш тарту: "
# ^SymbolNotFound
"Символны табып булмый: "
# ^CouldNotLoad
"Йөкләп булмый: "
# ^CreateFolder
"Папка ясау: "
# ^CreateShortcut
"Сылтама ясау: "
# ^CreatedUninstaller
"Бетерү программасын ясау: "
# ^Delete
"Файлны бетерү: "
# ^DeleteOnReboot
"Санак сүндереп кабызылганда бетерү: "
# ^ErrorCreatingShortcut
"Сылтама ясауда хата: " 
# ^ErrorCreating
"Ясауда хата: "
# ^ErrorDecompressing
Мәгълүматларны чыгаруда хата! Урнаштыручы бозык булуы мөмкин.
# ^ErrorRegistering
DLL теркәүдә хата
# ^ExecShell
"Тышча командасын башкару: " 
# ^Exec
"Башкару: "
# ^Extract
"Чыгару: "
# ^ErrorWriting
"Чыгару: файл яздыруда хата "
# ^InvalidOpcode
Урнаштыручы бозылган: мөмкин булмаган код
# ^NoOLE
"OLE моның өчен юк: " 
# ^OutputFolder
"Урнаштыру папкасы: "
# ^RemoveFolder
"Папканы бетерү: "
# ^RenameOnReboot
"Санак сүндереп кабызылганда исемен үзгәртү: "
# ^Rename
"Исемен үзгәртү: "
# ^Skipped
"Калдыру: "
# ^CopyDetails
Мәгълүматларны алмашыну буферына күчермәләргә
# ^LogInstall
Урнаштыру хисабын алып барырга
# byte (байт)
б
# kilo
 К
# mega
 М
# giga
 Г