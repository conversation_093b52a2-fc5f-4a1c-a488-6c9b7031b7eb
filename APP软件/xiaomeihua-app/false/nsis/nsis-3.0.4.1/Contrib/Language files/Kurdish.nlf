﻿# Header, don't edit
NLF v6
# Language ID (none exists for Kurdish at this time)
9999
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1254
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON><PERSON>(chagy) (<EMAIL>)
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) Sazkirin
# ^UninstallCaption
$(^Name) Rakirin
# ^LicenseSubCaption
: Peymana Lîsansê
# ^ComponentsSubCaption
: Vebijêrkên <PERSON>
# ^DirSubCaption
: Peldanka <PERSON>
# ^InstallingSubCaption
: Tê <PERSON>
# ^CompletedSubCaption
: Qediya
# ^UnComponentsSubCaption
: Vebijêrkên Ra<PERSON>
# ^UnDirSubCaption
: Peldanka Rakirinê
# ^ConfirmSubCaption
: Erêkirin
# ^UninstallingSubCaption
: Tê Ra<PERSON>rin
# ^UnCompletedSubCaption
: Qediya
# ^BackBtn
< &Vegere
# ^NextBtn
&Bidomîne >
# ^AgreeBtn
&Ez Dipejirînim
# ^AcceptBtn
Şertên Peymanê &Dipejirînim
# ^DontAcceptBtn
Şertên Peymanê Napejirînim
# ^InstallBtn
&Saz Bike
# ^UninstallBtn
&Rake
# ^CancelBtn
Betal
# ^CloseBtn
&Bigire
# ^BrowseBtn
&Çavlêgerîn...
# ^ShowDetailsBtn
Hûragahiyan &Nîşan Bide
# ^ClickNext
Ji bo berdewamê 'Bidomîne'yê bitikîne.
# ^ClickInstall
Ji bo destpêka sazkirinê 'Saz Bike'yê bitikîne.
# ^ClickUninstall
Ji bo destpêka rakirinê 'Rake' bitikîne.
# ^Name
nav
# ^Completed
Qediya
# ^LicenseText
Ji kerema xwe re berî tu bernameya $(^NameDA) saz bikî, çavekî li peymana lîsansê bigerîne. Heke tu hemû şertên peymanê dipejirînî, 'Ez Dipejirînim'ê bitikîne.
# ^LicenseTextCB
Ji kerema xwe re berî tu bernameya $(^NameDA) saz bikî, çavekî li peymana lîsansê bigerîne. Heke tu hemû şertan dipejirînî, zeviya erêkirinê ya jêrîn dagire. $_CLICK
# ^LicenseTextRB
Ji kerema xwe re berî tu bernameya $(^NameDA) saz bikî çavekî li peymana lîsansê bigerîne. Heke tu hemû şertên peymanê dipejirînî, zeviya vebijêrkê ya jêrîn dagire. $_CLICK
# ^UnLicenseText
Ji kerema xwe re berî tu bernameya $(^NameDA) rakî, çavekî li peymana lîsansê bigerîne. Heke tu hemû şertên peymanê dipejirînî, 'Ez Dipejirînim'ê bitikîn.
# ^UnLicenseTextCB
Ji kerema xwe re berî tu bernameya $(^NameDA) ji pergala xwe rakî, çavekî li peymana lîsansê bigerîne. Heke tu hemû şertên peymanê dipejirînî, zeviya jêrîn a erêkirinê dagire. $_CLICK
# ^UnLicenseTextRB
Ji kerema xwe re berî tu bernameya $(^NameDA) ji pergala xwe rakî, çavekî li peymana lîsansê bigerîne. Heke tu hemû şertên peymanê dipejirînî, zeviya vebijêrkê ya jêrîn dagire. $_CLICK
# ^Custom
Taybet
# ^ComponentsText
Beşên tu dixwazî saz bikî hilbijêre û niqirên 'check' beşên tu naxwazî werin sazkirin rake. $_CLICK
# ^ComponentsSubText1
Awayê sazkirinê hilbijêre:
# ^ComponentsSubText2_NoInstTypes
Beşên dê werin sazkirin hilbijêre:
# ^ComponentsSubText2
an jî, beşên beşên tu dixwazî werin sazkirin hilbijêre:
# ^UnComponentsText
Beşên tu dixwazî rakî hilbijêre, an jî niqira 'check'a ber beşên tu daxwazî were rakirin, rake. $_CLICK
# ^UnComponentsSubText1
Awayê rakirinê hilbijêre:
# ^UnComponentsSubText2_NoInstTypes
Beşên dê werin rakirin hilbijêre:
# ^UnComponentsSubText2
an jî beşên tu dixwazî werin rakirin hilbijêre:
# ^DirText
$(^NameDA) dê ji aliyê sazkirinê ve li peldanka jêrîn were sazkirin. Ji bo tu li peldankeke din saz bikî 'Çavlêgerîn'ê bitikîne û peldankeke din hilbijêre. $_CLICK
# ^DirSubText
Peldanka Armanckirî
# ^DirBrowseText
Peldanka tu dixwazî bernameya $(^NameDA) lê were sazkirin hilbijêre:
# ^UnDirText
$(^NameDA) dê ji aliyê sazkirinê ve ji peldanka jêrîn were rakirin. Ji bo tu ji peldankeke cuda rakî 'Çavlêgerîn'ê bitikîne û peldankeke din hilbijêre. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Peldanka tu dixwazî bernameya $(^NameDA) jê were rakirin hilbijêre:
# ^SpaceAvailable
"Herêma vala ku dikare were bikaranîn: "
# ^SpaceRequired
"Herêma vala ya pêwist: "
# ^UninstallingText
Bernameya $(^NameDA) dê ji peldanka jêrîn were rakirin. $_CLICK
# ^UninstallingSubText
tê rakirin:
# ^FileError
Dosya ji bo nivîsandinê venebû: \r\n\t"$0"\r\nJi bo destjêberdana sazkirinê abort'ê bitikîne,\r\nji bo ceribandina ji nû ve  retry'ê , an jî\r\nji bo tu dosiyê tune bihesibînî û berdewam bikî ignore'yê bitikîne
# ^FileError_NoIgnore
Dosya ji bo nivîsandinê vebenebû: \r\n\t"$0"\r\nJi bo nivîsandina ji nû ve retry'yê, an jî\r\nJi bo destjêberdana sazkirinê abort'ê hilbijêre
# ^CantWrite
"Nehate Nivîsandin: "
# ^CopyFailed
Çewtiya Jibergirtinê
# ^CopyTo
"Ji Ber Bigire "
# ^Registering
"Tê Tomarkirin: "
# ^Unregistering
"Tomarî Tê Jêbirin: "
# ^SymbolNotFound
"Dawêr Nehate Dîtin: "
# ^CouldNotLoad
"Nehate Barkirin: "
# ^CreateFolder
"Peldankê Çêke: "
# ^CreateShortcut
"Kineriyê Çêke: "
# ^CreatedUninstaller
"Sêrbazê Rakirinê Hate Çêkirin: "
# ^Delete
"Dosyayê Jê Bibe: "
# ^DeleteOnReboot
"Dema ji nû ve dest pê kir dosiyê jê bibe: "
# ^ErrorCreatingShortcut
"Dema çêkirina kineriyê çewtî derket: "
# ^ErrorCreating
"Çewtiya çêkirinê: "
# ^ErrorDecompressing
Di dema vekirina daneyan de çewtî derket! Sazkirina Çewt?
# ^ErrorRegistering
Çewtiya tomariya DLL
# ^ExecShell
"Qalikê Xebatê: "
# ^Exec
"Bixebitîne: "
# ^Extract
"Veke: "
# ^ErrorWriting
"Veke: Dema li dosiyê hate nivîsîn çewtiyek derket "
# ^InvalidOpcode
Sazkirina Xirabe: koda nerast pêkanînê
# ^NoOLE
"OLE nehate dîtin: "
# ^OutputFolder
"Peldanka derketinê: "
# ^RemoveFolder
"Peldankê jê bibe: "
# ^RenameOnReboot
"Dema ji nû hate destpêkirin ji nû ve bi nav bike: "
# ^Rename
"Nav Biguhere: "
# ^Skipped
"Hate gavkirin: "
# ^CopyDetails
Hûragahiyan li Pano'yê binivîse
# ^LogInstall
Pêkanîna sazkirinê li lênûska rewşê binivîse
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
