﻿# Header, don't edit
NLF v6
# Start editing here
# Language ID
1054
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
874
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON><PERSON><PERSON><PERSON><PERSON>, TuW@nNu (asdfuae)
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) ติดตั้ง
# ^UninstallCaption
$(^Name) ยกเลิกการติดตั้ง
# ^LicenseSubCaption
: ข้อตกลงเรื่องลิขสิทธิ์
# ^ComponentsSubCaption
: ตัวเลือกการติดตั้ง
# ^DirSubCaption
: แฟ้มที่ติดตั้ง
# ^InstallingSubCaption
: กำลังติดตั้ง
# ^CompletedSubCaption
: เสร็จสิ้น
# ^UnComponentsSubCaption
: ตัวเลือกยกเลิกการติดตั้ง
# ^UnDirSubCaption
: แฟ้มที่ยกเลิกการติดตั้ง
# ^ConfirmSubCaption
: ยืนยัน
# ^UninstallingSubCaption
: กำลังยกเลิกการติดตั้ง
# ^UnCompletedSubCaption
: เสร็จสิ้น
# ^BackBtn
< &กลับไป
# ^NextBtn
&ต่อไป >
# ^AgreeBtn
&ตกลง
# ^AcceptBtn
&ตกลงยอมรับข้อต่างๆในหัวข้อลิขสิทธิ์ 
# ^DontAcceptBtn
&ไม่ยอมรับข้อต่างๆในหัวข้อลิขสิทธิ์
# ^InstallBtn
&ติดตั้ง
# ^UninstallBtn
&ยกเลิกการติดตั้ง
# ^CancelBtn
ยกเลิก
# ^CloseBtn
&ปิด
# ^BrowseBtn
เ&รียกดู...
# ^ShowDetailsBtn
&รายละเอียด
# ^ClickNext
กด ต่อไป เพื่อเริ่มระบบอัติโนมัติ
# ^ClickInstall
กด  ติดตั้ง เพื่อทำการติดตั้ง
# ^ClickUninstall
กด  ยกเลิกการติดตั้ง เพื่อยกเลิกการติดตั้ง
# ^Name
ชื่อ
# ^Completed
เสร็จสิ้นแล้ว
# ^LicenseText
โปรดอ่านทวนเรื่องการยอมรับในลิขสิทธิ์ก่อนที่คุณจะทำการติดตั้ง $(^NameDA). ถ้าคุณยอมรับข้อตกลงในทุกๆด้าน, กด ฉันยอมรับ
# ^LicenseTextCB
โปรดอ่านทวนเรื่องการยอมรับในลิขสิทธิ์ก่อนที่คุณจะทำการติดตั้ง $(^NameDA). ถ้าคุณยอมรับข้อตกลงในทุกๆด้าน, กดเลือกที่กล่องด้านล่าง. $_CLICK
# ^LicenseTextRB
โปรดอ่านทวนเรื่องการยอมรับในลิขสิทธิ์ก่อนที่คุณจะทำการติดตั้ง $(^NameDA). ถ้าคุณยอมรับข้อตกลงในทุกๆด้าน, เลือกตัวเลือกแรกข้างล่าง. $_CLICK
# ^UnLicenseText
โปรดอ่านทวนเรื่องการยอมรับในลิขสิทธิ์ก่อนที่คุณจะทำการยกเลิกติดตั้ง $(^NameDA). ถ้าคุณยอมรับข้อตกลงในทุกๆด้าน, กด ฉันยอมรับ
# ^UnLicenseTextCB
โปรดอ่านทวนเรื่องการยอมรับในลิขสิทธิ์ก่อนที่คุณจะทำการยกเลิกติดตั้ง $(^NameDA). ถ้าคุณยอมรับข้อตกลงในทุกๆด้าน, กดเลือกที่กล่องด้านล่าง. $_CLICK
# ^UnLicenseTextRB
โปรดอ่านทวนเรื่องการยอมรับในลิขสิทธิ์ก่อนที่คุณจะทำการยกเลิกติดตั้ง $(^NameDA). ถ้าคุณยอมรับข้อตกลงในทุกๆด้าน, เลือกตัวเลือกแรกข้างล่าง. $_CLICK
# ^Custom
กำหนดเอง
# ^ComponentsText
เลื่อกสิ่งที่คุณต้องการติดตั้งและไม่เลือกสิ่งที่คุณไม่ต้องการติดตั้ง $_CLICK
# ^ComponentsSubText1
เลือกวิธีการการติดตั้ง:
# ^ComponentsSubText2_NoInstTypes
เลือกสิ่งที่คุณต้องการติดตั้ง:
# ^ComponentsSubText2
หรือ, เลือกตัวเลือกที่คุณต้องการติดตั้ง:
# ^UnComponentsText
เลือกตัวเลือกที่คุณต้องการจะยกเลิกการติดตั้งและไม่เลือกสิ่งที่คุณไม่ต้องการจะยกเลิกการติดตั้ง $_CLICK
# ^UnComponentsSubText1
เลือกวิธีการยกเลิกการติดตั้ง:
# ^UnComponentsSubText2_NoInstTypes
เลือกตัวเลือกที่ต้องการจะยกเลิกการติดตั้ง:
# ^UnComponentsSubText2
หรือ, เลือกจากตัวเลือกอื่นๆที่คุณต้องการจะยกเลิกการติดตั้ง:
# ^DirText
ตัวติดตั้งจะทำการติดตั้ง $(^NameDA) ลงในแฟ้มดังต่อไปนี้, ถ้าต้องการติดตั้งลงในแฟ้มอื่น, กด เรียกดูและเลือกแฟ้มอื่น $_CLICK
# ^DirSubText
แฟ้มที่ต้องการติดตั้ง
# ^DirBrowseText
เลือกแฟ้มที่ต้องการติดตั้ง $(^NameDA) ใน:
# ^UnDirText
ตัวติดตั้งจะทำการยกเลิกการติดตั้ง $(^NameDA) จากแฟ้มดังต่อไปนี้, ถ้าต้องการยกเลิกการติดตั้งจากแฟ้มอื่น, กด เรียกดู และเลือกแฟ้มอื่น $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
เลือกแฟ้มที่ต้องการยกเลิกการติดตั้ง $(^NameDA) จาก:
# ^SpaceAvailable
"มีพื้นที่เหลือ: "
# ^SpaceRequired
"ต้องการพื้นที่: "
# ^UninstallingText
$(^NameDA) จะถูกยกเลิกการติดตั้งจากแฟ้มต่อไปนี้. $_CLICK
# ^UninstallingSubText
ยกเลิกการติดตั้งจาก:
# ^FileError
ไม่สามารถเปิดไฟล์สำหรับเขียนได้: \r\n\r\n$0\r\n\r\nกด ยกเลิก เพื่อหยุดการติดตั้ง,\r\nลองอีกครั้ง เพื่อลองอีกครั้ง, หรือ\r\nเพิกเฉย เพื่อข้ามไฟล์นี้.
# ^FileError_NoIgnore
ไม่สามารถเปิดไฟล์สำหรับเขียนได้: \r\n\r\n$0\r\n\r\nกด ลองอีกครั้ง เพื่อลองอีกครั้ง, หรือ\r\nยกเลิกเพื่อหยุดการติดตั้ง
# ^CantWrite
"ไม่สามารถเขียน: "
# ^CopyFailed
คัดลอกผิดพลาด
# ^CopyTo
"คัดลอกไปยัง "
# ^Registering
"กำลังลงทะเบียน: "
# ^Unregistering
"ยกเลิกการลงทะเบียน: "
# ^SymbolNotFound
"ไม่สามารถหาสัญลักษณ์ได้: "
# ^CouldNotLoad
"ไม่สามารถโหลดได้: "
# ^CreateFolder
"สร้างแฟ้ม: "
# ^CreateShortcut
"สร้างชอร์ตคัท: "
# ^CreatedUninstaller
"สร้างตัวยกเลิกการติดตั้ง: "
# ^Delete
"ลบไฟล์: "
# ^DeleteOnReboot
"ลบตอนรีบูท: "
# ^ErrorCreatingShortcut
"มีปัญหาสร้างไฟล์ชอร์ตคัท: "
# ^ErrorCreating
"มีปัญหาในการสร้างไฟล์: "
# ^ErrorDecompressing
มีปัญหาในการคลายข้อมูล! เกิดข้อผิดพลาดจากตัวติดตั้ง?
# ^ErrorRegistering
มีปัญหาในการลงทะเบียน DLL
# ^ExecShell
"รันเชลล์ไฟล์: "
# ^Exec
"รันไฟล์: "
# ^Extract
"แตกไฟล์: "
# ^ErrorWriting
"แตกไฟล์: เกิดปัญหาในการเขียนไฟล์"
# ^InvalidOpcode
ตัวติดตั้งมีปัญหา: รหัส opcode ผิดพลาด
# ^NoOLE
"ไม่มี OLE สำหรับ: "
# ^OutputFolder
"แฟ้มทีติดตั้ง: "
# ^RemoveFolder
"ลบแฟ้ม: "
# ^RenameOnReboot
"เปลี่ยนชื่อตอนรีบูท: "
# ^Rename
"เปลี่ยนชื่อ: "
# ^Skipped
"ข้าม: "
# ^CopyDetails
คัดลอกรายละเอียดลงคลิปบอร์ด
# ^LogInstall
บันทึกการติดตั้ง
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
