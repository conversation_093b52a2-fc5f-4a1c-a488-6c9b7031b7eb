﻿# Header, don't edit
NLF v6
# Language ID
4103
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1252
# RTL - anything else than RTL means LTR
-
# Translation by <PERSON>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) Installatioun
# ^UninstallCaption
$(^Name) Desinstallatioun
# ^LicenseSubCaption
: <PERSON>en<PERSON><PERSON>kommes
# ^ComponentsSubCaption
: Installatiouns-Optiounen
# ^DirSubCaption
: Zielverzeechnis
# ^InstallingSubCaption
: Installéieren
# ^CompletedSubCaption
: Färdeg
# ^UnComponentsSubCaption
: Desinstallatiuons-Optiounen
# ^UnDirSubCaption
: Quellverzeechnes
# ^ConfirmSubCaption
: Bestätegung
# ^UninstallingSubCaption
: Läschen
# ^UnCompletedSubCaption
: Färdeg
# ^BackBtn
< &Zréck
# ^NextBtn
&Weider >
# ^AgreeBtn
&Unhuelen
# ^AcceptBtn
Ech &huelen d'Lizenzofkommes un.
# ^DontAcceptBtn
Ech &lehnen d'Lizenzofkommes of.
# ^InstallBtn
&Installéieren
# ^UninstallBtn
&Desinstalléieren
# ^CancelBtn
Ofbriechen
# ^CloseBtn
&Zou maan
# ^BrowseBtn
&Duerchsichen...
# ^ShowDetailsBtn
&Details uweisen
# ^ClickNext
Klick op weider fir weiderzefueren
# ^ClickInstall
Klick op Installéieren, fir d'Installatioun unzefänken.
# ^ClickUninstall
Klick op Desinstalléieren, fir d'Desinstallatioun unzefänken.
# ^Name
Numm
# ^Completed
Färdeg
# ^LicenseText
W.e.g. d'Lizenzofkommes liesen, ierts de $(^NameDA) installéiers. Wanns de all Bedengungen vum Ofkommes akzeptéiers, klick op Unhuelen.
# ^LicenseTextCB
W.e.g. d'Lizenzofkommes liesen, ierts de $(^NameDA) installéiers. Wanns de all Bedengungen vum Ofkommes akzeptéiers, aktivéier d'Kontrollkeschtchen. $_CLICK
# ^LicenseTextRB
W.e.g. d'Lizenzofkommes liesen, ierts de $(^NameDA) installéiers. Wanns de all Bedengungen vum Ofkommes akzeptéiers, wiel d'entsprichend Optioun. $_CLICK
# ^UnLicenseText
W.e.g. d'Lizenzofkommes liesen, ierts de $(^NameDA) desinstalléiers. Wanns de all Bedengungen vum Ofkommes akzeptéiers, klick op Unhuelen.
# ^UnLicenseTextCB
W.e.g. d'Lizenzofkommes liesen, ierts de $(^NameDA) desinstalléiers. Wanns de all Bedengungen vum Ofkommes akzeptéiers, aktivéier d'Kontrollkeschtchen. $_CLICK
# ^UnLicenseTextRB
W.e.g. d'Lizenzoofkommes liesen, ierts de $(^NameDA) desinstalléiers. Wanns de all Bedengungen vum Oofkommes akzeptéiers, wiel d'entspriechend Optioun. $_CLICK
# ^Custom
Benutzerdefiniert
# ^ComponentsText
Wiel d'Komponenten aus, déis de wëlls installéieren an wiel déijéineg of, déis de net installéieren wëlls. $_CLICK
# ^ComponentsSubText1
Installatiouns-Typ bestëmmen:
# ^ComponentsSubText2_NoInstTypes
Wiel d'Komponenten aus, déis de installéieren wëlls:
# ^ComponentsSubText2
oder wiel zousätzlech Komponenten aus déis de installéieren wëlls:
# ^UnComponentsText
Wiel d'Komponenten aus déis de desinstalléieren wëlls an wiel déijéineg of, déis de net desinstalléieren wëlls. $_CLICK
# ^UnComponentsSubText1
Deinstallatiouns-Typ bestëmmen:
# ^UnComponentsSubText2_NoInstTypes
Wiel d'Komponenten aus, déis de desinstalléieren wëlls:
# ^UnComponentsSubText2
oder wiel zusätzlech Komponenten aus, déis de desinstalléieren wëlls:
# ^DirText
$(^NameDA) gëtt an den Dossier installéiert deen fierginn gouf. Wanns de et an een aneren Dossier installéieren wëlls, klick op Duechsichen an wiel een aneren Dossier aus. $_CLICK
# ^DirSubText
Zielverzeechnes
# ^DirBrowseText
Wiel en Dossier aus wuers de $(^NameDA) installéieren wëlls:
# ^UnDirText
$(^NameDA) gëtt an deem Dossier desinstalléiert, deen uginn gouf. Wann $(^NameDA) an engem aneren Dossier ass, klick op Duechsichen an wiel een aneren Dossier aus. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Wiel den Dossier aus wou $(^NameDA) dran installéiert ass:
# ^SpaceAvailable
"Verfügbaren Späicher: "
# ^SpaceRequired
"Gebrauchten Späicher: "
# ^UninstallingText
$(^NameDA) gëtt aus dem ausgewielten Dossier desinstalléiert. $_CLICK
# ^UninstallingSubText
Desinstalléieren aus:
# ^FileError
Fehler beim Iwwerschreiwen vun der Datei: \r\n\t"$0"\r\nKlick op ofbriechen fir den Setup ze verloossen,\r\nop Widderhuelen fir den Setup nach eng Kéier duechzeféieren\r\n oder op Ignoréieren fir des Datei ze iwwersprengen an weiderzefueren.
# ^FileError_NoIgnore
Fehler beim Iwwerschreiwen vun der Datei: \r\n\t"$0"\r\nKlick op Widderhuelen fir den Setup nach eng Kéier duechzeféieren,\r\noder op ofbriechen fir den Setup ze verloossen.
# ^CantWrite
"Fehler beim Schreiwen: "
# ^CopyFailed
Kopéieren fehlgeschloen
# ^CopyTo
"Kopéiere an "
# ^Registering
"Registréieren: "
# ^Unregistering
"Deregistréieren: "
# ^SymbolNotFound
"Symbol ass net do: "
# ^CouldNotLoad
"Fehler beim Lueden vun: "
# ^CreateFolder
"Maan Dossier: "
# ^CreateShortcut
"Maan Oofkierzung: "
# ^CreatedUninstaller
"Man Desinstallatiouns-Programm: "
# ^Delete
"Läschen Datei: "
# ^DeleteOnReboot
"Läschen Datei no engem Neistart: "
# ^ErrorCreatingShortcut
"Fehler beim man vun enger Oofkierzung: "
# ^ErrorCreating
"Fehler beim maan: "
# ^ErrorDecompressing
Fehler beim Dekompriméieren. Installations-Programm beschiedegt?
# ^ErrorRegistering
Fehler beim Registréieren vun der DLL
# ^ExecShell
"ExecShell: "
# ^Exec
"Starten: "
# ^Extract
"Dekompriméieren: "
# ^ErrorWriting
"Dekompriméierung: Fehler beim Schreiwen vun der Datei "
# ^InvalidOpcode
Installations-Programm Beschiedegt: net zoulässegen Befehlscode
# ^NoOLE
"Keen OLE fier: "
# ^OutputFolder
"Zieldossier: "
# ^RemoveFolder
"Läschen Dossier: "
# ^RenameOnReboot
"Gett no Neistart embenannt: "
# ^Rename
"Embenennen: "
# ^Skipped
"Iwwersprongen: "
# ^CopyDetails
Detailler an d'Zwëschenooflag kopéieren
# ^LogInstall
Installatioun protokolléieren
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
