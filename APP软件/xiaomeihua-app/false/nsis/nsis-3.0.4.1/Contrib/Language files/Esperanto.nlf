﻿# Header, don't edit
NLF v6
# Start editing here
# Language ID (none exists for Esperanto at the moment)
9998
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
-
# RTL - anything else than RTL means LTR
-
# Translation v4.0.3 by <PERSON> <<EMAIL>>
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
Instalado de $(^Name)
# ^UninstallCaption
Malinstalado de $(^Name)
# ^LicenseSubCaption
: Permes-Kontrakto
# ^ComponentsSubCaption
: Instaladaj Opcioj
# ^DirSubCaption
: Instalada Dosierujo
# ^InstallingSubCaption
: Oni Instalas Dosierojn
# ^CompletedSubCaption
: Kompletite
# ^UnComponentsSubCaption
: Malinstaladaj Opcioj
# ^UnDirSubCaption
: Malinstalada Dosierujo
# ^ConfirmSubCaption
: Konfirmo
# ^UninstallingSubCaption
: Oni <PERSON>
# ^UnCompletedSubCaption
: Kompletite
# ^BackBtn
< &Antauxe
# ^NextBtn
&Sekve >
# ^AgreeBtn
&Akceptite
# ^AcceptBtn
Mi &akceptas la kondicxojn de la Permes-Kontrakto
# ^DontAcceptBtn
Mi &ne akceptas la kondicxojn de la Permes-Kontrakto
# ^InstallBtn
&Instali
# ^UninstallBtn
&Malinstali
# ^CancelBtn
Nuligi
# ^CloseBtn
&Fermi
# ^BrowseBtn
&Sercxi...
# ^ShowDetailsBtn
Vidi &Detalojn
# ^ClickNext
Musklaku en 'Sekve' por dauxrigi.
# ^ClickInstall
Musklaku en 'Instali' por ekigi la instaladon.
# ^ClickUninstall
Musklaku en 'Malinstali' por ekigi la malinstaladon.
# ^Name
Nomo
# ^Completed
Kompletite
# ^LicenseText
Bonvole revidu la permes-akordon antaux ol instali $(^NameDA). Se vi konsentas kun cxiuj kondicxoj de la permeso, musklaku en 'Akceptite'.
# ^LicenseTextCB
Bonvole revidu la permes-akordon antaux ol instali $(^NameDA). Se vi konsentas kun cxiuj kondicxoj de la permeso, musklaku en la suba elekt-skatolo. $_CLICK
# ^LicenseTextRB
Bonvole revidu la permes-akordon antaux ol instali $(^NameDA). Se vi konsentas kun cxiuj kondicxoj de la permeso, elektu la unuan opcion sube. $_CLICK
# ^UnLicenseText
Bonvole revidu la permes-akordon antaux ol malinstali $(^NameDA). Se vi konsentas kun cxiuj kondicxoj de la permeso, musklaku en 'Akceptite'.
# ^UnLicenseTextCB
Bonvole revidu la permes-akordon antaux ol malinstali $(^NameDA). Se vi konsentas kun cxiuj kondicxoj de la permeso, musklaku en la suba elekt-skatolo. $_CLICK
# ^UnLicenseTextRB
Bonvole revidu la permes-akordon antaux ol malinstali $(^NameDA). Se vi konsentas kun cxiuj kondicxoj de la permeso, elektu la unuan opcion sube. $_CLICK
# ^Custom
Personigite
# ^ComponentsText
Marku la konsisterojn, kiujn vi deziras instali kaj malmarku tiujn, kiujn vi ne deziras instali. $_CLICK
# ^ComponentsSubText1
Elektu la tipon de instalado:
# ^ComponentsSubText2_NoInstTypes
Elektu la konsisterojn por instali:
# ^ComponentsSubText2
Aux, elektu la nedevigajn konsisterojn, kiujn vi deziras instali:
# ^UnComponentsText
Marku la konsisterojn, kiujn vi volas malinstali aux male. $_CLICK 
# ^UnComponentsSubText1
Elektu la tipon de malinstalado:
# ^UnComponentsSubText2_NoInstTypes
Elektu la konsisterojn por malinstali:
# ^UnComponentsSubText2
Aux, elektu la nedevigajn konsisterojn, kiujn vi deziras malinstali:
# ^DirText
$(^NameDA) estos instalita en la jena dosierujo. Por instali en alia dosierujo, musklaku en 'Sercxi...' kaj elektu gxin. $_CLICK
# ^DirSubText
Celota Dosierujo
# ^DirBrowseText
Elektu dosierujon por instali $(^NameDA):
# ^UnDirText
$(^NameDA) estos malinstalita el la jena dosierujo. Por malinstali en alia dosierujo, musklaku en 'Sercxi...' kaj elektu gxin. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Elektu dosierujon el kie $(^NameDA) estos malinstalita:
# ^SpaceAvailable
"Disponebla spaco: "
# ^SpaceRequired
"Postulata spaco: "
# ^UninstallingText
$(^NameDA) estos malinstalita el la jena dosierujo. $_CLICK
# ^UninstallingSubText
Malinstalado el:
# ^FileError
Eraro dum malfermo de dosiero por skribi: \r\n\t"$0"\r\nMusklaku en Cxesigi por finigi la instaladon,\r\Ripeti por provi refoje skribi sur la dosiero, aux\r\nPreteratenti por preteratenti tiun cxi dosieron.
# ^FileError_NoIgnore
Eraro dum malfermo de dosierujo por skribi: \r\n\t"$0"\r\nMusklaku en Ripeti por provi refoje skribi sur la dosiero, aux\r\nNuligi por cxesigi la instaladon.
# ^CantWrite
"Ne eblis skribi: "
# ^CopyFailed
Malsukceso dum kopio
# ^CopyTo
"Kopii al "
# ^Registering
"Oni registras: "
# ^Unregistering
"Oni malregistras: "
# ^SymbolNotFound
"Ne trovita simbolo: "
# ^CouldNotLoad
"Ne eblis sxargi: "
# ^CreateFolder
"Oni kreas subdosierujon: "
# ^CreateShortcut
"Oni kreas lancxilon: "
# ^CreatedUninstaller
"Oni kreas malinstalilon: "
# ^Delete
"Oni forigas dosieron: "
# ^DeleteOnReboot
"Forigi je restarto: "
# ^ErrorCreatingShortcut
"Eraro dum kreo de lancxilo: "
# ^ErrorCreating
"Eraro dum kreo: "
# ^ErrorDecompressing
Eraro dum malkompaktigo de datumaro! Cxu misrompita instalilo?
# ^ErrorRegistering
Eraru dum registro de DLL
# ^ExecShell
"ExecShell: "
# ^Exec
"Lancxi: "
# ^Extract
"Eltiri: "
# ^ErrorWriting
"Eltirado: eraro dum skribo de dosiero "
# ^InvalidOpcode
Misrompita instalilo: malvalida operaci-kodo
# ^NoOLE
"Sen OLE por: "
# ^OutputFolder
"Celota dosierujo: "
# ^RemoveFolder
"Oni forigas la dosierujon: "
# ^RenameOnReboot
"Renomigi je restarto: "
# ^Rename
"Oni renomigas: "
# ^Skipped
"Preterpasita: "
# ^CopyDetails
Kopii detalojn al la tondejo
# ^LogInstall
Registri instalad-procezo
# ^Byte
B
# ^Kilo
 k
# ^Mega
 M
# ^Giga
 G
