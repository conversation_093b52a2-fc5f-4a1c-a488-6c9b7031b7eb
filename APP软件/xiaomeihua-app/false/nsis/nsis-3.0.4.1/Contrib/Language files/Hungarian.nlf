﻿# Header, don't edit
NLF v6
# Language ID
1038
# Font and size - dash (-) means default
-
-
# Codepage - dash (-) means ASCII code page
1250
# RTL - anything else than RTL means LTR
-
# Translation by Soft-Trans Bt. (V2)
# Translation by <PERSON><PERSON><PERSON>.   (V3-V6)
# ^Branding
Nullsoft Install System %s
# ^SetupCaption
$(^Name) Telepítő
# ^UninstallCaption
$(^Name) Eltávolító
# ^LicenseSubCaption
: Licencszerződés
# ^ComponentsSubCaption
: Telepítési lehetőségek
# ^DirSubCaption
: Célmappa
# ^InstallingSubCaption
: Fájlok telepítése
# ^CompletedSubCaption
: Kész
# ^UnComponentsSubCaption
: Eltávolítási lehetőségek
# ^UnDirSubCaption
: Eltávolítás mappája
# ^ConfirmSubCaption
: Megerősítés
# ^UninstallingSubCaption
: <PERSON><PERSON><PERSON><PERSON><PERSON>ávolítása
# ^UnCompletedSubCaption
: K<PERSON>z
# ^BackBtn
< &Vissza
# ^NextBtn
&Tovább >
# ^AgreeBtn
&Elfogadom
# ^AcceptBtn
&Elfogadom a Licencszerződés feltételeit
# ^DontAcceptBtn
&Nem fogadom el a Licencszerződés feltételeit
# ^InstallBtn
&Telepítés
# ^UninstallBtn
&Eltávolítás
# ^CancelBtn
&Mégse
# ^CloseBtn
&Bezárás
# ^BrowseBtn
&Tallózás...
# ^ShowDetailsBtn
&Részletek
# ^ClickNext
Kattintson a Tovább-ra a folytatáshoz.
# ^ClickInstall
Kattintson a Telepítésre a telepítéshez.
# ^ClickUninstall
Kattintson az Eltávolításra az eltávolításhoz.
# ^Name
Név
# ^Completed
Kész
# ^LicenseText
A(z) $(^NameDA) telepítése előtt tekintse át a szerződés feltételeit. Ha elfogadja a szerződés valamennyi feltételét, az Elfogadom gombbal folytathatja.
# ^LicenseTextCB
A(z) $(^NameDA) telepítése előtt tekintse át a szerződés feltételeit. Ha elfogadja a szerződés valamennyi feltételét, jelölje be a Jelölőnégyzeten. $_CLICK
# ^LicenseTextRB
A(z) $(^NameDA) telepítése előtt tekintse át a szerződés feltételeit. Ha elfogadja a szerződés valamennyi feltételét, válassza az első lehetőséget. $_CLICK
# ^UnLicenseText
A(z) $(^NameDA) eltávolítása előtt tekintse át a szerződés feltételeit. Ha elfogadja a szerződés valamennyi feltételét, az Elfogadom gombbal folytathatja.
# ^UnLicenseTextCB
A(z) $(^NameDA) eltávolítása előtt tekintse át a szerződés feltételeit. Ha elfogadja a szerződés valamennyi feltételét, jelölje be a Jelölőnégyzeten. $_CLICK
# ^UnLicenseTextRB
A(z) $(^NameDA) eltávolítása előtt tekintse át a szerződés feltételeit. Ha elfogadja a szerződés valamennyi feltételét, válassza az első lehetőséget. $_CLICK
# ^Custom
Egyéni
# ^ComponentsText
Jelölje be azokat az összetevőket amelyeket telepíteni kíván és törölje a jelölést a nem kívánt összetevőknél. $_CLICK
# ^ComponentsSubText1
Válassza ki a telepítés típusát:
# ^ComponentsSubText2_NoInstTypes
Válassza ki a telepítendő összetevőket:
# ^ComponentsSubText2
vagy, jelölje ki a választható összetevők közül a telepíteni kívánta(ka)t:
# ^UnComponentsText
Jelölje be azokat az összetevőket amelyeket el kíván távolítani és törölje a jelölést az eltávolítani nem kívánt összetevőknél. $_CLICK
# ^UnComponentsSubText1
Válassza ki az Eltávolítás típusát:
# ^UnComponentsSubText2_NoInstTypes
Válassza ki az eltávolítandó összetevőket:
# ^UnComponentsSubText2
vagy, jelölje ki a választható összetevők közül az eltávolítani kívánta(ka)t:
# ^DirText
A $(^NameDA) a következő mappába kerül. Másik mappa választásához kattintson a Tallózás gombra. $_CLICK
# ^DirSubText
Telepítés helye
# ^DirBrowseText
A(z) $(^NameDA) telepítési helyének kiválasztása:
# ^UnDirText
A(z) $(^NameDA) eltávolítása a következő mappából. Másik mappa választásához kattintson a Tallózás gombra. $_CLICK
# ^UnDirSubText
""
# ^UnDirBrowseText
Válassza ki, hogy a $(^NameDA) melyik mappából kerüljön eltávolításra:
# ^SpaceAvailable
"Szabad terület: "
# ^SpaceRequired
"Helyigény: "
# ^UninstallingText
A(z) $(^NameDA) eltávolítása következik a számítógépről. $_CLICK
# ^UninstallingSubText
Eltávolítás helye:
# ^FileError
Hiba történt a fájl írásra történő megnyitásakor: \r\n\t"$0"\r\nA Mégse gomb megnyomásával megszakíthatja a telepítést,\r\naz Ismét gombbal megismételheti a fájl írását,\r\na Kihagyás gombbal kihagyhatja ezt a fájlt.
# ^FileError_NoIgnore
Hiba történt a fájl írásra történő megnyitásakor:  \r\n\t"$0"\r\nAz Újra gomb megnyomásával megismételheti a műveletet, vagy \r\na Mégse gombbal megszakíthatja a telepítést.
# ^CantWrite
"Nem írható: "
# ^CopyFailed
A másolás megszakadt
# ^CopyTo
"Másolás ide: "
# ^Registering
"Bejegyzés: "
# ^Unregistering
"Eltávolítás: "
# ^SymbolNotFound
"A következő szimbólum nem található: "
# ^CouldNotLoad
"Nem tölthető be: "
# ^CreateFolder
"Mappa létrehozás: "
# ^CreateShortcut
"Parancsikon létrehozása: "
# ^CreatedUninstaller
"Létrehozott eltávolító: "
# ^Delete
"Törölt fájl: "
# ^DeleteOnReboot
"Rendszerindításkor törlendő: "
# ^ErrorCreatingShortcut
"Hiba a parancsikon létrehozásakor: "
# ^ErrorCreating
"Hiba a létrehozáskor: "
# ^ErrorDecompressing
Hiba az adatok kibontásakor! Megsérült a Telepítő?
# ^ErrorRegistering
Hiba a DLL regisztrálásakor
# ^ExecShell
"Végrehajtás a hozzárendeléseken keresztül: "
# ^Exec
"Végrehajtás: "
# ^Extract
"Kibontás: "
# ^ErrorWriting
"Kibontás: Hiba a fájl írásakor "
# ^InvalidOpcode
Sérült a telepítő: hibás utasítás
# ^NoOLE
"Nincs OLE: "
# ^OutputFolder
"Kimeneti mappa: "
# ^RemoveFolder
"Mappa eltávolítása: "
# ^RenameOnReboot
"Átnevezés rendszerindításkor: "
# ^Rename
"Átnevezés: "
# ^Skipped
"Kihagyott: "
# ^CopyDetails
Adatok vágólapra másolása
# ^LogInstall
Telepítő ellenőrzőlista
# ^Byte
B
# ^Kilo
 K
# ^Mega
 M
# ^Giga
 G
