#!/usr/bin/env node

/**
 * 快速构建脚本 - 重新打包dmg和exe
 * 使用最简单的方式快速生成安装包
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('⚡ 快速构建 - 小梅花AI智能客服');
console.log('================================\n');

function log(message, type = 'info') {
  const icons = {
    info: '📝',
    success: '✅', 
    error: '❌',
    warning: '⚠️',
    build: '🔨'
  };
  console.log(`${icons[type]} ${message}`);
}

function runCommand(command, description) {
  log(`${description}...`, 'build');
  try {
    execSync(command, { stdio: 'inherit', cwd: __dirname });
    log(`${description} 完成`, 'success');
  } catch (error) {
    log(`${description} 失败`, 'error');
    throw error;
  }
}

async function main() {
  try {
    const startTime = Date.now();
    
    // 1. 清理旧构建
    log('清理旧构建产物...', 'build');
    if (fs.existsSync('dist')) {
      runCommand('rimraf dist', '删除dist目录');
    }
    
    // 2. 检查依赖
    if (!fs.existsSync('node_modules')) {
      runCommand('npm install', '安装依赖');
    }
    
    // 3. 构建Windows版本
    log('构建Windows EXE...', 'build');
    runCommand('npm run build:win', '构建Windows版本');
    
    // 4. 构建macOS版本
    log('构建macOS DMG...', 'build');
    runCommand('npm run build:mac', '构建macOS版本');
    
    // 5. 检查构建结果
    const distDir = path.join(__dirname, 'dist');
    if (fs.existsSync(distDir)) {
      const files = fs.readdirSync(distDir);
      const exeFiles = files.filter(f => f.endsWith('.exe'));
      const dmgFiles = files.filter(f => f.endsWith('.dmg'));
      
      console.log('\n📦 构建结果:');
      console.log(`Windows EXE: ${exeFiles.length}个`);
      exeFiles.forEach(f => console.log(`  - ${f}`));
      console.log(`macOS DMG: ${dmgFiles.length}个`);
      dmgFiles.forEach(f => console.log(`  - ${f}`));
    }
    
    const duration = Math.round((Date.now() - startTime) / 1000);
    log(`构建完成！耗时 ${duration}秒`, 'success');
    log('安装包位置: dist/ 目录', 'info');
    
  } catch (error) {
    log(`构建失败: ${error.message}`, 'error');
    process.exit(1);
  }
}

main();
