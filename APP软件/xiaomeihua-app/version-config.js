/**
 * 小梅花AI智能客服 - 版本配置文件
 * 统一管理所有版本号，确保版本一致性
 * 
 * 注意：只有在明确要求更新版本号时才修改此文件
 * 所有版本号都从这个文件读取，确保全局一致
 */

const VERSION_CONFIG = {
  // 主版本号 - 所有地方都使用这个版本号
  VERSION: '1.0.4',
  
  // 产品信息
  PRODUCT_NAME: '小梅花AI智能客服',
  COMPANY: '小梅花AI科技',
  COPYRIGHT_YEAR: '2025',
  
  // 构建信息
  BUILD_TIME: new Date().toISOString(),
  BUILD_PLATFORM: process.platform,
  NODE_VERSION: process.version,
  
  // 版本显示格式
  getVersionString() {
    return `v${this.VERSION}`;
  },
  
  // 完整版本信息
  getFullVersionString() {
    return `${this.getVersionString()} © ${this.COPYRIGHT_YEAR} ${this.COMPANY}`;
  },
  
  // 获取版本号（不带v前缀）
  getVersionNumber() {
    return this.VERSION;
  },
  
  // 获取产品名称和版本
  getProductNameWithVersion() {
    return `${this.PRODUCT_NAME} ${this.getVersionString()}`;
  }
};

module.exports = VERSION_CONFIG;
