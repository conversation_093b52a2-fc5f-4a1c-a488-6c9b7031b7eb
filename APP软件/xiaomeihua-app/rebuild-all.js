#!/usr/bin/env node

/**
 * 重新打包dmg和exe软件
 * 清理后的完整构建脚本
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 === 小梅花AI智能客服 - 重新打包脚本 ===\n');

// 配置
const config = {
  productName: '小梅花AI智能客服',
  version: '1.0.0',
  platforms: {
    windows: true,
    macos: true
  },
  macArchs: ['x64', 'arm64', 'universal'], // Intel, Apple Silicon, Universal
  cleanBefore: true
};

// 工具函数
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = {
    info: '📝',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    build: '🔨'
  }[type] || '📝';
  
  console.log(`[${timestamp}] ${prefix} ${message}`);
}

function execCommand(command, description) {
  log(`${description}...`, 'build');
  try {
    const result = execSync(command, { 
      stdio: 'inherit',
      cwd: __dirname
    });
    log(`${description} 完成`, 'success');
    return result;
  } catch (error) {
    log(`${description} 失败: ${error.message}`, 'error');
    throw error;
  }
}

// 清理旧的构建产物
function cleanBuild() {
  log('清理旧的构建产物...', 'build');
  
  const distDir = path.join(__dirname, 'dist');
  const releaseDir = path.join(__dirname, 'release');
  
  if (fs.existsSync(distDir)) {
    execCommand('rimraf dist', '删除dist目录');
  }
  
  if (fs.existsSync(releaseDir)) {
    execCommand('rimraf release', '删除release目录');
  }
  
  log('清理完成', 'success');
}

// 检查环境
function checkEnvironment() {
  log('检查构建环境...', 'build');
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  log(`Node.js版本: ${nodeVersion}`);
  
  // 检查package.json
  const packagePath = path.join(__dirname, 'package.json');
  if (!fs.existsSync(packagePath)) {
    throw new Error('package.json文件不存在');
  }
  
  // 检查源码目录
  const srcDir = path.join(__dirname, 'src');
  if (!fs.existsSync(srcDir)) {
    throw new Error('src目录不存在');
  }
  
  // 检查构建资源
  const buildDir = path.join(__dirname, 'build');
  if (!fs.existsSync(buildDir)) {
    throw new Error('build目录不存在');
  }
  
  log('环境检查通过', 'success');
}

// 安装依赖
function installDependencies() {
  log('检查并安装依赖...', 'build');
  
  const nodeModulesDir = path.join(__dirname, 'node_modules');
  if (!fs.existsSync(nodeModulesDir)) {
    execCommand('npm install', '安装依赖');
  } else {
    log('依赖已存在，跳过安装', 'info');
  }
}

// 构建Windows版本
function buildWindows() {
  if (!config.platforms.windows) {
    log('跳过Windows构建', 'warning');
    return;
  }
  
  log('开始构建Windows版本...', 'build');
  execCommand('npm run build:win', '构建Windows EXE');
  log('Windows版本构建完成', 'success');
}

// 构建macOS版本
function buildMacOS() {
  if (!config.platforms.macos) {
    log('跳过macOS构建', 'warning');
    return;
  }
  
  log('开始构建macOS版本...', 'build');
  
  // 构建不同架构的版本
  for (const arch of config.macArchs) {
    log(`构建macOS ${arch}版本...`, 'build');
    try {
      if (arch === 'universal') {
        execCommand('npm run build:dmg:universal', `构建macOS Universal版本`);
      } else {
        execCommand(`npm run build:dmg:${arch}`, `构建macOS ${arch}版本`);
      }
      log(`macOS ${arch}版本构建完成`, 'success');
    } catch (error) {
      log(`macOS ${arch}版本构建失败，继续其他版本`, 'warning');
    }
  }
}

// 整理构建产物
function organizeBuildArtifacts() {
  log('整理构建产物...', 'build');
  
  const distDir = path.join(__dirname, 'dist');
  const releaseDir = path.join(__dirname, 'release');
  
  if (!fs.existsSync(distDir)) {
    log('没有找到构建产物', 'warning');
    return;
  }
  
  // 创建发布目录
  if (!fs.existsSync(releaseDir)) {
    fs.mkdirSync(releaseDir, { recursive: true });
  }
  
  const windowsDir = path.join(releaseDir, 'windows');
  const macosDir = path.join(releaseDir, 'macos');
  
  [windowsDir, macosDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
  
  // 移动文件
  const files = fs.readdirSync(distDir);
  
  files.forEach(file => {
    const srcPath = path.join(distDir, file);
    let destDir;
    
    if (file.endsWith('.exe')) {
      destDir = windowsDir;
    } else if (file.endsWith('.dmg')) {
      destDir = macosDir;
    } else {
      return; // 跳过其他文件
    }
    
    const destPath = path.join(destDir, file);
    fs.copyFileSync(srcPath, destPath);
    log(`移动文件: ${file} -> ${path.basename(destDir)}/`, 'info');
  });
  
  log('构建产物整理完成', 'success');
}

// 生成构建报告
function generateBuildReport() {
  log('生成构建报告...', 'build');
  
  const releaseDir = path.join(__dirname, 'release');
  const windowsDir = path.join(releaseDir, 'windows');
  const macosDir = path.join(releaseDir, 'macos');
  
  const report = {
    buildTime: new Date().toISOString(),
    version: config.version,
    productName: config.productName,
    windows: [],
    macos: []
  };
  
  // 统计Windows文件
  if (fs.existsSync(windowsDir)) {
    const windowsFiles = fs.readdirSync(windowsDir);
    windowsFiles.forEach(file => {
      const filePath = path.join(windowsDir, file);
      const stats = fs.statSync(filePath);
      report.windows.push({
        filename: file,
        size: `${(stats.size / 1024 / 1024).toFixed(2)} MB`,
        created: stats.birthtime.toISOString()
      });
    });
  }
  
  // 统计macOS文件
  if (fs.existsSync(macosDir)) {
    const macosFiles = fs.readdirSync(macosDir);
    macosFiles.forEach(file => {
      const filePath = path.join(macosDir, file);
      const stats = fs.statSync(filePath);
      report.macos.push({
        filename: file,
        size: `${(stats.size / 1024 / 1024).toFixed(2)} MB`,
        created: stats.birthtime.toISOString()
      });
    });
  }
  
  // 保存报告
  const reportPath = path.join(releaseDir, 'build-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  // 显示报告
  console.log('\n📊 === 构建报告 ===');
  console.log(`构建时间: ${new Date(report.buildTime).toLocaleString()}`);
  console.log(`产品版本: ${report.version}`);
  console.log(`\nWindows版本 (${report.windows.length}个):`);
  report.windows.forEach(file => {
    console.log(`  - ${file.filename} (${file.size})`);
  });
  console.log(`\nmacOS版本 (${report.macos.length}个):`);
  report.macos.forEach(file => {
    console.log(`  - ${file.filename} (${file.size})`);
  });
  
  log(`构建报告已保存: ${reportPath}`, 'success');
}

// 主函数
async function main() {
  try {
    const startTime = Date.now();
    
    // 1. 检查环境
    checkEnvironment();
    
    // 2. 清理旧构建
    if (config.cleanBefore) {
      cleanBuild();
    }
    
    // 3. 安装依赖
    installDependencies();
    
    // 4. 构建Windows版本
    buildWindows();
    
    // 5. 构建macOS版本
    buildMacOS();
    
    // 6. 整理构建产物
    organizeBuildArtifacts();
    
    // 7. 生成构建报告
    generateBuildReport();
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('\n🎉 === 构建完成 ===');
    log(`总耗时: ${duration}秒`, 'success');
    log('所有安装包已生成在 release/ 目录中', 'success');
    
  } catch (error) {
    log(`构建失败: ${error.message}`, 'error');
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, config };
