#!/usr/bin/env node

/**
 * M芯片API专项测试
 */

const axios = require('axios');

async function testM1Api() {
  console.log('🧪 测试M芯片API...');
  
  const url = 'https://xiaomeihuakefu.cn/api/app_update_macos_m1_fixed.php';
  const params = {
    action: 'check',
    version: '1.0.0'
  };
  
  try {
    console.log(`📡 请求URL: ${url}`);
    console.log(`📋 参数: ${JSON.stringify(params)}`);
    
    const response = await axios.get(url, {
      params: params,
      headers: {
        'User-Agent': 'XiaoMeiHua-App/1.0.0 (macos; m1)',
        'Accept': 'application/json'
      },
      timeout: 10000,
      validateStatus: function (status) {
        return status >= 200 && status < 600; // 接受所有状态码
      }
    });
    
    console.log(`📊 状态码: ${response.status}`);
    console.log(`📄 响应头: ${JSON.stringify(response.headers, null, 2)}`);
    console.log(`📄 响应数据: ${JSON.stringify(response.data, null, 2)}`);
    
    if (response.status === 200) {
      console.log('✅ API响应成功');
    } else {
      console.log(`❌ API返回错误状态: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    
    if (error.response) {
      console.log(`📊 错误状态码: ${error.response.status}`);
      console.log(`📄 错误响应: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

testM1Api();
