#!/usr/bin/env node

/**
 * DMG软件"已损坏"问题修复脚本
 * 解决macOS显示"已损坏"而不是"无法验证"的问题
 * 
 * 根本原因：
 * - Sealed Resources=none - 没有资源封装
 * - Info.plist=not bound - Info.plist 没有绑定到签名中
 * 
 * 解决方案：
 * 1. 重新进行 adhoc 签名并封装资源
 * 2. 确保 Info.plist 正确绑定
 * 3. 重新打包 DMG
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const crypto = require('crypto');

class DMGDamageFixTool {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.tempDir = path.join(this.projectRoot, 'temp-fix');
        this.fixedDir = path.join(this.projectRoot, 'release', 'fixed');
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        this.targetArch = args[0] || 'universal'; // x64, arm64, universal
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        this.log('🔧 DMG"已损坏"问题修复工具');
        this.log(`目标架构: ${this.targetArch}`);
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',    // 青色
            success: '\x1b[32m', // 绿色
            warning: '\x1b[33m', // 黄色
            error: '\x1b[31m',   // 红色
            step: '\x1b[35m',    // 紫色
            reset: '\x1b[0m'     // 重置
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            this.log(`执行命令: ${command}`, 'info');
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令执行失败但忽略错误: ${error.message}`, 'warning');
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 步骤1: 环境检查
     */
    checkEnvironment() {
        this.log('检查修复环境...', 'step');
        
        // 检查操作系统
        if (process.platform !== 'darwin') {
            throw new Error('此脚本只能在macOS系统上运行');
        }
        
        // 检查必要工具
        const tools = ['codesign', 'hdiutil', 'plutil', 'xattr'];
        for (const tool of tools) {
            try {
                this.exec(`which ${tool}`, { silent: true });
                this.log(`${tool} 工具可用`, 'success');
            } catch (error) {
                throw new Error(`${tool} 工具不可用，请确保已安装Xcode Command Line Tools`);
            }
        }
        
        // 检查DMG文件是否存在
        const dmgFile = this.getDMGFileName();
        const dmgPath = path.join(this.distDir, dmgFile);
        if (!fs.existsSync(dmgPath)) {
            throw new Error(`DMG文件不存在: ${dmgPath}`);
        }
        
        this.log('环境检查完成', 'success');
        return dmgPath;
    }

    /**
     * 获取DMG文件名
     */
    getDMGFileName() {
        return `${this.productName}-${this.version}-${this.targetArch}.dmg`;
    }

    /**
     * 步骤2: 挂载DMG并提取应用程序
     */
    extractAppFromDMG(dmgPath) {
        this.log('挂载DMG并提取应用程序...', 'step');
        
        // 清理临时目录
        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
        }
        fs.mkdirSync(this.tempDir, { recursive: true });
        
        // 挂载DMG
        this.log('挂载DMG文件...');
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        
        // 解析挂载点
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        if (mountLines.length === 0) {
            throw new Error('无法找到DMG挂载点');
        }
        
        const mountPoint = mountLines[0].split('\t').pop().trim();
        this.log(`DMG已挂载到: ${mountPoint}`);
        
        try {
            // 查找应用程序
            const appName = `${this.productName}.app`;
            const appPath = path.join(mountPoint, appName);
            
            if (!fs.existsSync(appPath)) {
                throw new Error(`应用程序不存在: ${appPath}`);
            }
            
            // 复制应用程序到临时目录
            const tempAppPath = path.join(this.tempDir, appName);
            this.log(`复制应用程序到临时目录...`);
            this.exec(`cp -R "${appPath}" "${tempAppPath}"`);
            
            this.log('应用程序提取完成', 'success');
            return tempAppPath;
            
        } finally {
            // 卸载DMG
            this.log('卸载DMG...');
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 步骤3: 检查当前签名状态
     */
    checkCurrentSignature(appPath) {
        this.log('检查当前应用程序签名状态...', 'step');
        
        try {
            const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
            this.log('当前签名信息:');
            console.log(signInfo);
            
            // 检查是否有Sealed Resources
            if (signInfo.includes('Sealed Resources=none')) {
                this.log('发现问题: Sealed Resources=none (没有资源封装)', 'warning');
            }
            
            // 检查Info.plist绑定
            if (signInfo.includes('Info.plist=not bound')) {
                this.log('发现问题: Info.plist=not bound (Info.plist未绑定)', 'warning');
            }
            
        } catch (error) {
            this.log('应用程序未签名或签名无效', 'warning');
        }
        
        // 检查Info.plist文件
        const infoPlistPath = path.join(appPath, 'Contents', 'Info.plist');
        if (fs.existsSync(infoPlistPath)) {
            this.log('Info.plist文件存在', 'success');
            
            // 验证Info.plist格式
            try {
                this.exec(`plutil -lint "${infoPlistPath}"`, { silent: true });
                this.log('Info.plist格式正确', 'success');
            } catch (error) {
                this.log('Info.plist格式有问题', 'error');
                throw error;
            }
        } else {
            throw new Error('Info.plist文件不存在');
        }
    }

    /**
     * 步骤4: 移除现有签名
     */
    removeExistingSignature(appPath) {
        this.log('移除现有签名...', 'step');
        
        try {
            this.exec(`codesign --remove-signature "${appPath}"`, { ignoreError: true });
            this.log('现有签名已移除', 'success');
        } catch (error) {
            this.log('没有现有签名需要移除', 'info');
        }
        
        // 移除扩展属性
        this.exec(`xattr -cr "${appPath}"`, { ignoreError: true });
        this.log('扩展属性已清理', 'success');
    }

    /**
     * 步骤5: 进行adhoc签名（包含资源封装）
     */
    performAdhocSigning(appPath) {
        this.log('进行adhoc签名（包含资源封装）...', 'step');
        
        // 第一步：深度签名，包含资源封装
        this.log('执行深度adhoc签名...');
        this.exec(`codesign --force --deep --sign - "${appPath}"`);
        
        // 第二步：重新签名以确保Info.plist绑定
        this.log('重新签名以绑定Info.plist...');
        this.exec(`codesign --force --sign - --preserve-metadata=entitlements "${appPath}"`);
        
        this.log('adhoc签名完成', 'success');
    }

    /**
     * 步骤6: 验证修复后的签名
     */
    verifyFixedSignature(appPath) {
        this.log('验证修复后的签名...', 'step');
        
        try {
            const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
            this.log('修复后签名信息:');
            console.log(signInfo);
            
            // 检查修复结果
            let isFixed = true;
            
            if (signInfo.includes('Sealed Resources=none')) {
                this.log('❌ 资源封装仍然缺失', 'error');
                isFixed = false;
            } else {
                this.log('✅ 资源封装已修复', 'success');
            }
            
            if (signInfo.includes('Info.plist=not bound')) {
                this.log('❌ Info.plist仍未绑定', 'error');
                isFixed = false;
            } else {
                this.log('✅ Info.plist已正确绑定', 'success');
            }
            
            // 验证签名有效性
            this.exec(`codesign --verify --verbose "${appPath}"`);
            this.log('✅ 签名验证通过', 'success');
            
            return isFixed;
            
        } catch (error) {
            this.log('签名验证失败', 'error');
            throw error;
        }
    }

    /**
     * 步骤7: 重新打包DMG
     */
    repackageDMG(appPath) {
        this.log('重新打包DMG...', 'step');

        // 创建修复后的目录
        if (!fs.existsSync(this.fixedDir)) {
            fs.mkdirSync(this.fixedDir, { recursive: true });
        }

        // 创建临时DMG目录
        const tempDMGDir = path.join(this.tempDir, 'dmg-contents');
        fs.mkdirSync(tempDMGDir, { recursive: true });

        // 复制修复后的应用程序
        const appName = `${this.productName}.app`;
        const dmgAppPath = path.join(tempDMGDir, appName);
        this.exec(`cp -R "${appPath}" "${dmgAppPath}"`);

        // 复制修复脚本和说明文件
        this.copyDMGResources(tempDMGDir);

        // 生成新的DMG文件名
        const fixedDMGName = `${this.productName}-${this.version}-${this.targetArch}-fixed.dmg`;
        const fixedDMGPath = path.join(this.fixedDir, fixedDMGName);

        // 删除旧的DMG文件（如果存在）
        if (fs.existsSync(fixedDMGPath)) {
            fs.unlinkSync(fixedDMGPath);
        }

        // 创建新的DMG
        this.log('创建新的DMG文件...');
        this.exec(`hdiutil create -srcfolder "${tempDMGDir}" -format UDZO -volname "${this.productName} ${this.version}" "${fixedDMGPath}"`);

        this.log(`修复后的DMG已创建: ${fixedDMGPath}`, 'success');
        return fixedDMGPath;
    }

    /**
     * 复制DMG资源文件
     */
    copyDMGResources(dmgDir) {
        this.log('复制DMG资源文件...');

        const resourcesDir = path.join(this.projectRoot, 'resources');

        // 复制修复脚本
        const fixScriptSrc = path.join(resourcesDir, '修复已损坏.command');
        if (fs.existsSync(fixScriptSrc)) {
            const fixScriptDest = path.join(dmgDir, '修复已损坏.command');
            fs.copyFileSync(fixScriptSrc, fixScriptDest);

            // 设置执行权限
            this.exec(`chmod +x "${fixScriptDest}"`);
            this.log('修复脚本已复制', 'success');
        }

        // 复制安装说明
        const installGuideSrc = path.join(resourcesDir, '安装前先打开.txt');
        if (fs.existsSync(installGuideSrc)) {
            const installGuideDest = path.join(dmgDir, '安装前先打开.txt');
            fs.copyFileSync(installGuideSrc, installGuideDest);
            this.log('安装说明已复制', 'success');
        }

        // 创建Applications链接
        const applicationsLink = path.join(dmgDir, 'Applications');
        if (!fs.existsSync(applicationsLink)) {
            this.exec(`ln -s /Applications "${applicationsLink}"`);
            this.log('Applications链接已创建', 'success');
        }
    }

    /**
     * 步骤8: 验证修复后的DMG
     */
    verifyFixedDMG(dmgPath) {
        this.log('验证修复后的DMG...', 'step');

        // 验证DMG文件完整性
        try {
            this.exec(`hdiutil verify "${dmgPath}"`, { silent: true });
            this.log('DMG文件完整性验证通过', 'success');
        } catch (error) {
            this.log('DMG文件完整性验证失败', 'warning');
        }

        // 挂载并测试应用程序
        this.log('挂载DMG并测试应用程序...');
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });

        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        if (mountLines.length === 0) {
            throw new Error('无法挂载修复后的DMG');
        }

        const mountPoint = mountLines[0].split('\t').pop().trim();

        try {
            const appName = `${this.productName}.app`;
            const mountedAppPath = path.join(mountPoint, appName);

            if (!fs.existsSync(mountedAppPath)) {
                throw new Error('修复后的DMG中找不到应用程序');
            }

            // 验证应用程序签名
            const signInfo = this.exec(`codesign -dv --verbose=4 "${mountedAppPath}" 2>&1`, { silent: true });

            if (!signInfo.includes('Sealed Resources=none') && !signInfo.includes('Info.plist=not bound')) {
                this.log('✅ 修复后的应用程序签名正常', 'success');
                return true;
            } else {
                this.log('❌ 修复后的应用程序仍有签名问题', 'error');
                return false;
            }

        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 步骤9: 生成修复报告
     */
    generateFixReport(dmgPath, isSuccess) {
        this.log('生成修复报告...', 'step');

        const report = {
            timestamp: new Date().toISOString(),
            productName: this.productName,
            version: this.version,
            architecture: this.targetArch,
            originalDMG: this.getDMGFileName(),
            fixedDMG: path.basename(dmgPath),
            fixSuccess: isSuccess,
            fixSteps: [
                '1. 环境检查',
                '2. 提取应用程序',
                '3. 检查当前签名状态',
                '4. 移除现有签名',
                '5. 进行adhoc签名（包含资源封装）',
                '6. 验证修复后的签名',
                '7. 重新打包DMG',
                '8. 验证修复后的DMG'
            ],
            recommendations: isSuccess ? [
                '修复成功！现在应用程序应该显示"无法验证"而不是"已损坏"',
                '用户可以通过"系统偏好设置" > "安全性与隐私"允许运行',
                '或使用提供的修复脚本移除隔离属性'
            ] : [
                '修复失败，请检查错误日志',
                '确保在macOS系统上运行',
                '确保已安装Xcode Command Line Tools',
                '检查应用程序结构是否完整'
            ]
        };

        const reportPath = path.join(this.fixedDir, 'fix-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

        this.log(`修复报告已生成: ${reportPath}`, 'success');
        return report;
    }

    /**
     * 清理临时文件
     */
    cleanup() {
        this.log('清理临时文件...', 'step');

        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
            this.log('临时文件已清理', 'success');
        }
    }

    /**
     * 显示最终结果
     */
    showResults(dmgPath, report) {
        console.log('\n' + '='.repeat(60));
        if (report.fixSuccess) {
            this.log('🎉 DMG"已损坏"问题修复完成！', 'success');
        } else {
            this.log('❌ DMG修复失败', 'error');
        }
        console.log('='.repeat(60));

        console.log(`\n📋 修复信息:`);
        console.log(`  产品: ${this.productName}`);
        console.log(`  版本: ${this.version}`);
        console.log(`  架构: ${this.targetArch}`);
        console.log(`  原始DMG: ${report.originalDMG}`);
        console.log(`  修复后DMG: ${report.fixedDMG}`);

        if (report.fixSuccess) {
            console.log(`\n📁 修复后文件位置: ${this.fixedDir}`);
            console.log(`\n✨ 修复效果:`);
            console.log(`  - 应用程序现在应该显示"无法验证"而不是"已损坏"`);
            console.log(`  - 用户可以通过系统设置允许运行`);
            console.log(`  - 或使用提供的修复脚本移除隔离属性`);

            console.log(`\n💡 用户安装步骤:`);
            console.log(`  1. 下载修复后的DMG文件`);
            console.log(`  2. 双击打开DMG`);
            console.log(`  3. 将应用拖拽到Applications文件夹`);
            console.log(`  4. 首次运行时选择"打开"（如果出现安全提示）`);

            if (process.platform === 'darwin') {
                console.log(`\n🔍 快速查看:`);
                console.log(`  open "${this.fixedDir}"`);
            }
        } else {
            console.log(`\n❌ 修复失败原因:`);
            report.recommendations.forEach(rec => {
                console.log(`  - ${rec}`);
            });
        }
    }

    /**
     * 主修复流程
     */
    async fix() {
        try {
            console.log('🚀 开始修复DMG"已损坏"问题...\n');

            // 执行修复步骤
            const dmgPath = this.checkEnvironment();
            const appPath = this.extractAppFromDMG(dmgPath);
            this.checkCurrentSignature(appPath);
            this.removeExistingSignature(appPath);
            this.performAdhocSigning(appPath);
            const isSignatureFixed = this.verifyFixedSignature(appPath);

            if (!isSignatureFixed) {
                throw new Error('签名修复失败');
            }

            const fixedDMGPath = this.repackageDMG(appPath);
            const isDMGValid = this.verifyFixedDMG(fixedDMGPath);
            const report = this.generateFixReport(fixedDMGPath, isDMGValid);

            this.showResults(fixedDMGPath, report);

            return report.fixSuccess;

        } catch (error) {
            this.log(`修复失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);

            const report = this.generateFixReport('', false);
            this.showResults('', report);

            return false;
        } finally {
            this.cleanup();
        }
    }
}

// 主程序入口
async function main() {
    const fixer = new DMGDamageFixTool();
    const success = await fixer.fix();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = DMGDamageFixTool;
