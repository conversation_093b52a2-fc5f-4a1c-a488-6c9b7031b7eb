#!/usr/bin/env node

/**
 * 优化版DMG构建器
 * 解决启动速度慢和移除废纸篓按钮的问题
 * 
 * 核心优化策略：
 * 1. 使用最简化的签名方式，避免过度签名
 * 2. 优化资源封装，减少验证开销
 * 3. 移除导致"移到废纸篓"按钮的属性
 * 4. 保持快速启动的同时避免"已损坏"提示
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class OptimizedDMGBuilder {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.tempDir = path.join(this.projectRoot, 'temp-optimized');
        this.optimizedDir = path.join(this.projectRoot, 'release', 'optimized');
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        this.targetArch = args[0] || 'universal';
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        this.log('🚀 优化版DMG构建器');
        this.log(`目标架构: ${this.targetArch}`);
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            optimize: '\x1b[94m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄',
            optimize: '🚀'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            this.log(`执行命令: ${command}`, 'info');
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令执行失败但忽略错误: ${error.message}`, 'warning');
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 步骤1: 环境准备
     */
    prepareEnvironment() {
        this.log('准备优化构建环境...', 'optimize');
        
        // 清理临时目录，但保留优化目录中的现有文件
        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
        }
        fs.mkdirSync(this.tempDir, { recursive: true });

        // 确保优化目录存在，但不清理现有文件
        if (!fs.existsSync(this.optimizedDir)) {
            fs.mkdirSync(this.optimizedDir, { recursive: true });
        }
        
        this.log('环境准备完成', 'success');
    }

    /**
     * 步骤2: 查找现有DMG文件
     */
    findExistingDMG() {
        this.log('查找现有DMG文件...', 'optimize');
        
        // 查找现有的DMG文件
        const dmgPattern = `${this.productName}-${this.version}-${this.targetArch}.dmg`;
        const dmgPath = path.join(this.distDir, dmgPattern);
        
        if (fs.existsSync(dmgPath)) {
            this.log(`找到现有DMG文件: ${dmgPattern}`, 'success');
            return dmgPath;
        }
        
        // 如果没有找到，尝试查找其他版本
        if (fs.existsSync(this.distDir)) {
            const files = fs.readdirSync(this.distDir);
            const dmgFiles = files.filter(file =>
                file.endsWith('.dmg') &&
                file.includes(this.targetArch) &&
                file.includes(this.productName)
            );

            if (dmgFiles.length > 0) {
                const foundDMG = dmgFiles[0];
                const foundPath = path.join(this.distDir, foundDMG);
                this.log(`找到DMG文件: ${foundDMG}`, 'success');
                return foundPath;
            }
        }
        
        throw new Error(`未找到${this.targetArch}架构的DMG文件，请先运行构建命令`);
    }

    /**
     * 步骤3: 提取并优化清理应用程序
     */
    extractAndOptimizeApp(dmgPath) {
        this.log('提取并优化清理应用程序...', 'optimize');
        
        // 挂载DMG
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();
        
        try {
            // 复制应用程序
            const appName = `${this.productName}.app`;
            const sourceAppPath = path.join(mountPoint, appName);
            const tempAppPath = path.join(this.tempDir, appName);
            
            this.exec(`cp -R "${sourceAppPath}" "${tempAppPath}"`);
            
            // 优化清理应用程序
            this.optimizedCleanApp(tempAppPath);
            
            return tempAppPath;
            
        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 优化清理应用程序
     */
    optimizedCleanApp(appPath) {
        this.log('优化清理应用程序...', 'optimize');
        
        // 1. 移除所有签名（但保留基本结构）
        this.exec(`codesign --remove-signature "${appPath}"`, { ignoreError: true });
        
        // 2. 只移除关键的扩展属性，保留必要的属性
        this.exec(`xattr -d com.apple.quarantine "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        this.exec(`xattr -d com.apple.metadata:kMDItemWhereFroms "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        
        // 3. 移除导致"移到废纸篓"按钮的特定属性
        this.exec(`xattr -d com.apple.metadata:_kMDItemUserTags "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        this.exec(`xattr -d com.apple.FinderInfo "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        this.exec(`xattr -d com.apple.ResourceFork "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        
        // 4. 修复权限
        this.exec(`chmod -R 755 "${appPath}"`);
        this.exec(`chmod +x "${appPath}/Contents/MacOS/"*`);
        
        this.log('优化清理完成', 'success');
    }

    /**
     * 步骤4: 应用优化签名策略
     */
    applyOptimizedSignature(appPath) {
        this.log('应用优化签名策略...', 'optimize');
        
        // 优化策略：使用最简化的签名方式
        // 只进行必要的签名，避免过度签名导致的性能问题
        
        // 1. 先签名所有框架
        this.log('签名所有框架...');
        this.signAllFrameworks(appPath);

        // 2. 签名关键的Helper应用程序
        this.log('签名关键Helper应用程序...');
        const helperApps = [
            'Contents/Frameworks/小梅花AI智能客服 Helper.app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (GPU).app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (Plugin).app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (Renderer).app'
        ];

        helperApps.forEach(helperPath => {
            const fullHelperPath = path.join(appPath, helperPath);
            if (fs.existsSync(fullHelperPath)) {
                try {
                    this.exec(`codesign --force --sign - "${fullHelperPath}"`, { ignoreError: true });
                    this.log(`✅ 已签名: ${helperPath}`, 'success');
                } catch (error) {
                    this.log(`⚠️ 签名失败: ${helperPath}`, 'warning');
                }
            }
        });

        // 3. 使用深度签名确保所有组件都被签名
        this.log('执行优化的深度签名...');
        this.exec(`codesign --force --deep --sign - "${appPath}"`);
        
        // 3. 验证签名结果
        const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
        
        if (signInfo.includes('Sealed Resources version=')) {
            this.log('✅ 资源封装成功', 'success');
        }
        
        if (signInfo.includes('Info.plist entries=')) {
            this.log('✅ Info.plist绑定成功', 'success');
        }
        
        // 最终验证
        this.exec(`codesign --verify --verbose "${appPath}"`);
        this.log('优化签名策略应用完成', 'success');
    }

    /**
     * 签名所有框架
     */
    signAllFrameworks(appPath) {
        this.log('递归签名所有框架...');

        // 查找并签名所有框架和动态库
        const frameworksDir = path.join(appPath, 'Contents/Frameworks');

        if (fs.existsSync(frameworksDir)) {
            // 签名所有.framework
            this.exec(`find "${frameworksDir}" -name "*.framework" -exec codesign --force --sign - {} \\; 2>/dev/null || true`, { ignoreError: true });

            // 签名所有.dylib
            this.exec(`find "${frameworksDir}" -name "*.dylib" -exec codesign --force --sign - {} \\; 2>/dev/null || true`, { ignoreError: true });

            // 签名所有.so文件
            this.exec(`find "${frameworksDir}" -name "*.so" -exec codesign --force --sign - {} \\; 2>/dev/null || true`, { ignoreError: true });
        }

        // 签名Resources中的.node文件
        const resourcesDir = path.join(appPath, 'Contents/Resources');
        if (fs.existsSync(resourcesDir)) {
            this.exec(`find "${resourcesDir}" -name "*.node" -exec codesign --force --sign - {} \\; 2>/dev/null || true`, { ignoreError: true });
        }

        this.log('框架签名完成', 'success');
    }

    /**
     * 步骤5: 创建优化DMG
     */
    createOptimizedDMG(appPath) {
        this.log('创建优化DMG...', 'optimize');
        
        // 创建DMG内容目录
        const dmgContentDir = path.join(this.tempDir, 'optimized-dmg');
        fs.mkdirSync(dmgContentDir, { recursive: true });
        
        // 复制应用程序
        const appName = `${this.productName}.app`;
        const dmgAppPath = path.join(dmgContentDir, appName);
        this.exec(`cp -R "${appPath}" "${dmgAppPath}"`);
        
        // 对DMG中的应用程序进行最终优化
        this.finalOptimizeApp(dmgAppPath);
        
        // 复制优化的修复脚本
        this.createOptimizedFixScript(dmgContentDir);
        
        // 复制说明文件
        this.createOptimizedGuide(dmgContentDir);
        
        // 创建Applications链接
        this.exec(`ln -s /Applications "${path.join(dmgContentDir, 'Applications')}"`);
        
        // 生成优化DMG文件名
        const optimizedDMGName = `${this.productName}-${this.version}-${this.targetArch}-OPTIMIZED.dmg`;
        const optimizedDMGPath = path.join(this.optimizedDir, optimizedDMGName);
        
        // 删除旧文件
        if (fs.existsSync(optimizedDMGPath)) {
            fs.unlinkSync(optimizedDMGPath);
        }
        
        // 创建DMG，使用优化参数
        this.exec(`hdiutil create -srcfolder "${dmgContentDir}" -format UDZO -imagekey zlib-level=6 -volname "${this.productName} ${this.version} OPTIMIZED" "${optimizedDMGPath}"`);
        
        this.log(`优化DMG已创建: ${optimizedDMGPath}`, 'success');
        return optimizedDMGPath;
    }

    /**
     * 最终优化应用程序
     */
    finalOptimizeApp(appPath) {
        this.log('最终优化应用程序...', 'optimize');
        
        // 移除所有可能导致"移到废纸篓"按钮的属性
        this.exec(`xattr -cr "${appPath}"`);
        
        // 设置最优权限
        this.exec(`chmod 755 "${appPath}"`);
        this.exec(`chmod +x "${appPath}/Contents/MacOS/"*`);
        
        this.log('最终优化完成', 'success');
    }

    /**
     * 创建优化的修复脚本
     */
    createOptimizedFixScript(dmgDir) {
        const scriptContent = `#!/bin/bash

# 小梅花AI智能客服 - 优化修复脚本
# 快速解决macOS兼容性问题

APP_NAME="小梅花AI智能客服"
APP_PATH="/Applications/\${APP_NAME}.app"

echo "🚀 \${APP_NAME} - 优化修复工具"
echo "========================================"
echo ""

# 检查应用程序是否存在
if [ ! -d "\$APP_PATH" ]; then
    echo "❌ 错误: 应用程序未安装"
    echo "请先将 \${APP_NAME}.app 拖拽到 Applications 文件夹"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

echo "✅ 找到应用程序: \$APP_PATH"
echo ""

echo "🚀 执行优化修复..."
echo "正在请求管理员权限..."

# 1. 移除隔离属性
echo "1. 移除隔离属性..."
sudo xattr -d com.apple.quarantine "\$APP_PATH" 2>/dev/null || true

# 2. 移除导致废纸篓按钮的属性
echo "2. 移除问题属性..."
sudo xattr -d com.apple.metadata:kMDItemWhereFroms "\$APP_PATH" 2>/dev/null || true
sudo xattr -d com.apple.metadata:_kMDItemUserTags "\$APP_PATH" 2>/dev/null || true
sudo xattr -d com.apple.FinderInfo "\$APP_PATH" 2>/dev/null || true

# 3. 优化权限
echo "3. 优化权限..."
sudo chmod 755 "\$APP_PATH"
sudo chmod +x "\$APP_PATH/Contents/MacOS/"*

echo ""
echo "✅ 优化修复完成！"
echo ""
echo "💡 现在尝试打开应用程序："
echo "1. 双击应用程序图标"
echo "2. 应该快速启动，不会显示'移到废纸篓'按钮"
echo "3. 如果仍有提示，选择'打开'"
echo ""
echo "🚀 优化效果："
echo "- 移除了'移到废纸篓'按钮"
echo "- 提升了启动速度"
echo "- 保持了兼容性"
echo ""
echo "按任意键退出..."
read -n 1
`;

        const scriptPath = path.join(dmgDir, '优化修复.command');
        fs.writeFileSync(scriptPath, scriptContent);
        this.exec(`chmod +x "${scriptPath}"`);

        this.log('优化修复脚本已创建', 'success');
    }

    /**
     * 创建优化说明
     */
    createOptimizedGuide(dmgDir) {
        const guideContent = `小梅花AI智能客服 - 优化版安装指南

🚀 优化版本 - 快速启动，无废纸篓按钮

📋 安装步骤：
1. 将"小梅花AI智能客服.app"拖拽到"Applications"文件夹
2. 双击应用程序图标直接启动
3. 如有提示，选择"打开"（不会显示"移到废纸篓"按钮）

🚀 优化特性：
- 快速启动：应用程序启动速度已优化
- 简洁界面：移除了"移到废纸篓"按钮
- 兼容性好：保持与macOS的良好兼容性
- 签名优化：使用最简化的签名策略

⚠️ 重要说明：
- 此版本专门优化了启动性能
- 移除了导致"移到废纸篓"按钮的属性
- 如果仍有问题，请运行"优化修复.command"脚本

🛠️ 故障排除：
- 如果启动慢，请重启电脑后再试
- 如果仍显示废纸篓按钮，请运行修复脚本
- 建议在安装前关闭杀毒软件

📞 技术支持：
如有问题，请联系我们的技术支持团队。

版本：${this.version} (优化版)
构建时间：${new Date().toLocaleString('zh-CN')}
优化特性：快速启动 + 无废纸篓按钮
`;

        const guidePath = path.join(dmgDir, '优化版说明.txt');
        fs.writeFileSync(guidePath, guideContent);

        this.log('优化说明已创建', 'success');
    }

    /**
     * 步骤6: 验证优化效果
     */
    verifyOptimization(dmgPath) {
        this.log('验证优化效果...', 'optimize');

        // 验证DMG完整性
        this.exec(`hdiutil verify "${dmgPath}"`, { silent: true });
        this.log('✅ DMG完整性验证通过', 'success');

        // 挂载并检查应用程序
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();

        try {
            const appName = `${this.productName}.app`;
            const mountedAppPath = path.join(mountPoint, appName);

            // 检查应用程序存在
            if (!fs.existsSync(mountedAppPath)) {
                throw new Error('应用程序不存在');
            }

            // 检查签名状态
            const signInfo = this.exec(`codesign -dv --verbose=4 "${mountedAppPath}" 2>&1`, { silent: true });

            let optimizationSuccess = true;
            const issues = [];

            // 检查关键指标
            if (signInfo.includes('Sealed Resources version=')) {
                this.log('✅ 资源封装正常', 'success');
            } else {
                issues.push('资源封装缺失');
                optimizationSuccess = false;
            }

            if (signInfo.includes('Info.plist entries=')) {
                this.log('✅ Info.plist绑定正常', 'success');
            } else {
                issues.push('Info.plist未绑定');
                optimizationSuccess = false;
            }

            // 验证签名有效性
            try {
                this.exec(`codesign --verify "${mountedAppPath}"`, { silent: true });
                this.log('✅ 签名验证通过', 'success');
            } catch (error) {
                issues.push('签名验证失败');
                optimizationSuccess = false;
            }

            // 检查扩展属性（应该很少或没有）
            const xattrResult = this.exec(`xattr "${mountedAppPath}" 2>/dev/null || echo "no attributes"`, { silent: true });
            const attributeCount = xattrResult.trim().split('\n').filter(line => line.trim()).length;

            if (attributeCount <= 1) {
                this.log('✅ 扩展属性已优化', 'success');
            } else {
                this.log(`⚠️ 仍有 ${attributeCount} 个扩展属性`, 'warning');
            }

            // 检查签名复杂度（应该较低）
            const resourcesMatch = signInfo.match(/Sealed Resources version=\d+ rules=(\d+) files=(\d+)/);
            if (resourcesMatch) {
                const rules = parseInt(resourcesMatch[1]);
                const files = parseInt(resourcesMatch[2]);

                if (rules <= 15 && files <= 25) {
                    this.log('✅ 签名复杂度已优化', 'success');
                } else {
                    this.log(`⚠️ 签名复杂度较高: rules=${rules}, files=${files}`, 'warning');
                }
            }

            if (optimizationSuccess) {
                this.log('🚀 优化验证成功！', 'optimize');
            } else {
                this.log('⚠️ 优化可能不完全', 'warning');
                issues.forEach(issue => this.log(`  - ${issue}`, 'warning'));
            }

            return optimizationSuccess;

        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 清理临时文件
     */
    cleanup() {
        this.log('清理临时文件...', 'step');

        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
            this.log('临时文件已清理', 'success');
        }
    }

    /**
     * 显示最终结果
     */
    showResults(dmgPath, success) {
        console.log('\n' + '='.repeat(60));
        if (success) {
            this.log('🚀 优化DMG构建完成！', 'optimize');
        } else {
            this.log('⚠️ 优化可能不完全', 'warning');
        }
        console.log('='.repeat(60));

        console.log(`\n📋 优化信息:`);
        console.log(`  产品: ${this.productName}`);
        console.log(`  版本: ${this.version}`);
        console.log(`  架构: ${this.targetArch}`);
        console.log(`  优化DMG: ${path.basename(dmgPath)}`);

        if (success) {
            console.log(`\n🚀 优化效果:`);
            console.log(`  - 使用了最简化的签名策略`);
            console.log(`  - 移除了导致'移到废纸篓'按钮的属性`);
            console.log(`  - 优化了启动性能，减少验证开销`);
            console.log(`  - 保持了与macOS的良好兼容性`);
            console.log(`  - 应该快速启动，不显示废纸篓按钮`);
        }

        console.log(`\n💡 用户体验改进:`);
        console.log(`  1. 启动速度：应用程序启动更快`);
        console.log(`  2. 界面简洁：不会显示'移到废纸篓'按钮`);
        console.log(`  3. 兼容性好：仍然避免'已损坏'提示`);
        console.log(`  4. 用户友好：提供了优化修复脚本`);

        console.log(`\n📁 文件位置: ${this.optimizedDir}`);

        if (process.platform === 'darwin') {
            console.log(`\n🔍 快速查看:`);
            console.log(`  open "${this.optimizedDir}"`);
        }
    }

    /**
     * 主构建流程
     */
    async build() {
        try {
            console.log('🚀 开始优化DMG构建...\n');

            // 执行优化构建步骤
            this.prepareEnvironment();
            const dmgPath = this.findExistingDMG();
            const appPath = this.extractAndOptimizeApp(dmgPath);
            this.applyOptimizedSignature(appPath);
            const optimizedDMGPath = this.createOptimizedDMG(appPath);
            const success = this.verifyOptimization(optimizedDMGPath);

            this.showResults(optimizedDMGPath, success);

            return success;

        } catch (error) {
            this.log(`优化构建失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        } finally {
            this.cleanup();
        }
    }
}

// 主程序入口
async function main() {
    const builder = new OptimizedDMGBuilder();
    const success = await builder.build();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = OptimizedDMGBuilder;
