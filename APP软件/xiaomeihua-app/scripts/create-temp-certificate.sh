#!/bin/bash

# 创建临时自签名证书用于演示
# 注意：这只是用于演示，实际使用需要Apple Developer ID证书

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔧 创建临时自签名证书（仅用于演示）${NC}"
echo "=================================================="
echo ""

# 证书信息
CERT_NAME="XiaoMeiHua Temp Developer Certificate"
KEYCHAIN_NAME="login"

# 检查是否已存在证书
if security find-identity -v -p codesigning | grep -q "$CERT_NAME"; then
    echo -e "${YELLOW}⚠️  证书已存在，删除旧证书...${NC}"
    # 删除旧证书
    security delete-identity -c "$CERT_NAME" "$KEYCHAIN_NAME" 2>/dev/null || true
fi

echo -e "${BLUE}📝 使用系统工具创建临时自签名证书...${NC}"

# 使用 security 命令创建证书
security create-keypair -a 2048 -d "$CERT_NAME"
security add-certificates -k login.keychain "$CERT_NAME"

# 或者提示用户手动创建
echo -e "${YELLOW}请按照以下步骤手动创建临时证书:${NC}"
echo "1. 打开'钥匙串访问'应用"
echo "2. 菜单栏选择'钥匙串访问' > '证书助理' > '创建证书'"
echo "3. 填写以下信息:"
echo "   - 名称: $CERT_NAME"
echo "   - 身份类型: 自签名根证书"
echo "   - 证书类型: 代码签名"
echo "4. 点击'创建'完成"
echo ""
read -p "完成后按回车键继续..." -r

echo -e "${GREEN}✅ 临时证书创建完成${NC}"

# 验证证书
echo -e "${BLUE}🔍 验证证书...${NC}"
if security find-identity -v -p codesigning | grep -q "$CERT_NAME"; then
    echo -e "${GREEN}✅ 证书验证成功${NC}"
    
    # 保存证书配置
    echo "DEVELOPER_ID_CERT=\"$CERT_NAME\"" > .codesign-config
    echo -e "${GREEN}✅ 证书配置已保存${NC}"
    
    # 显示证书信息
    echo ""
    echo -e "${BLUE}📋 证书信息:${NC}"
    security find-identity -v -p codesigning | grep "$CERT_NAME"
    
else
    echo -e "${RED}❌ 证书验证失败${NC}"
    exit 1
fi

# 清理临时文件
rm -f /tmp/temp_cert.key /tmp/temp_cert.crt /tmp/temp_cert.p12 /tmp/cert_config.conf

echo ""
echo -e "${YELLOW}⚠️  重要提示:${NC}"
echo "1. 这是临时自签名证书，仅用于演示代码签名流程"
echo "2. 使用此证书签名的应用仍会被macOS标记为未验证"
echo "3. 实际使用需要申请Apple Developer ID证书"
echo "4. 现在可以运行: npm run build:signed"
echo ""
echo -e "${GREEN}🎉 临时证书设置完成！${NC}"
