#!/usr/bin/env node

/**
 * 清理旧DMG文件脚本
 * 
 * 功能：
 * 1. 删除dist目录中的所有旧DMG文件
 * 2. 删除release目录中除了optimized-final之外的所有DMG文件
 * 3. 保留最新的优化版本DMG文件
 */

const fs = require('fs');
const path = require('path');

class DMGCleaner {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.releaseDir = path.join(this.projectRoot, 'release');
        this.optimizedFinalDir = path.join(this.releaseDir, 'optimized-final');
        
        this.log('🧹 DMG文件清理工具', 'info');
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 获取目录中的DMG文件
     */
    getDMGFiles(directory) {
        if (!fs.existsSync(directory)) {
            return [];
        }
        
        const files = fs.readdirSync(directory);
        return files.filter(file => file.endsWith('.dmg'));
    }

    /**
     * 删除文件
     */
    deleteFile(filePath) {
        try {
            fs.unlinkSync(filePath);
            this.log(`已删除: ${path.basename(filePath)}`, 'success');
            return true;
        } catch (error) {
            this.log(`删除失败: ${path.basename(filePath)} - ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 清理dist目录中的DMG文件
     */
    cleanDistDirectory() {
        this.log('清理dist目录中的DMG文件...', 'step');
        
        const dmgFiles = this.getDMGFiles(this.distDir);
        
        if (dmgFiles.length === 0) {
            this.log('dist目录中没有DMG文件', 'info');
            return;
        }
        
        let deletedCount = 0;
        dmgFiles.forEach(file => {
            const filePath = path.join(this.distDir, file);
            if (this.deleteFile(filePath)) {
                deletedCount++;
            }
        });
        
        this.log(`dist目录清理完成，删除了 ${deletedCount} 个文件`, 'success');
    }

    /**
     * 清理release目录中的旧DMG文件（保留optimized-final）
     */
    cleanReleaseDirectory() {
        this.log('清理release目录中的旧DMG文件...', 'step');
        
        if (!fs.existsSync(this.releaseDir)) {
            this.log('release目录不存在', 'info');
            return;
        }
        
        // 获取release目录下的所有子目录和文件
        const items = fs.readdirSync(this.releaseDir);
        let deletedCount = 0;
        
        items.forEach(item => {
            const itemPath = path.join(this.releaseDir, item);
            const stat = fs.statSync(itemPath);
            
            // 跳过optimized-final目录
            if (item === 'optimized-final') {
                this.log(`保留: ${item}`, 'info');
                return;
            }
            
            if (stat.isDirectory()) {
                // 删除其他目录
                try {
                    fs.rmSync(itemPath, { recursive: true, force: true });
                    this.log(`已删除目录: ${item}`, 'success');
                    deletedCount++;
                } catch (error) {
                    this.log(`删除目录失败: ${item} - ${error.message}`, 'error');
                }
            } else if (item.endsWith('.dmg')) {
                // 删除DMG文件
                if (this.deleteFile(itemPath)) {
                    deletedCount++;
                }
            }
        });
        
        this.log(`release目录清理完成，删除了 ${deletedCount} 个项目`, 'success');
    }

    /**
     * 显示最终保留的文件
     */
    showFinalFiles() {
        this.log('显示最终保留的DMG文件...', 'step');
        
        const finalDMGFiles = this.getDMGFiles(this.optimizedFinalDir);
        
        if (finalDMGFiles.length === 0) {
            this.log('没有找到最终的DMG文件', 'warning');
            return;
        }
        
        console.log('\n📁 最终保留的DMG文件:');
        finalDMGFiles.forEach(file => {
            const filePath = path.join(this.optimizedFinalDir, file);
            const stats = fs.statSync(filePath);
            const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
            
            console.log(`  ✅ ${file} (${sizeInMB} MB)`);
        });
        
        console.log(`\n📂 文件位置: ${this.optimizedFinalDir}`);
    }

    /**
     * 主清理流程
     */
    clean() {
        try {
            console.log('🧹 开始清理旧DMG文件...\n');
            
            this.cleanDistDirectory();
            this.cleanReleaseDirectory();
            this.showFinalFiles();
            
            console.log('\n' + '='.repeat(60));
            this.log('🎉 DMG文件清理完成！', 'success');
            console.log('='.repeat(60));
            
            console.log('\n💡 现在只保留了优化后的DMG文件:');
            console.log('  - 小梅花AI智能客服-1.0.3-M芯片版本.dmg');
            console.log('  - 小梅花AI智能客服-1.0.3-intel版本.dmg');
            
            console.log('\n🚀 这些DMG文件具有以下优化特性:');
            console.log('  - 简化内容：只包含app、Applications链接、安装教程图片');
            console.log('  - 统一窗口大小和布局');
            console.log('  - 使用final版本技术设置（Bundle ID修改、特殊签名）');
            console.log('  - 规范命名：M芯片版本和intel版本');
            console.log('  - 安装教程图片居中显示在上方');
            console.log('  - 彻底解决macOS兼容性问题');
            
            return true;
            
        } catch (error) {
            this.log(`清理失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        }
    }
}

// 主程序入口
function main() {
    const cleaner = new DMGCleaner();
    const success = cleaner.clean();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = DMGCleaner;
