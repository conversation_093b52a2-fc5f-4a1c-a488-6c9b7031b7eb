const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('=== macOS DMG 打包脚本 ===\n');

// 检查构建产物
function checkBuildArtifacts() {
  console.log('1. 检查构建产物...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  if (!fs.existsSync(distDir)) {
    throw new Error('dist目录不存在，请先运行构建命令');
  }
  
  const files = fs.readdirSync(distDir);
  const dmgFiles = files.filter(file => file.endsWith('.dmg'));
  
  if (dmgFiles.length === 0) {
    throw new Error('未找到macOS安装包(.dmg文件)');
  }
  
  console.log('✓ 找到macOS安装包:');
  dmgFiles.forEach(file => {
    const filePath = path.join(distDir, file);
    const stats = fs.statSync(filePath);
    const sizeInMB = Math.round(stats.size / 1024 / 1024);
    console.log(`  - ${file} (${sizeInMB}MB)`);
  });
  
  return dmgFiles;
}

// 创建发布目录
function createReleaseDir() {
  console.log('\n2. 创建发布目录...');
  
  const releaseDir = path.join(__dirname, '..', 'release', 'macos');
  if (!fs.existsSync(releaseDir)) {
    fs.mkdirSync(releaseDir, { recursive: true });
    console.log('✓ 创建发布目录:', releaseDir);
  } else {
    console.log('✓ 发布目录已存在:', releaseDir);
  }
  
  return releaseDir;
}

// 复制文件到发布目录
function copyToRelease(dmgFiles, releaseDir) {
  console.log('\n3. 复制文件到发布目录...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  
  dmgFiles.forEach(file => {
    const sourcePath = path.join(distDir, file);
    const destPath = path.join(releaseDir, file);
    
    fs.copyFileSync(sourcePath, destPath);
    console.log(`✓ 复制: ${file}`);
  });
  
  // 复制相关的blockmap文件
  dmgFiles.forEach(file => {
    const blockmapFile = file + '.blockmap';
    const sourcePath = path.join(distDir, blockmapFile);
    const destPath = path.join(releaseDir, blockmapFile);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✓ 复制: ${blockmapFile}`);
    }
  });
}

// 生成安装说明
function generateInstallGuide(releaseDir) {
  console.log('\n4. 生成安装说明...');
  
  const installGuide = `# 小梅花AI智能客服 - macOS安装指南

## 系统要求
- macOS 10.15 (Catalina) 或更高版本
- Intel 或 Apple Silicon (M1/M2) 处理器
- 至少 200MB 可用磁盘空间

## 安装步骤
1. 双击打开 .dmg 文件
2. 将应用程序拖拽到 Applications 文件夹
3. 在 Launchpad 或 Applications 文件夹中找到应用
4. 首次运行时，可能需要在"系统偏好设置 > 安全性与隐私"中允许运行

## 权限说明
应用可能需要以下权限：
- 网络访问：用于API通信
- 文件访问：用于保存配置和数据
- 辅助功能：用于自动化功能（可选）

## 功能特性
- AI智能客服
- 多店铺管理
- 自动化脚本
- 数据同步
- 支持 Intel 和 Apple Silicon

## 卸载方法
1. 在 Applications 文件夹中找到应用
2. 将应用拖拽到废纸篓
3. 清空废纸篓

## 技术支持
如有问题，请联系技术支持团队。

---
构建时间: ${new Date().toLocaleString('zh-CN')}
版本: 1.0.0
`;
  
  const guidePath = path.join(releaseDir, 'macOS安装说明.md');
  fs.writeFileSync(guidePath, installGuide, 'utf8');
  console.log('✓ 生成安装说明:', guidePath);
}

// 验证DMG文件
function verifyDMG(dmgFiles, releaseDir) {
  console.log('\n5. 验证DMG文件...');
  
  dmgFiles.forEach(file => {
    const filePath = path.join(releaseDir, file);
    
    try {
      // 在macOS上验证DMG文件
      if (process.platform === 'darwin') {
        execSync(`hdiutil verify "${filePath}"`, { stdio: 'pipe' });
        console.log(`✓ ${file} 验证通过`);
      } else {
        console.log(`⚠ 非macOS系统，跳过 ${file} 的验证`);
      }
    } catch (error) {
      console.warn(`⚠ ${file} 验证失败，但文件可能仍然可用`);
    }
  });
}

// 主函数
function main() {
  try {
    const dmgFiles = checkBuildArtifacts();
    const releaseDir = createReleaseDir();
    copyToRelease(dmgFiles, releaseDir);
    generateInstallGuide(releaseDir);
    verifyDMG(dmgFiles, releaseDir);
    
    console.log('\n=== macOS DMG 打包完成 ===');
    console.log('✓ 安装包已准备就绪');
    console.log(`📁 发布目录: ${releaseDir}`);
    console.log('\n可分发的文件:');
    
    const files = fs.readdirSync(releaseDir);
    files.forEach(file => {
      const filePath = path.join(releaseDir, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile()) {
        const sizeInMB = Math.round(stats.size / 1024 / 1024);
        console.log(`  - ${file} ${sizeInMB > 0 ? `(${sizeInMB}MB)` : ''}`);
      }
    });
    
    console.log('\n📝 注意事项:');
    console.log('- Intel版本适用于Intel处理器的Mac');
    console.log('- ARM64版本适用于Apple Silicon (M1/M2)的Mac');
    console.log('- 用户可以根据自己的Mac型号选择对应版本');
    
  } catch (error) {
    console.error('\n✗ 打包失败:', error.message);
    process.exit(1);
  }
}

main();
