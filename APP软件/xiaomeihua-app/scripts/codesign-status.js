#!/usr/bin/env node

/**
 * 代码签名状态检查脚本
 * 显示当前代码签名环境的完整状态
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class CodeSignStatus {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.configFile = path.join(this.projectRoot, '.codesign-config');
        this.envFile = path.join(this.projectRoot, '.env.codesign');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.releaseDir = path.join(this.projectRoot, 'release');
    }

    /**
     * 显示标题
     */
    showHeader() {
        console.log('🍎 小梅花AI智能客服 - 代码签名状态检查');
        console.log('='.repeat(60));
        console.log('');
    }

    /**
     * 检查系统环境
     */
    checkSystemEnvironment() {
        console.log('🔍 系统环境检查');
        console.log('-'.repeat(30));
        
        // 检查操作系统
        if (process.platform === 'darwin') {
            console.log('✅ 操作系统: macOS');
            
            // 获取macOS版本
            try {
                const version = execSync('sw_vers -productVersion', { encoding: 'utf8' }).trim();
                console.log(`✅ 系统版本: macOS ${version}`);
            } catch (error) {
                console.log('⚠️  无法获取系统版本');
            }
        } else {
            console.log('❌ 操作系统: 非macOS系统');
            return false;
        }
        
        // 检查必要工具
        const tools = [
            { name: 'codesign', desc: '代码签名工具' },
            { name: 'security', desc: '钥匙串工具' },
            { name: 'hdiutil', desc: 'DMG工具' },
            { name: 'node', desc: 'Node.js' },
            { name: 'npm', desc: 'NPM包管理器' }
        ];
        
        for (const tool of tools) {
            try {
                execSync(`which ${tool.name}`, { stdio: 'pipe' });
                console.log(`✅ ${tool.desc}: 可用`);
            } catch (error) {
                console.log(`❌ ${tool.desc}: 不可用`);
            }
        }
        
        console.log('');
        return true;
    }

    /**
     * 检查证书状态
     */
    checkCertificateStatus() {
        console.log('📜 证书状态检查');
        console.log('-'.repeat(30));
        
        try {
            const result = execSync('security find-identity -v -p codesigning', { encoding: 'utf8' });
            const lines = result.split('\n').filter(line => line.trim() && !line.includes('valid identities found'));
            
            if (lines.length > 0) {
                console.log('✅ 找到以下代码签名证书:');
                lines.forEach((line, index) => {
                    const match = line.match(/"(.+)"/);
                    if (match) {
                        const certName = match[1];
                        const isAppleDev = certName.includes('Developer ID Application');
                        const status = isAppleDev ? '🍎 Apple Developer ID' : '🔧 自签名证书';
                        console.log(`   ${index + 1}. ${certName} (${status})`);
                    }
                });
            } else {
                console.log('❌ 未找到有效的代码签名证书');
            }
        } catch (error) {
            console.log('❌ 无法检查证书状态');
        }
        
        console.log('');
    }

    /**
     * 检查项目配置
     */
    checkProjectConfiguration() {
        console.log('⚙️  项目配置检查');
        console.log('-'.repeat(30));
        
        // 检查配置文件
        if (fs.existsSync(this.configFile)) {
            const config = fs.readFileSync(this.configFile, 'utf8');
            const match = config.match(/DEVELOPER_ID_CERT="(.+)"/);
            if (match) {
                console.log(`✅ 证书配置: ${match[1]}`);
            } else {
                console.log('⚠️  证书配置格式错误');
            }
        } else {
            console.log('❌ 未找到证书配置文件 (.codesign-config)');
        }
        
        // 检查环境配置
        if (fs.existsSync(this.envFile)) {
            console.log('✅ 环境配置文件存在 (.env.codesign)');
        } else {
            console.log('⚠️  环境配置文件不存在');
        }
        
        // 检查package.json配置
        const packagePath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packagePath)) {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            const buildConfig = packageJson.build;
            
            if (buildConfig && buildConfig.mac) {
                const macConfig = buildConfig.mac;
                console.log('✅ Electron构建配置:');
                console.log(`   - 强化运行时: ${macConfig.hardenedRuntime ? '启用' : '禁用'}`);
                console.log(`   - 身份验证: ${macConfig.identity || '未设置'}`);
                console.log(`   - 权限文件: ${macConfig.entitlements || '未设置'}`);
            } else {
                console.log('⚠️  缺少macOS构建配置');
            }
        }
        
        console.log('');
    }

    /**
     * 检查构建文件
     */
    checkBuildFiles() {
        console.log('📦 构建文件检查');
        console.log('-'.repeat(30));
        
        // 检查dist目录
        if (fs.existsSync(this.distDir)) {
            const distFiles = fs.readdirSync(this.distDir);
            const appFiles = distFiles.filter(f => f.endsWith('.app'));
            const dmgFiles = distFiles.filter(f => f.endsWith('.dmg'));
            
            console.log(`✅ 构建目录存在: ${distFiles.length} 个文件`);
            if (appFiles.length > 0) {
                console.log(`   - 应用文件: ${appFiles.length} 个`);
            }
            if (dmgFiles.length > 0) {
                console.log(`   - DMG文件: ${dmgFiles.length} 个`);
            }
        } else {
            console.log('❌ 构建目录不存在 (dist/)');
        }
        
        // 检查release目录
        if (fs.existsSync(this.releaseDir)) {
            const releaseFiles = fs.readdirSync(this.releaseDir);
            const dmgFiles = releaseFiles.filter(f => f.endsWith('.dmg'));
            
            console.log(`✅ 发布目录存在: ${releaseFiles.length} 个文件`);
            if (dmgFiles.length > 0) {
                console.log(`   - 发布DMG: ${dmgFiles.length} 个`);
                dmgFiles.forEach(file => {
                    const filePath = path.join(this.releaseDir, file);
                    const stats = fs.statSync(filePath);
                    const size = (stats.size / 1024 / 1024).toFixed(1);
                    console.log(`     • ${file} (${size}MB)`);
                });
            }
        } else {
            console.log('❌ 发布目录不存在 (release/)');
        }
        
        console.log('');
    }

    /**
     * 检查可用脚本
     */
    checkAvailableScripts() {
        console.log('🛠️  可用脚本检查');
        console.log('-'.repeat(30));
        
        const packagePath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packagePath)) {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            const scripts = packageJson.scripts || {};
            
            const codeSignScripts = Object.keys(scripts).filter(key => 
                key.includes('codesign') || 
                key.includes('sign') || 
                key.includes('setup') ||
                key.includes('verify') ||
                key.includes('auto-sign')
            );
            
            if (codeSignScripts.length > 0) {
                console.log('✅ 代码签名相关脚本:');
                codeSignScripts.forEach(script => {
                    console.log(`   • npm run ${script}`);
                });
            } else {
                console.log('⚠️  未找到代码签名相关脚本');
            }
        }
        
        console.log('');
    }

    /**
     * 显示建议操作
     */
    showRecommendations() {
        console.log('💡 建议操作');
        console.log('-'.repeat(30));
        
        // 检查是否有证书
        let hasCert = false;
        try {
            const result = execSync('security find-identity -v -p codesigning', { encoding: 'utf8' });
            hasCert = result.includes('Developer ID Application') || result.includes('valid identities found');
        } catch (error) {
            // 忽略错误
        }
        
        if (!hasCert) {
            console.log('🔴 优先级高:');
            console.log('   1. 申请Apple Developer ID证书');
            console.log('      命令: node scripts/certificate-helper.js');
            console.log('   2. 设置代码签名环境');
            console.log('      命令: npm run setup:codesign');
        } else {
            console.log('🟢 证书已配置，可以进行以下操作:');
            console.log('   1. 构建签名版本');
            console.log('      命令: npm run build:signed');
            console.log('   2. 验证代码签名');
            console.log('      命令: npm run verify:codesign');
            console.log('   3. 自动打包发布');
            console.log('      命令: npm run auto-sign-package');
        }
        
        console.log('');
        console.log('🔵 其他有用命令:');
        console.log('   • 查看完整指南: open docs/完整代码签名指南.md');
        console.log('   • 无签名构建: npm run auto-sign-package -- --no-sign');
        console.log('   • 详细输出: npm run auto-sign-package -- --verbose');
        
        console.log('');
    }

    /**
     * 显示摘要
     */
    showSummary() {
        console.log('📊 状态摘要');
        console.log('-'.repeat(30));
        
        const status = {
            system: process.platform === 'darwin',
            tools: true, // 简化检查
            certificate: false,
            config: fs.existsSync(this.configFile),
            builds: fs.existsSync(this.distDir)
        };
        
        // 检查证书
        try {
            const result = execSync('security find-identity -v -p codesigning', { encoding: 'utf8' });
            status.certificate = result.includes('Developer ID Application') || 
                                result.includes('valid identities found');
        } catch (error) {
            // 忽略错误
        }
        
        const readyItems = Object.values(status).filter(Boolean).length;
        const totalItems = Object.keys(status).length;
        const percentage = Math.round((readyItems / totalItems) * 100);
        
        console.log(`整体完成度: ${readyItems}/${totalItems} (${percentage}%)`);
        console.log('');
        
        if (percentage === 100) {
            console.log('🎉 恭喜！代码签名环境已完全配置');
            console.log('   可以开始构建签名版本的应用程序');
        } else if (percentage >= 60) {
            console.log('⚡ 环境基本就绪，还需要完成几个步骤');
        } else {
            console.log('🚧 需要完成更多配置才能使用代码签名');
        }
    }

    /**
     * 主函数
     */
    run() {
        this.showHeader();
        
        if (!this.checkSystemEnvironment()) {
            console.log('❌ 系统环境不支持，请在macOS上运行此脚本');
            return;
        }
        
        this.checkCertificateStatus();
        this.checkProjectConfiguration();
        this.checkBuildFiles();
        this.checkAvailableScripts();
        this.showRecommendations();
        this.showSummary();
        
        console.log('');
        console.log('📞 需要帮助？查看文档或联系技术支持');
        console.log('='.repeat(60));
    }
}

// 运行状态检查
if (require.main === module) {
    const status = new CodeSignStatus();
    status.run();
}

module.exports = CodeSignStatus;
