#!/usr/bin/env node

/**
 * 优化DMG安装包构建器 - 最终版本
 * 
 * 功能：
 * 1. 简化DMG内容：只包含app、Applications链接、安装教程图片
 * 2. 统一DMG窗口大小和布局
 * 3. 使用final版本的技术设置（Bundle ID修改、特殊签名）
 * 4. 规范命名：M芯片版本和intel版本
 * 5. 安装教程图片居中显示在上方
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class OptimizedDMGBuilder {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.tempDir = path.join(this.projectRoot, 'temp-dmg-final');
        this.finalDir = this.distDir; // 直接存放在dist文件夹
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        this.targetArch = args[0] || 'arm64';
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        // 生成新的Bundle ID避免冲突
        this.newBundleId = `cn.xiaomeihuakefu.app.v${this.version.replace(/\./g, '')}`;
        
        // 根据架构确定DMG名称
        this.dmgName = this.getDMGName();
        
        this.log('🚀 优化DMG安装包构建器 - 最终版本');
        this.log(`目标架构: ${this.targetArch}`);
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
        this.log(`新Bundle ID: ${this.newBundleId}`);
        this.log(`DMG名称: ${this.dmgName}`);
    }

    /**
     * 根据架构获取DMG名称
     */
    getDMGName() {
        switch (this.targetArch) {
            case 'arm64':
                return `${this.productName}-${this.version}-M芯片版本.dmg`;
            case 'x64':
                return `${this.productName}-${this.version}-intel版本.dmg`;
            case 'universal':
                // universal版本不再需要，但保留兼容性
                return `${this.productName}-${this.version}-通用版本.dmg`;
            default:
                throw new Error(`不支持的架构: ${this.targetArch}`);
        }
    }

    /**
     * 获取DMG卷标名称（与文件名一致，但不包含.dmg扩展名）
     */
    getDMGVolumeLabel() {
        return this.dmgName.replace('.dmg', '');
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            final: '\x1b[95m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄',
            final: '🚀'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            this.log(`执行命令: ${command}`, 'info');
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令执行失败但忽略错误: ${error.message}`, 'warning');
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 步骤1: 环境准备
     */
    prepareEnvironment() {
        this.log('准备构建环境...', 'final');
        
        // 清理临时目录
        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
        }
        fs.mkdirSync(this.tempDir, { recursive: true });
        
        // 确保dist目录存在
        if (!fs.existsSync(this.finalDir)) {
            fs.mkdirSync(this.finalDir, { recursive: true });
        }
        
        this.log('环境准备完成', 'success');
    }

    /**
     * 步骤2: 查找现有DMG文件
     */
    findExistingDMG() {
        this.log('查找现有DMG文件...', 'final');
        
        // 查找现有的DMG文件
        const dmgPattern = `${this.productName}-${this.version}-${this.targetArch}.dmg`;
        const dmgPath = path.join(this.distDir, dmgPattern);
        
        if (fs.existsSync(dmgPath)) {
            this.log(`找到现有DMG文件: ${dmgPattern}`, 'success');
            return dmgPath;
        }
        
        // 如果没有找到，尝试查找其他版本
        if (fs.existsSync(this.distDir)) {
            const files = fs.readdirSync(this.distDir);
            const dmgFiles = files.filter(file =>
                file.endsWith('.dmg') &&
                file.includes(this.targetArch) &&
                file.includes(this.productName)
            );

            if (dmgFiles.length > 0) {
                const foundDMG = dmgFiles[0];
                const foundPath = path.join(this.distDir, foundDMG);
                this.log(`找到DMG文件: ${foundDMG}`, 'success');
                return foundPath;
            }
        }

        // 如果没有找到，尝试构建
        this.log(`未找到${this.targetArch}架构的DMG文件，尝试构建...`, 'warning');
        this.buildBaseDMG();

        // 再次查找
        if (fs.existsSync(dmgPath)) {
            this.log(`构建并找到DMG文件: ${dmgPattern}`, 'success');
            return dmgPath;
        }

        throw new Error(`无法找到或构建${this.targetArch}架构的DMG文件`);
    }

    /**
     * 构建基础DMG文件
     */
    buildBaseDMG() {
        this.log(`构建${this.targetArch}架构的基础DMG...`, 'step');

        try {
            const buildCmd = `npx electron-builder --mac --${this.targetArch}`;
            this.exec(buildCmd);
            this.log('基础DMG构建完成', 'success');
        } catch (error) {
            this.log(`基础DMG构建失败: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 步骤3: 提取并优化应用程序
     */
    extractAndOptimizeApp(dmgPath) {
        this.log('提取并优化应用程序...', 'final');
        
        // 挂载DMG
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();
        
        try {
            // 复制应用程序
            const appName = `${this.productName}.app`;
            const sourceAppPath = path.join(mountPoint, appName);
            const tempAppPath = path.join(this.tempDir, appName);
            
            this.exec(`cp -R "${sourceAppPath}" "${tempAppPath}"`);
            
            // 应用final版本的优化
            this.applyFinalOptimizations(tempAppPath);
            
            return tempAppPath;
            
        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 应用final版本的优化
     */
    applyFinalOptimizations(appPath) {
        this.log('应用final版本优化...', 'final');
        
        // 1. 完全移除所有签名和扩展属性
        this.exec(`codesign --remove-signature "${appPath}"`, { ignoreError: true });
        this.exec(`xattr -cr "${appPath}"`);
        
        // 2. 修改Bundle ID避免冲突
        this.modifyBundleId(appPath);
        
        // 3. 修复权限
        this.exec(`chmod -R 755 "${appPath}"`);
        this.exec(`chmod +x "${appPath}/Contents/MacOS/"*`);
        
        // 4. 应用特殊签名
        this.applySpecialSignature(appPath);
        
        this.log('final版本优化完成', 'success');
    }

    /**
     * 修改Bundle ID
     */
    modifyBundleId(appPath) {
        this.log('修改Bundle ID避免冲突...', 'final');
        
        const infoPlistPath = path.join(appPath, 'Contents/Info.plist');
        
        if (fs.existsSync(infoPlistPath)) {
            // 读取Info.plist
            let infoPlistContent = fs.readFileSync(infoPlistPath, 'utf8');
            
            // 替换Bundle ID
            const oldBundleId = 'cn.xiaomeihuakefu.app';
            infoPlistContent = infoPlistContent.replace(
                new RegExp(oldBundleId, 'g'),
                this.newBundleId
            );
            
            // 写回文件
            fs.writeFileSync(infoPlistPath, infoPlistContent);
            
            this.log(`Bundle ID已更新: ${oldBundleId} -> ${this.newBundleId}`, 'success');
        }
    }

    /**
     * 应用特殊签名
     */
    applySpecialSignature(appPath) {
        this.log('应用特殊签名...', 'final');
        
        // 签名所有组件
        const frameworksDir = path.join(appPath, 'Contents/Frameworks');
        if (fs.existsSync(frameworksDir)) {
            this.exec(`find "${frameworksDir}" -name "*.framework" -exec codesign --force --sign - {} \\; 2>/dev/null || true`, { ignoreError: true });
            this.exec(`find "${frameworksDir}" -name "*.dylib" -exec codesign --force --sign - {} \\; 2>/dev/null || true`, { ignoreError: true });
        }
        
        // 签名Helper应用程序
        const helperApps = [
            'Contents/Frameworks/小梅花AI智能客服 Helper.app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (GPU).app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (Plugin).app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (Renderer).app'
        ];
        
        helperApps.forEach(helperPath => {
            const fullHelperPath = path.join(appPath, helperPath);
            if (fs.existsSync(fullHelperPath)) {
                this.exec(`codesign --force --sign - "${fullHelperPath}"`, { ignoreError: true });
            }
        });
        
        // 最终签名主应用程序
        this.exec(`codesign --force --deep --sign - --preserve-metadata=identifier,entitlements,flags,runtime "${appPath}"`);
        
        // 验证签名
        this.exec(`codesign --verify --verbose "${appPath}"`);
        
        this.log('特殊签名应用完成', 'success');
    }

    /**
     * 步骤4: 创建优化的DMG内容
     */
    createOptimizedDMGContent(appPath) {
        this.log('创建优化的DMG内容...', 'final');

        // 创建DMG内容目录
        const dmgContentDir = path.join(this.tempDir, 'dmg-content');
        fs.mkdirSync(dmgContentDir, { recursive: true });

        // 1. 复制应用程序
        const appName = `${this.productName}.app`;
        const dmgAppPath = path.join(dmgContentDir, appName);
        this.exec(`cp -R "${appPath}" "${dmgAppPath}"`);

        // 2. 创建Applications链接
        this.exec(`ln -s /Applications "${path.join(dmgContentDir, 'Applications')}"`);

        // 3. 复制安装教程图片
        const tutorialImageSrc = path.join(this.projectRoot, 'build', 'Mac电脑安装教程.png');
        const tutorialImageDst = path.join(dmgContentDir, 'Mac电脑安装教程.png');

        if (fs.existsSync(tutorialImageSrc)) {
            this.exec(`cp "${tutorialImageSrc}" "${tutorialImageDst}"`);
            this.log('安装教程图片已添加', 'success');
        } else {
            this.log('未找到安装教程图片，跳过', 'warning');
        }

        // 4. 最终清理DMG中的应用程序
        this.exec(`xattr -cr "${dmgAppPath}"`);
        this.exec(`chmod 755 "${dmgAppPath}"`);

        this.log('DMG内容创建完成', 'success');
        return dmgContentDir;
    }

    /**
     * 步骤5: 创建DMG背景和布局脚本
     */
    createDMGLayoutScript(dmgContentDir) {
        this.log('创建DMG布局脚本...', 'final');

        // DMG窗口设置（640x480像素）
        const windowWidth = 640;
        const windowHeight = 480;

        // 创建AppleScript来设置DMG窗口布局 - 640x480窗口
        const volumeName = this.getDMGVolumeLabel();
        const layoutScript = `
tell application "Finder"
    tell disk "${volumeName}"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set sidebar width of container window to 0

        -- 获取屏幕尺寸并计算居中位置
        tell application "Finder"
            set screenBounds to bounds of window of desktop
            set screenWidth to item 3 of screenBounds
            set screenHeight to item 4 of screenBounds
        end tell

        -- 计算640x480窗口的居中位置
        set windowWidth to 640
        set windowHeight to 480
        set leftPos to (screenWidth - windowWidth) / 2
        set topPos to (screenHeight - windowHeight) / 2
        set rightPos to leftPos + windowWidth
        set bottomPos to topPos + windowHeight

        -- 强制设置640x480居中窗口（多次设置确保生效）
        set the bounds of container window to {leftPos, topPos, rightPos, bottomPos}
        delay 1
        set the bounds of container window to {leftPos, topPos, rightPos, bottomPos}

        set viewOptions to the icon view options of container window
        set arrangement of viewOptions to not arranged
        set icon size of viewOptions to 100
        set text size of viewOptions to 12
        set label position of viewOptions to bottom

        -- 640x480窗口布局 - 优化位置
        -- 安装教程图片在上方居中（适配640x480窗口）
        set position of item "Mac电脑安装教程.png" to {320, 80}

        -- 应用程序图标在左下方（适配640x480窗口）
        set position of item "${this.productName}.app" to {160, 280}

        -- Applications文件夹在右下方（适配640x480窗口）
        set position of item "Applications" to {480, 280}

        -- 多次强制设置窗口大小和刷新
        set the bounds of container window to {leftPos, topPos, rightPos, bottomPos}
        update without registering applications
        delay 2
        close
        delay 1
        open
        set the bounds of container window to {leftPos, topPos, rightPos, bottomPos}
        delay 2
        set the bounds of container window to {leftPos, topPos, rightPos, bottomPos}
        close
    end tell
end tell
`;

        const scriptPath = path.join(this.tempDir, 'dmg-layout.applescript');
        fs.writeFileSync(scriptPath, layoutScript);

        this.log('DMG布局脚本已创建（640x480窗口）', 'success');
        return scriptPath;
    }

    /**
     * 步骤6: 创建最终DMG
     */
    createFinalDMG(dmgContentDir) {
        this.log('创建最终DMG...', 'final');

        // 生成最终DMG文件路径
        const finalDMGPath = path.join(this.finalDir, this.dmgName);

        // 删除旧文件（如果存在）
        if (fs.existsSync(finalDMGPath)) {
            this.log(`删除旧文件: ${this.dmgName}`, 'warning');
            fs.unlinkSync(finalDMGPath);
        }

        // 创建临时DMG - 使用完整的DMG名称作为卷标
        const tempDMGPath = path.join(this.tempDir, 'temp.dmg');
        const volumeName = this.getDMGVolumeLabel();
        this.exec(`hdiutil create -srcfolder "${dmgContentDir}" -format UDRW -volname "${volumeName}" "${tempDMGPath}"`);

        // 挂载临时DMG进行布局设置
        const mountResult = this.exec(`hdiutil attach "${tempDMGPath}" -readwrite -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();

        try {
            // 创建.background目录并复制背景图片（如果需要）
            const backgroundDir = path.join(mountPoint, '.background');
            if (!fs.existsSync(backgroundDir)) {
                fs.mkdirSync(backgroundDir);
            }

            // 设置窗口属性
            this.exec(`SetFile -a C "${mountPoint}"`, { ignoreError: true });

            // 运行AppleScript设置窗口布局（静默模式，不显示弹窗）
            const layoutScriptPath = this.createDMGLayoutScript(dmgContentDir);
            try {
                this.exec(`osascript "${layoutScriptPath}"`, { silent: true, ignoreError: true });
                this.log('AppleScript窗口布局设置完成（静默模式）', 'success');
            } catch (scriptError) {
                this.log(`AppleScript执行失败: ${scriptError.message}`, 'warning');
            }

            this.log('DMG布局设置完成', 'success');

        } finally {
            // 卸载临时DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }

        // 转换为最终的只读DMG
        this.exec(`hdiutil convert "${tempDMGPath}" -format UDZO -imagekey zlib-level=9 -o "${finalDMGPath}"`);

        // 清理临时DMG
        if (fs.existsSync(tempDMGPath)) {
            fs.unlinkSync(tempDMGPath);
        }

        this.log(`最终DMG已创建: ${finalDMGPath}`, 'success');
        return finalDMGPath;
    }

    /**
     * 步骤7: 验证最终DMG
     */
    verifyFinalDMG(dmgPath) {
        this.log('验证最终DMG...', 'final');

        // 验证DMG完整性
        this.exec(`hdiutil verify "${dmgPath}"`, { silent: true });
        this.log('✅ DMG完整性验证通过', 'success');

        // 挂载并检查内容
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();

        try {
            const appName = `${this.productName}.app`;
            const mountedAppPath = path.join(mountPoint, appName);

            // 检查应用程序存在
            if (!fs.existsSync(mountedAppPath)) {
                throw new Error('应用程序不存在');
            }

            // 检查Applications链接
            const applicationsPath = path.join(mountPoint, 'Applications');
            if (!fs.existsSync(applicationsPath)) {
                this.log('⚠️ Applications链接缺失', 'warning');
            } else {
                this.log('✅ Applications链接正常', 'success');
            }

            // 检查安装教程图片
            const tutorialPath = path.join(mountPoint, 'Mac电脑安装教程.png');
            if (!fs.existsSync(tutorialPath)) {
                this.log('⚠️ 安装教程图片缺失', 'warning');
            } else {
                this.log('✅ 安装教程图片存在', 'success');
            }

            // 检查Bundle ID是否已更新
            const infoPlistPath = path.join(mountedAppPath, 'Contents/Info.plist');
            if (fs.existsSync(infoPlistPath)) {
                const infoPlistContent = fs.readFileSync(infoPlistPath, 'utf8');
                if (infoPlistContent.includes(this.newBundleId)) {
                    this.log('✅ Bundle ID已更新', 'success');
                } else {
                    this.log('⚠️ Bundle ID未更新', 'warning');
                }
            }

            // 验证签名
            try {
                this.exec(`codesign --verify "${mountedAppPath}"`, { silent: true });
                this.log('✅ 签名验证通过', 'success');
            } catch (error) {
                this.log('⚠️ 签名验证失败', 'warning');
            }

            this.log('🚀 最终DMG验证完成！', 'final');
            return true;

        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 清理临时文件
     */
    cleanup() {
        this.log('清理临时文件...', 'step');

        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
            this.log('临时文件已清理', 'success');
        }
    }

    /**
     * 显示所有架构的构建结果
     */
    showAllResults(results) {
        console.log('\n' + '='.repeat(60));
        console.log('🚀 优化DMG安装包构建完成！');
        console.log('='.repeat(60));

        const successResults = results.filter(r => r.success);
        const failedResults = results.filter(r => !r.success);

        if (successResults.length > 0) {
            console.log(`\n✅ 成功构建 ${successResults.length} 个版本:`);
            successResults.forEach(result => {
                const archName = result.arch === 'arm64' ? 'M芯片版本' : 'Intel版本';
                console.log(`  📦 ${archName}: ${path.basename(result.path)}`);
            });
        }

        if (failedResults.length > 0) {
            console.log(`\n❌ 构建失败 ${failedResults.length} 个版本:`);
            failedResults.forEach(result => {
                const archName = result.arch === 'arm64' ? 'M芯片版本' : 'Intel版本';
                console.log(`  ⚠️ ${archName}: ${result.error}`);
            });
        }

        if (successResults.length > 0) {
            console.log(`\n🚀 优化特性:`);
            console.log(`  - 简化DMG内容：只包含app、Applications链接、安装教程图片`);
            console.log(`  - DMG窗口（640x480），屏幕居中显示`);
            console.log(`  - 使用final版本技术设置（Bundle ID修改、特殊签名）`);
            console.log(`  - 同时支持M芯片和Intel版本`);
            console.log(`  - 经典布局：安装教程在上方，app和Applications在下方`);
            console.log(`  - 彻底解决macOS兼容性问题`);
            console.log(`  - DMG文件直接存放在dist文件夹`);

            console.log(`\n💡 用户安装指南:`);
            console.log(`  1. 双击打开对应架构的DMG文件`);
            console.log(`  2. 查看上方的"Mac电脑安装教程.png"`);
            console.log(`  3. 将应用程序拖拽到Applications文件夹`);
            console.log(`  4. 直接启动应用程序（无需额外修复）`);

            console.log(`\n📁 文件位置: ${this.finalDir}`);

            if (process.platform === 'darwin') {
                console.log(`\n🔍 快速查看:`);
                console.log(`  open "${this.finalDir}"`);
            }
        }
    }

    /**
     * 显示单个架构的结果
     */
    showResults(dmgPath, success) {
        console.log('\n' + '='.repeat(60));
        if (success) {
            this.log('🚀 优化DMG安装包构建完成！', 'final');
        } else {
            this.log('⚠️ 构建可能不完全', 'warning');
        }
        console.log('='.repeat(60));

        console.log(`\n📋 构建信息:`);
        console.log(`  产品: ${this.productName}`);
        console.log(`  版本: ${this.version}`);
        console.log(`  架构: ${this.targetArch}`);
        console.log(`  新Bundle ID: ${this.newBundleId}`);
        console.log(`  DMG文件: ${path.basename(dmgPath)}`);

        if (success) {
            console.log(`\n🚀 优化特性:`);
            console.log(`  - 简化DMG内容：只包含app、Applications链接、安装教程图片`);
            console.log(`  - DMG窗口（640x480），屏幕居中显示`);
            console.log(`  - 使用final版本技术设置（Bundle ID修改、特殊签名）`);
            console.log(`  - 规范命名：${this.targetArch === 'arm64' ? 'M芯片版本' : 'Intel版本'}`);
            console.log(`  - 经典布局：安装教程在上方，app和Applications在下方`);
            console.log(`  - 彻底解决macOS兼容性问题`);
            console.log(`  - DMG文件直接存放在dist文件夹`);
        }

        console.log(`\n💡 用户安装指南:`);
        console.log(`  1. 双击打开DMG文件`);
        console.log(`  2. 查看上方的"Mac电脑安装教程.png"`);
        console.log(`  3. 将应用程序拖拽到Applications文件夹`);
        console.log(`  4. 直接启动应用程序（无需额外修复）`);

        console.log(`\n📁 文件位置: ${this.finalDir}`);

        if (process.platform === 'darwin') {
            console.log(`\n🔍 快速查看:`);
            console.log(`  open "${this.finalDir}"`);
        }
    }

    /**
     * 构建单个架构的DMG
     */
    async buildSingleArch(arch) {
        this.log(`🚀 开始构建${arch === 'arm64' ? 'M芯片' : 'Intel'}版本...`, 'step');

        // 更新当前架构
        this.targetArch = arch;
        this.dmgName = this.getDMGName();

        // 执行构建步骤
        const dmgPath = this.findExistingDMG();
        const appPath = this.extractAndOptimizeApp(dmgPath);
        const dmgContentDir = this.createOptimizedDMGContent(appPath);
        const finalDMGPath = this.createFinalDMG(dmgContentDir);
        const success = this.verifyFinalDMG(finalDMGPath);

        this.log(`✅ ${arch === 'arm64' ? 'M芯片' : 'Intel'}版本构建完成`, 'success');
        return { success, path: finalDMGPath, arch };
    }

    /**
     * 主构建流程 - 构建所有架构
     */
    async build() {
        try {
            console.log('🚀 开始优化DMG安装包构建（M芯片 + Intel版本）...\n');

            // 准备环境
            this.prepareEnvironment();

            const results = [];
            const architectures = ['arm64', 'x64'];

            // 构建每个架构
            for (const arch of architectures) {
                try {
                    const result = await this.buildSingleArch(arch);
                    results.push(result);
                } catch (error) {
                    this.log(`${arch === 'arm64' ? 'M芯片' : 'Intel'}版本构建失败: ${error.message}`, 'error');
                    results.push({ success: false, arch, error: error.message });
                }

                // 清理临时文件，为下一个架构做准备
                this.cleanup();
                this.prepareEnvironment();
            }

            this.showAllResults(results);

            // 如果至少有一个成功，返回true
            return results.some(r => r.success);

        } catch (error) {
            this.log(`构建失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        } finally {
            this.cleanup();
        }
    }
}

// 主程序入口
async function main() {
    const builder = new OptimizedDMGBuilder();
    const success = await builder.build();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = OptimizedDMGBuilder;
