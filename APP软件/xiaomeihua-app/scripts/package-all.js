const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('=== 全平台打包脚本 ===\n');

// 检查构建产物
function checkBuildArtifacts() {
  console.log('1. 检查构建产物...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  if (!fs.existsSync(distDir)) {
    throw new Error('dist目录不存在，请先运行构建命令');
  }
  
  const files = fs.readdirSync(distDir);
  const exeFiles = files.filter(file => file.endsWith('.exe') && file.includes('Setup'));
  const dmgFiles = files.filter(file => file.endsWith('.dmg'));
  
  console.log('✓ 构建产物统计:');
  console.log(`  - Windows安装包: ${exeFiles.length}个`);
  console.log(`  - macOS安装包: ${dmgFiles.length}个`);
  
  return { exeFiles, dmgFiles };
}

// 创建发布目录结构
function createReleaseStructure() {
  console.log('\n2. 创建发布目录结构...');
  
  const releaseDir = path.join(__dirname, '..', 'release');
  const windowsDir = path.join(releaseDir, 'windows');
  const macosDir = path.join(releaseDir, 'macos');
  const allDir = path.join(releaseDir, 'all-platforms');
  
  [releaseDir, windowsDir, macosDir, allDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✓ 创建目录: ${path.basename(dir)}`);
    }
  });
  
  return { releaseDir, windowsDir, macosDir, allDir };
}

// 复制文件到各平台目录
function copyToPlatformDirs(artifacts, dirs) {
  console.log('\n3. 复制文件到各平台目录...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  const { exeFiles, dmgFiles } = artifacts;
  const { windowsDir, macosDir, allDir } = dirs;
  
  // 复制Windows文件
  exeFiles.forEach(file => {
    const sourcePath = path.join(distDir, file);
    const destPath = path.join(windowsDir, file);
    const allDestPath = path.join(allDir, file);
    
    fs.copyFileSync(sourcePath, destPath);
    fs.copyFileSync(sourcePath, allDestPath);
    console.log(`✓ 复制Windows: ${file}`);
    
    // 复制blockmap文件
    const blockmapFile = file + '.blockmap';
    const blockmapSource = path.join(distDir, blockmapFile);
    if (fs.existsSync(blockmapSource)) {
      fs.copyFileSync(blockmapSource, path.join(windowsDir, blockmapFile));
      fs.copyFileSync(blockmapSource, path.join(allDir, blockmapFile));
    }
  });
  
  // 复制macOS文件
  dmgFiles.forEach(file => {
    const sourcePath = path.join(distDir, file);
    const destPath = path.join(macosDir, file);
    const allDestPath = path.join(allDir, file);
    
    fs.copyFileSync(sourcePath, destPath);
    fs.copyFileSync(sourcePath, allDestPath);
    console.log(`✓ 复制macOS: ${file}`);
    
    // 复制blockmap文件
    const blockmapFile = file + '.blockmap';
    const blockmapSource = path.join(distDir, blockmapFile);
    if (fs.existsSync(blockmapSource)) {
      fs.copyFileSync(blockmapSource, path.join(macosDir, blockmapFile));
      fs.copyFileSync(blockmapSource, path.join(allDir, blockmapFile));
    }
  });
}

// 生成发布说明
function generateReleaseNotes(dirs, artifacts) {
  console.log('\n4. 生成发布说明...');
  
  const { releaseDir } = dirs;
  const { exeFiles, dmgFiles } = artifacts;
  
  const releaseNotes = `# 小梅花AI智能客服 v1.0.0 发布包

## 发布信息
- 版本: 1.0.0
- 构建时间: ${new Date().toLocaleString('zh-CN')}
- 构建平台: ${process.platform}
- Node.js版本: ${process.version}

## 包含的安装包

### Windows版本
${exeFiles.map(file => {
  const filePath = path.join(__dirname, '..', 'dist', file);
  const stats = fs.statSync(filePath);
  const sizeInMB = Math.round(stats.size / 1024 / 1024);
  return `- ${file} (${sizeInMB}MB)`;
}).join('\n')}

### macOS版本
${dmgFiles.map(file => {
  const filePath = path.join(__dirname, '..', 'dist', file);
  const stats = fs.statSync(filePath);
  const sizeInMB = Math.round(stats.size / 1024 / 1024);
  return `- ${file} (${sizeInMB}MB)`;
}).join('\n')}

## 系统要求

### Windows
- Windows 10 或更高版本
- 64位或32位系统
- 至少 200MB 可用磁盘空间

### macOS
- macOS 10.15 (Catalina) 或更高版本
- Intel 或 Apple Silicon (M1/M2) 处理器
- 至少 200MB 可用磁盘空间

## 功能特性
- ✅ AI智能客服
- ✅ 多店铺管理
- ✅ 自动化脚本
- ✅ 数据同步
- ✅ 跨平台支持
- ✅ 本地数据存储
- ✅ 安全认证

## 安装说明

### Windows用户
1. 下载对应的 .exe 安装包
2. 双击运行安装程序
3. 按照向导完成安装

### macOS用户
1. 下载对应的 .dmg 安装包
2. 双击打开DMG文件
3. 将应用拖拽到Applications文件夹

## 技术支持
如有问题，请联系技术支持团队。

---
© 2025 小梅花AI科技
`;
  
  const notesPath = path.join(releaseDir, 'README.md');
  fs.writeFileSync(notesPath, releaseNotes, 'utf8');
  console.log('✓ 生成发布说明:', notesPath);
}

// 生成版本信息文件
function generateVersionInfo(dirs) {
  console.log('\n5. 生成版本信息文件...');
  
  const { releaseDir } = dirs;
  
  const versionInfo = {
    version: '1.0.0',
    buildTime: new Date().toISOString(),
    buildPlatform: process.platform,
    nodeVersion: process.version,
    electronVersion: require('../package.json').devDependencies.electron,
    features: [
      'AI智能客服',
      '多店铺管理',
      '自动化脚本',
      '数据同步',
      '跨平台支持'
    ],
    platforms: {
      windows: {
        supported: true,
        minVersion: 'Windows 10',
        architectures: ['x64', 'ia32']
      },
      macos: {
        supported: true,
        minVersion: 'macOS 10.15',
        architectures: ['x64', 'arm64']
      }
    }
  };
  
  const versionPath = path.join(releaseDir, 'version.json');
  fs.writeFileSync(versionPath, JSON.stringify(versionInfo, null, 2), 'utf8');
  console.log('✓ 生成版本信息:', versionPath);
}

// 生成发布摘要
function generateSummary(dirs, artifacts) {
  console.log('\n6. 生成发布摘要...');
  
  const { releaseDir } = dirs;
  const { exeFiles, dmgFiles } = artifacts;
  
  console.log('\n=== 全平台打包完成 ===');
  console.log('✓ 所有安装包已准备就绪');
  console.log(`📁 发布目录: ${releaseDir}`);
  
  console.log('\n📦 打包摘要:');
  console.log(`  Windows安装包: ${exeFiles.length}个`);
  console.log(`  macOS安装包: ${dmgFiles.length}个`);
  console.log(`  总计: ${exeFiles.length + dmgFiles.length}个安装包`);
  
  console.log('\n📁 目录结构:');
  console.log('  release/');
  console.log('  ├── windows/     # Windows安装包');
  console.log('  ├── macos/       # macOS安装包');
  console.log('  ├── all-platforms/ # 所有平台安装包');
  console.log('  ├── README.md    # 发布说明');
  console.log('  └── version.json # 版本信息');
  
  console.log('\n🚀 可以开始分发了！');
}

// 主函数
function main() {
  try {
    const artifacts = checkBuildArtifacts();
    const dirs = createReleaseStructure();
    copyToPlatformDirs(artifacts, dirs);
    generateReleaseNotes(dirs, artifacts);
    generateVersionInfo(dirs);
    generateSummary(dirs, artifacts);
    
  } catch (error) {
    console.error('\n✗ 打包失败:', error.message);
    process.exit(1);
  }
}

main();
