const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('=== Windows EXE 打包脚本 ===\n');

// 检查构建产物
function checkBuildArtifacts() {
  console.log('1. 检查构建产物...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  if (!fs.existsSync(distDir)) {
    throw new Error('dist目录不存在，请先运行构建命令');
  }
  
  const files = fs.readdirSync(distDir);
  const exeFiles = files.filter(file => file.endsWith('.exe') && file.includes('Setup'));
  
  if (exeFiles.length === 0) {
    throw new Error('未找到Windows安装包(.exe文件)');
  }
  
  console.log('✓ 找到Windows安装包:');
  exeFiles.forEach(file => {
    const filePath = path.join(distDir, file);
    const stats = fs.statSync(filePath);
    const sizeInMB = Math.round(stats.size / 1024 / 1024);
    console.log(`  - ${file} (${sizeInMB}MB)`);
  });
  
  return exeFiles;
}

// 创建发布目录
function createReleaseDir() {
  console.log('\n2. 创建发布目录...');
  
  const releaseDir = path.join(__dirname, '..', 'release', 'windows');
  if (!fs.existsSync(releaseDir)) {
    fs.mkdirSync(releaseDir, { recursive: true });
    console.log('✓ 创建发布目录:', releaseDir);
  } else {
    console.log('✓ 发布目录已存在:', releaseDir);
  }
  
  return releaseDir;
}

// 复制文件到发布目录
function copyToRelease(exeFiles, releaseDir) {
  console.log('\n3. 复制文件到发布目录...');
  
  const distDir = path.join(__dirname, '..', 'dist');
  
  exeFiles.forEach(file => {
    const sourcePath = path.join(distDir, file);
    const destPath = path.join(releaseDir, file);
    
    fs.copyFileSync(sourcePath, destPath);
    console.log(`✓ 复制: ${file}`);
  });
  
  // 复制相关的blockmap文件
  exeFiles.forEach(file => {
    const blockmapFile = file + '.blockmap';
    const sourcePath = path.join(distDir, blockmapFile);
    const destPath = path.join(releaseDir, blockmapFile);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✓ 复制: ${blockmapFile}`);
    }
  });
}

// 生成安装说明
function generateInstallGuide(releaseDir) {
  console.log('\n4. 生成安装说明...');
  
  const installGuide = `# 小梅花AI智能客服 - Windows安装指南

## 系统要求
- Windows 10 或更高版本
- 64位或32位系统
- 至少 200MB 可用磁盘空间

## 安装步骤
1. 双击运行安装包
2. 选择安装路径（默认安装到D盘）
3. 等待安装完成
4. 启动应用程序

## 功能特性
- AI智能客服
- 多店铺管理
- 自动化脚本
- 数据同步

## 技术支持
如有问题，请联系技术支持团队。

---
构建时间: ${new Date().toLocaleString('zh-CN')}
版本: 1.0.0
`;
  
  const guidePath = path.join(releaseDir, 'Windows安装说明.md');
  fs.writeFileSync(guidePath, installGuide, 'utf8');
  console.log('✓ 生成安装说明:', guidePath);
}

// 主函数
function main() {
  try {
    const exeFiles = checkBuildArtifacts();
    const releaseDir = createReleaseDir();
    copyToRelease(exeFiles, releaseDir);
    generateInstallGuide(releaseDir);
    
    console.log('\n=== Windows EXE 打包完成 ===');
    console.log('✓ 安装包已准备就绪');
    console.log(`📁 发布目录: ${releaseDir}`);
    console.log('\n可分发的文件:');
    
    const files = fs.readdirSync(releaseDir);
    files.forEach(file => {
      const filePath = path.join(releaseDir, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile()) {
        const sizeInMB = Math.round(stats.size / 1024 / 1024);
        console.log(`  - ${file} ${sizeInMB > 0 ? `(${sizeInMB}MB)` : ''}`);
      }
    });
    
  } catch (error) {
    console.error('\n✗ 打包失败:', error.message);
    process.exit(1);
  }
}

main();
