#!/usr/bin/env node

/**
 * 终极DMG测试脚本
 * 测试终极版本是否彻底解决了"已损坏"问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class UltimateDMGTester {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.ultimateDir = path.join(this.projectRoot, 'release', 'ultimate');
        this.fixedDir = path.join(this.projectRoot, 'release', 'fixed');
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        this.log('🔥 终极DMG测试工具');
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            ultimate: '\x1b[95m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄',
            ultimate: '🔥'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 查找所有DMG文件
     */
    findAllDMGFiles() {
        this.log('查找所有DMG文件...', 'step');
        
        const allDMGs = [];
        
        // 查找终极版本
        if (fs.existsSync(this.ultimateDir)) {
            const ultimateFiles = fs.readdirSync(this.ultimateDir)
                .filter(file => file.endsWith('-ULTIMATE.dmg'))
                .map(file => ({
                    file,
                    path: path.join(this.ultimateDir, file),
                    type: 'ULTIMATE',
                    architecture: this.extractArchitecture(file)
                }));
            allDMGs.push(...ultimateFiles);
        }
        
        // 查找修复版本
        if (fs.existsSync(this.fixedDir)) {
            const fixedFiles = fs.readdirSync(this.fixedDir)
                .filter(file => file.endsWith('-fixed.dmg'))
                .map(file => ({
                    file,
                    path: path.join(this.fixedDir, file),
                    type: 'FIXED',
                    architecture: this.extractArchitecture(file)
                }));
            allDMGs.push(...fixedFiles);
        }
        
        this.log(`找到 ${allDMGs.length} 个DMG文件:`);
        allDMGs.forEach(dmg => {
            const stats = fs.statSync(dmg.path);
            const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
            this.log(`  ${dmg.file} (${dmg.type}, ${dmg.architecture}, ${sizeMB} MB)`);
        });
        
        return allDMGs;
    }

    /**
     * 从文件名提取架构信息
     */
    extractArchitecture(filename) {
        if (filename.includes('x64')) return 'x64';
        if (filename.includes('arm64')) return 'arm64';
        if (filename.includes('universal')) return 'universal';
        return 'unknown';
    }

    /**
     * 测试单个DMG文件
     */
    testDMGFile(dmgInfo) {
        this.log(`测试DMG: ${dmgInfo.file}`, 'ultimate');
        
        const results = {
            ...dmgInfo,
            dmgValid: false,
            appExists: false,
            appSigned: false,
            sealedResources: false,
            infoPlistBound: false,
            signatureValid: false,
            noQuarantine: false,
            hasFixScript: false,
            hasGuide: false,
            score: 0,
            maxScore: 8,
            issues: [],
            status: 'unknown'
        };
        
        try {
            // 1. 验证DMG完整性
            this.exec(`hdiutil verify "${dmgInfo.path}"`, { silent: true });
            results.dmgValid = true;
            results.score++;
            this.log('✅ DMG完整性验证通过', 'success');
            
            // 2. 挂载DMG并检查内容
            const mountResult = this.exec(`hdiutil attach "${dmgInfo.path}" -readonly -nobrowse`, { silent: true });
            const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
            
            if (mountLines.length === 0) {
                throw new Error('无法挂载DMG');
            }
            
            const mountPoint = mountLines[0].split('\t').pop().trim();
            
            try {
                const appName = `${this.productName}.app`;
                const appPath = path.join(mountPoint, appName);
                
                // 3. 检查应用程序存在
                if (fs.existsSync(appPath)) {
                    results.appExists = true;
                    results.score++;
                    this.log('✅ 应用程序存在', 'success');
                } else {
                    results.issues.push('应用程序不存在');
                    throw new Error('应用程序不存在');
                }
                
                // 4. 检查应用程序签名
                const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
                results.appSigned = true;
                results.score++;
                
                // 5. 检查资源封装
                if (signInfo.includes('Sealed Resources version=')) {
                    results.sealedResources = true;
                    results.score++;
                    this.log('✅ 资源封装正常', 'success');
                } else {
                    results.issues.push('资源封装缺失');
                }
                
                // 6. 检查Info.plist绑定
                if (signInfo.includes('Info.plist entries=')) {
                    results.infoPlistBound = true;
                    results.score++;
                    this.log('✅ Info.plist绑定正常', 'success');
                } else {
                    results.issues.push('Info.plist未绑定');
                }
                
                // 7. 验证签名有效性
                try {
                    this.exec(`codesign --verify "${appPath}"`, { silent: true });
                    results.signatureValid = true;
                    results.score++;
                    this.log('✅ 签名验证通过', 'success');
                } catch (error) {
                    results.issues.push('签名验证失败');
                }
                
                // 8. 检查隔离属性
                const xattrResult = this.exec(`xattr "${appPath}" 2>/dev/null || echo "no attributes"`, { silent: true });
                if (!xattrResult.includes('com.apple.quarantine')) {
                    results.noQuarantine = true;
                    results.score++;
                    this.log('✅ 无隔离属性', 'success');
                } else {
                    results.issues.push('仍有隔离属性');
                }
                
                // 9. 检查修复脚本（终极版本特有）
                if (dmgInfo.type === 'ULTIMATE') {
                    const fixScriptPath = path.join(mountPoint, '终极修复.command');
                    if (fs.existsSync(fixScriptPath)) {
                        results.hasFixScript = true;
                        this.log('✅ 终极修复脚本存在', 'success');
                    }
                    
                    const guidePath = path.join(mountPoint, '安装指南.txt');
                    if (fs.existsSync(guidePath)) {
                        results.hasGuide = true;
                        this.log('✅ 安装指南存在', 'success');
                    }
                }
                
            } finally {
                // 卸载DMG
                this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
            }
            
        } catch (error) {
            results.issues.push(`测试失败: ${error.message}`);
            this.log(`❌ 测试失败: ${error.message}`, 'error');
        }
        
        // 确定状态
        const scorePercentage = (results.score / results.maxScore) * 100;
        if (scorePercentage >= 90) {
            results.status = 'excellent';
        } else if (scorePercentage >= 75) {
            results.status = 'good';
        } else if (scorePercentage >= 50) {
            results.status = 'fair';
        } else {
            results.status = 'poor';
        }
        
        return results;
    }

    /**
     * 生成测试报告
     */
    generateReport(testResults) {
        this.log('生成测试报告...', 'step');
        
        const report = {
            timestamp: new Date().toISOString(),
            productName: this.productName,
            version: this.version,
            summary: {
                totalFiles: testResults.length,
                excellentFiles: 0,
                goodFiles: 0,
                fairFiles: 0,
                poorFiles: 0,
                averageScore: 0
            },
            results: testResults,
            recommendations: []
        };
        
        // 分析结果
        let totalScore = 0;
        testResults.forEach(result => {
            totalScore += result.score;
            switch (result.status) {
                case 'excellent':
                    report.summary.excellentFiles++;
                    break;
                case 'good':
                    report.summary.goodFiles++;
                    break;
                case 'fair':
                    report.summary.fairFiles++;
                    break;
                case 'poor':
                    report.summary.poorFiles++;
                    break;
            }
        });
        
        report.summary.averageScore = totalScore / (testResults.length * testResults[0]?.maxScore || 1) * 100;
        
        // 生成建议
        if (report.summary.excellentFiles > 0) {
            report.recommendations.push('推荐使用评分为"excellent"的DMG文件');
        }
        
        if (report.summary.poorFiles > 0) {
            report.recommendations.push('避免使用评分为"poor"的DMG文件');
        }
        
        const ultimateFiles = testResults.filter(r => r.type === 'ULTIMATE');
        if (ultimateFiles.length > 0) {
            report.recommendations.push('终极版本(ULTIMATE)使用了最强力的修复方法，推荐优先使用');
        }
        
        // 保存报告
        const reportPath = path.join(this.ultimateDir, 'ultimate-test-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        this.log(`测试报告已保存: ${reportPath}`, 'success');
        return report;
    }

    /**
     * 显示测试结果
     */
    showResults(report) {
        console.log('\n' + '='.repeat(60));
        this.log('🔥 终极DMG测试结果', 'ultimate');
        console.log('='.repeat(60));
        
        console.log(`\n📊 测试摘要:`);
        console.log(`  总文件数: ${report.summary.totalFiles}`);
        console.log(`  优秀(90%+): ${report.summary.excellentFiles}`);
        console.log(`  良好(75%+): ${report.summary.goodFiles}`);
        console.log(`  一般(50%+): ${report.summary.fairFiles}`);
        console.log(`  较差(<50%): ${report.summary.poorFiles}`);
        console.log(`  平均得分: ${report.summary.averageScore.toFixed(1)}%`);
        
        console.log(`\n📋 详细结果:`);
        report.results.forEach(result => {
            const statusIcon = {
                'excellent': '🔥',
                'good': '✅',
                'fair': '⚠️',
                'poor': '❌',
                'unknown': '❓'
            }[result.status] || '❓';
            
            const scorePercentage = (result.score / result.maxScore * 100).toFixed(1);
            
            console.log(`\n  ${statusIcon} ${result.file} (${result.type}, ${result.architecture}):`);
            console.log(`    得分: ${result.score}/${result.maxScore} (${scorePercentage}%)`);
            console.log(`    DMG完整性: ${result.dmgValid ? '✅' : '❌'}`);
            console.log(`    应用程序: ${result.appExists ? '✅' : '❌'}`);
            console.log(`    应用签名: ${result.appSigned ? '✅' : '❌'}`);
            console.log(`    资源封装: ${result.sealedResources ? '✅' : '❌'}`);
            console.log(`    Info.plist: ${result.infoPlistBound ? '✅' : '❌'}`);
            console.log(`    签名验证: ${result.signatureValid ? '✅' : '❌'}`);
            console.log(`    无隔离属性: ${result.noQuarantine ? '✅' : '❌'}`);
            
            if (result.type === 'ULTIMATE') {
                console.log(`    修复脚本: ${result.hasFixScript ? '✅' : '❌'}`);
                console.log(`    安装指南: ${result.hasGuide ? '✅' : '❌'}`);
            }
            
            if (result.issues.length > 0) {
                console.log(`    问题:`);
                result.issues.forEach(issue => console.log(`      - ${issue}`));
            }
        });
        
        console.log(`\n💡 建议:`);
        report.recommendations.forEach(rec => {
            console.log(`  - ${rec}`);
        });
        
        // 推荐最佳文件
        const bestFile = report.results.reduce((best, current) => 
            current.score > best.score ? current : best
        );
        
        if (bestFile) {
            console.log(`\n🏆 推荐使用:`);
            console.log(`  ${bestFile.file} (得分: ${bestFile.score}/${bestFile.maxScore})`);
            console.log(`  这个版本应该彻底解决"已损坏"问题，显示"无法验证"而不是"已损坏"`);
        }
    }

    /**
     * 主测试流程
     */
    async test() {
        try {
            console.log('🔥 开始测试终极DMG文件...\n');
            
            const dmgFiles = this.findAllDMGFiles();
            
            if (dmgFiles.length === 0) {
                throw new Error('未找到任何DMG文件');
            }
            
            const testResults = [];
            
            // 测试所有DMG文件
            for (const dmgInfo of dmgFiles) {
                const result = this.testDMGFile(dmgInfo);
                testResults.push(result);
            }
            
            const report = this.generateReport(testResults);
            this.showResults(report);
            
            return report.summary.averageScore >= 75;
            
        } catch (error) {
            this.log(`测试失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        }
    }
}

// 主程序入口
async function main() {
    const tester = new UltimateDMGTester();
    const success = await tester.test();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = UltimateDMGTester;
