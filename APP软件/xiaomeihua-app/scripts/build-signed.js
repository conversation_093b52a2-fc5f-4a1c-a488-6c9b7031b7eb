#!/usr/bin/env node

/**
 * 代码签名构建脚本
 * 自动构建并签名 macOS 应用程序
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class SignedBuilder {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.configFile = path.join(this.projectRoot, '.codesign-config');
        this.envFile = path.join(this.projectRoot, '.env.codesign');
        this.arch = process.argv[2] || 'universal'; // x64, arm64, universal
        
        // 默认配置
        this.config = {
            enableCodesigning: true,
            enableHardenedRuntime: true,
            enableNotarization: false,
            signDMG: true,
            developerIdCert: null
        };
        
        this.loadConfig();
    }

    /**
     * 加载配置
     */
    loadConfig() {
        // 加载证书配置
        if (fs.existsSync(this.configFile)) {
            const configContent = fs.readFileSync(this.configFile, 'utf8');
            const match = configContent.match(/DEVELOPER_ID_CERT="(.+)"/);
            if (match) {
                this.config.developerIdCert = match[1];
            }
        }

        // 加载环境配置
        if (fs.existsSync(this.envFile)) {
            const envContent = fs.readFileSync(this.envFile, 'utf8');
            const lines = envContent.split('\n');
            
            for (const line of lines) {
                if (line.startsWith('ENABLE_CODESIGNING=')) {
                    this.config.enableCodesigning = line.split('=')[1] === 'true';
                }
                if (line.startsWith('ENABLE_HARDENED_RUNTIME=')) {
                    this.config.enableHardenedRuntime = line.split('=')[1] === 'true';
                }
                if (line.startsWith('ENABLE_NOTARIZATION=')) {
                    this.config.enableNotarization = line.split('=')[1] === 'true';
                }
                if (line.startsWith('SIGN_DMG=')) {
                    this.config.signDMG = line.split('=')[1] === 'true';
                }
            }
        }
    }

    /**
     * 检查代码签名环境
     */
    checkSigningEnvironment() {
        console.log('🔍 检查代码签名环境...');
        
        if (!this.config.enableCodesigning) {
            console.log('⚠️  代码签名已禁用');
            return false;
        }

        if (!this.config.developerIdCert) {
            console.log('❌ 未找到 Developer ID 证书配置');
            console.log('💡 请运行: npm run setup:codesign');
            return false;
        }

        // 验证证书是否存在
        try {
            const result = execSync(`security find-identity -v -p codesigning | grep "${this.config.developerIdCert}"`, { encoding: 'utf8' });
            if (result.trim()) {
                console.log(`✅ 找到证书: ${this.config.developerIdCert}`);
                return true;
            }
        } catch (error) {
            console.log(`❌ 证书验证失败: ${this.config.developerIdCert}`);
            return false;
        }

        return false;
    }

    /**
     * 设置环境变量
     */
    setupEnvironment() {
        console.log('🔧 设置构建环境...');
        
        const env = {
            ...process.env,
            // 禁用自动证书发现，使用指定证书
            CSC_IDENTITY_AUTO_DISCOVERY: 'false'
        };

        if (this.config.enableCodesigning && this.config.developerIdCert) {
            env.CSC_NAME = this.config.developerIdCert;
            console.log(`✅ 使用证书: ${this.config.developerIdCert}`);
        } else {
            // 禁用代码签名
            env.CSC_IDENTITY_AUTO_DISCOVERY = 'false';
            env.CSC_LINK = '';
            env.CSC_KEY_PASSWORD = '';
            console.log('⚠️  构建无签名版本');
        }

        return env;
    }

    /**
     * 清理旧文件
     */
    cleanOldFiles() {
        console.log('🧹 清理旧文件...');
        
        const distDir = path.join(this.projectRoot, 'dist');
        if (fs.existsSync(distDir)) {
            execSync('rm -rf dist/*', { cwd: this.projectRoot });
            console.log('✅ 已清理 dist 目录');
        }
    }

    /**
     * 构建应用
     */
    async buildApp() {
        console.log(`🚀 开始构建 ${this.arch} 架构的应用...`);
        
        const env = this.setupEnvironment();
        
        // 构建命令
        let buildCmd;
        switch (this.arch) {
            case 'x64':
                buildCmd = 'npx electron-builder --mac --x64';
                break;
            case 'arm64':
                buildCmd = 'npx electron-builder --mac --arm64';
                break;
            case 'universal':
                buildCmd = 'npx electron-builder --mac --universal';
                break;
            default:
                buildCmd = 'npx electron-builder --mac';
        }

        try {
            console.log(`执行命令: ${buildCmd}`);
            execSync(buildCmd, { 
                stdio: 'inherit',
                env: env,
                cwd: this.projectRoot
            });
            console.log('✅ 应用构建完成');
        } catch (error) {
            console.error('❌ 构建失败:', error.message);
            throw error;
        }
    }

    /**
     * 验证签名
     */
    verifySignature() {
        console.log('🔍 验证应用签名...');
        
        const distDir = path.join(this.projectRoot, 'dist');
        const appFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.app'));
        const dmgFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.dmg'));
        
        // 验证 .app 文件签名
        for (const appFile of appFiles) {
            const appPath = path.join(distDir, appFile);
            try {
                execSync(`codesign --verify --deep --strict --verbose=2 "${appPath}"`, { stdio: 'pipe' });
                console.log(`✅ ${appFile} 签名验证通过`);
            } catch (error) {
                console.log(`⚠️  ${appFile} 签名验证失败`);
            }
        }
        
        // 验证 DMG 文件签名
        for (const dmgFile of dmgFiles) {
            const dmgPath = path.join(distDir, dmgFile);
            try {
                execSync(`codesign --verify --verbose=2 "${dmgPath}"`, { stdio: 'pipe' });
                console.log(`✅ ${dmgFile} 签名验证通过`);
            } catch (error) {
                console.log(`⚠️  ${dmgFile} 签名验证失败`);
            }
        }
    }

    /**
     * 生成构建报告
     */
    generateReport() {
        console.log('📋 生成构建报告...');
        
        const distDir = path.join(this.projectRoot, 'dist');
        const files = fs.readdirSync(distDir);
        
        const report = {
            buildTime: new Date().toISOString(),
            arch: this.arch,
            codesigning: {
                enabled: this.config.enableCodesigning,
                certificate: this.config.developerIdCert,
                hardenedRuntime: this.config.enableHardenedRuntime,
                notarization: this.config.enableNotarization
            },
            files: files.map(file => {
                const filePath = path.join(distDir, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    size: stats.size,
                    sizeFormatted: `${(stats.size / 1024 / 1024).toFixed(1)}MB`,
                    modified: stats.mtime.toISOString()
                };
            })
        };
        
        const reportPath = path.join(this.projectRoot, 'build-report-signed.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`✅ 构建报告已保存: ${reportPath}`);
        
        return report;
    }

    /**
     * 主构建流程
     */
    async build() {
        try {
            console.log('🍎 开始代码签名构建流程...\n');
            
            // 检查签名环境
            const signingReady = this.checkSigningEnvironment();
            if (!signingReady && this.config.enableCodesigning) {
                console.log('\n❌ 代码签名环境未就绪');
                console.log('💡 请先运行: npm run setup:codesign');
                process.exit(1);
            }
            
            // 清理旧文件
            this.cleanOldFiles();
            
            // 构建应用
            await this.buildApp();
            
            // 验证签名
            if (this.config.enableCodesigning) {
                this.verifySignature();
            }
            
            // 生成报告
            const report = this.generateReport();
            
            console.log('\n🎉 构建完成！');
            console.log(`📦 生成文件: ${report.files.length} 个`);
            console.log(`📁 输出目录: ${path.join(this.projectRoot, 'dist')}`);
            
            if (this.config.enableCodesigning) {
                console.log('✅ 应用已签名，可以正常安装');
            } else {
                console.log('⚠️  应用未签名，安装时可能需要用户手动允许');
            }
            
        } catch (error) {
            console.error('\n❌ 构建失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行构建
if (require.main === module) {
    const builder = new SignedBuilder();
    builder.build();
}

module.exports = SignedBuilder;
