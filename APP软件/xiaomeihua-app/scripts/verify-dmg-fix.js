#!/usr/bin/env node

/**
 * DMG修复效果验证脚本
 * 验证优化后的DMG是否解决了"已损坏"问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DMGFixVerifier {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        this.log('🔍 DMG修复效果验证工具');
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 查找DMG文件
     */
    findDMGFiles() {
        this.log('查找DMG文件...', 'step');
        
        if (!fs.existsSync(this.distDir)) {
            throw new Error('dist目录不存在');
        }
        
        const files = fs.readdirSync(this.distDir);
        const dmgFiles = files.filter(file => file.endsWith('.dmg'));
        
        if (dmgFiles.length === 0) {
            throw new Error('未找到DMG文件');
        }
        
        // 分类DMG文件
        const originalDMGs = dmgFiles.filter(file => !file.includes('optimized') && !file.includes('fixed'));
        const optimizedDMGs = dmgFiles.filter(file => file.includes('optimized'));
        const fixedDMGs = dmgFiles.filter(file => file.includes('fixed'));
        
        this.log(`找到 ${dmgFiles.length} 个DMG文件:`);
        this.log(`  原始DMG: ${originalDMGs.length} 个`);
        this.log(`  优化DMG: ${optimizedDMGs.length} 个`);
        this.log(`  修复DMG: ${fixedDMGs.length} 个`);
        
        return {
            original: originalDMGs,
            optimized: optimizedDMGs,
            fixed: fixedDMGs,
            all: dmgFiles
        };
    }

    /**
     * 验证单个DMG文件
     */
    verifyDMGFile(dmgFile) {
        this.log(`验证DMG文件: ${dmgFile}`, 'step');
        
        const dmgPath = path.join(this.distDir, dmgFile);
        const results = {
            file: dmgFile,
            dmgSigned: false,
            appSigned: false,
            sealedResources: false,
            infoPlistBound: false,
            signatureType: 'none',
            issues: [],
            recommendations: []
        };
        
        try {
            // 检查DMG签名
            try {
                const dmgSignInfo = this.exec(`codesign -dv "${dmgPath}" 2>&1`, { silent: true });
                results.dmgSigned = true;
                this.log('DMG文件已签名', 'success');
            } catch (error) {
                results.dmgSigned = false;
                this.log('DMG文件未签名', 'info');
            }
            
            // 挂载DMG并检查应用程序
            const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
            const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
            
            if (mountLines.length === 0) {
                throw new Error('无法挂载DMG');
            }
            
            const mountPoint = mountLines[0].split('\t').pop().trim();
            
            try {
                const appName = `${this.productName}.app`;
                const appPath = path.join(mountPoint, appName);
                
                if (!fs.existsSync(appPath)) {
                    throw new Error('应用程序不存在');
                }
                
                // 检查应用程序签名
                const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
                results.appSigned = true;
                
                // 分析签名信息
                if (signInfo.includes('Signature=adhoc')) {
                    results.signatureType = 'adhoc';
                } else if (signInfo.includes('Developer ID Application')) {
                    results.signatureType = 'developer-id';
                } else {
                    results.signatureType = 'unknown';
                }
                
                // 检查资源封装
                if (signInfo.includes('Sealed Resources=none')) {
                    results.sealedResources = false;
                    results.issues.push('缺少资源封装 (Sealed Resources=none)');
                    results.recommendations.push('需要重新进行adhoc签名以包含资源封装');
                } else if (signInfo.includes('Sealed Resources version=')) {
                    results.sealedResources = true;
                    this.log('✅ 资源封装正常', 'success');
                }
                
                // 检查Info.plist绑定
                if (signInfo.includes('Info.plist=not bound')) {
                    results.infoPlistBound = false;
                    results.issues.push('Info.plist未绑定 (Info.plist=not bound)');
                    results.recommendations.push('需要重新签名以绑定Info.plist');
                } else if (signInfo.includes('Info.plist entries=')) {
                    results.infoPlistBound = true;
                    this.log('✅ Info.plist绑定正常', 'success');
                }
                
                // 验证签名有效性
                try {
                    this.exec(`codesign --verify "${appPath}"`, { silent: true });
                    this.log('✅ 签名验证通过', 'success');
                } catch (error) {
                    results.issues.push('签名验证失败');
                }
                
            } finally {
                // 卸载DMG
                this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
            }
            
        } catch (error) {
            results.issues.push(`验证失败: ${error.message}`);
        }
        
        return results;
    }

    /**
     * 生成验证报告
     */
    generateReport(verificationResults) {
        this.log('生成验证报告...', 'step');
        
        const report = {
            timestamp: new Date().toISOString(),
            productName: this.productName,
            version: this.version,
            summary: {
                totalFiles: verificationResults.length,
                fixedFiles: 0,
                problematicFiles: 0
            },
            results: verificationResults,
            overallStatus: 'unknown',
            recommendations: []
        };
        
        // 分析结果
        verificationResults.forEach(result => {
            if (result.sealedResources && result.infoPlistBound && result.issues.length === 0) {
                report.summary.fixedFiles++;
            } else {
                report.summary.problematicFiles++;
            }
        });
        
        // 确定整体状态
        if (report.summary.fixedFiles > 0 && report.summary.problematicFiles === 0) {
            report.overallStatus = 'fixed';
            report.recommendations.push('所有DMG文件已修复，应该显示"无法验证"而不是"已损坏"');
        } else if (report.summary.fixedFiles > 0) {
            report.overallStatus = 'partially-fixed';
            report.recommendations.push('部分DMG文件已修复，建议使用优化版本');
        } else {
            report.overallStatus = 'not-fixed';
            report.recommendations.push('需要运行修复脚本或重新构建');
        }
        
        // 保存报告
        const reportPath = path.join(this.distDir, 'verification-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        this.log(`验证报告已保存: ${reportPath}`, 'success');
        return report;
    }

    /**
     * 显示验证结果
     */
    showResults(report) {
        console.log('\n' + '='.repeat(60));
        this.log('🔍 DMG修复效果验证结果', 'step');
        console.log('='.repeat(60));
        
        console.log(`\n📊 验证摘要:`);
        console.log(`  总文件数: ${report.summary.totalFiles}`);
        console.log(`  已修复文件: ${report.summary.fixedFiles}`);
        console.log(`  问题文件: ${report.summary.problematicFiles}`);
        console.log(`  整体状态: ${report.overallStatus}`);
        
        console.log(`\n📋 详细结果:`);
        report.results.forEach(result => {
            console.log(`\n  📁 ${result.file}:`);
            console.log(`    签名类型: ${result.signatureType}`);
            console.log(`    资源封装: ${result.sealedResources ? '✅ 正常' : '❌ 缺失'}`);
            console.log(`    Info.plist: ${result.infoPlistBound ? '✅ 已绑定' : '❌ 未绑定'}`);
            
            if (result.issues.length > 0) {
                console.log(`    问题:`);
                result.issues.forEach(issue => console.log(`      - ${issue}`));
            }
        });
        
        console.log(`\n💡 建议:`);
        report.recommendations.forEach(rec => {
            console.log(`  - ${rec}`);
        });
        
        if (report.overallStatus === 'fixed') {
            console.log(`\n🎉 修复成功！`);
            console.log(`  - 应用程序现在应该显示"无法验证"而不是"已损坏"`);
            console.log(`  - 用户可以通过系统设置允许运行应用程序`);
            console.log(`  - 或使用提供的修复脚本移除隔离属性`);
        }
    }

    /**
     * 主验证流程
     */
    async verify() {
        try {
            console.log('🔍 开始验证DMG修复效果...\n');
            
            const dmgFiles = this.findDMGFiles();
            const verificationResults = [];
            
            // 验证所有DMG文件
            for (const dmgFile of dmgFiles.all) {
                const result = this.verifyDMGFile(dmgFile);
                verificationResults.push(result);
            }
            
            const report = this.generateReport(verificationResults);
            this.showResults(report);
            
            return report.overallStatus === 'fixed' || report.overallStatus === 'partially-fixed';
            
        } catch (error) {
            this.log(`验证失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        }
    }
}

// 主程序入口
async function main() {
    const verifier = new DMGFixVerifier();
    const success = await verifier.verify();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = DMGFixVerifier;
