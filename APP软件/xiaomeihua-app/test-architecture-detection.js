#!/usr/bin/env node

/**
 * 架构检测测试脚本
 * 用于验证M芯片和Intel芯片的检测是否正确
 */

const { app } = require('electron');
const path = require('path');

// 模拟不同的进程信息
const testCases = [
  {
    name: 'M1 MacBook Air',
    platform: 'darwin',
    arch: 'arm64',
    expected: {
      architecture: 'm1',
      apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_m1.php',
      platformKey: 'macos_m1'
    }
  },
  {
    name: 'M2 MacBook Pro',
    platform: 'darwin',
    arch: 'arm64',
    expected: {
      architecture: 'm1',
      apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_m1.php',
      platformKey: 'macos_m1'
    }
  },
  {
    name: 'Intel MacBook Pro',
    platform: 'darwin',
    arch: 'x64',
    expected: {
      architecture: 'intel',
      apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_intel.php',
      platformKey: 'macos_intel'
    }
  },
  {
    name: 'Intel iMac',
    platform: 'darwin',
    arch: 'x64',
    expected: {
      architecture: 'intel',
      apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_intel.php',
      platformKey: 'macos_intel'
    }
  },
  {
    name: 'Windows PC',
    platform: 'win32',
    arch: 'x64',
    expected: {
      architecture: 'x64',
      apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_new.php',
      platformKey: 'windows'
    }
  }
];

// 模拟getPlatformInfo函数
function getPlatformInfo(testPlatform, testArch) {
  const platform = testPlatform;
  const arch = testArch;
  
  console.log(`🔍 检测平台信息: platform=${platform}, arch=${arch}`);
  
  if (platform === 'win32') {
    return {
      platform: 'windows',
      architecture: 'x64',
      apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_new.php',
      platformKey: 'windows'
    };
  } else if (platform === 'darwin') {
    if (arch === 'arm64') {
      // M系列芯片 - 使用专用API端点
      return {
        platform: 'macos',
        architecture: 'm1',
        apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_m1.php',
        platformKey: 'macos_m1'
      };
    } else {
      // Intel芯片 - 使用专用API端点
      return {
        platform: 'macos',
        architecture: 'intel', 
        apiUrl: 'https://xiaomeihuakefu.cn/api/app_update_macos_intel.php',
        platformKey: 'macos_intel'
      };
    }
  } else {
    throw new Error(`不支持的平台: ${platform}`);
  }
}

// 运行测试
function runTests() {
  console.log('🚀 === 架构检测测试开始 ===\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    console.log(`📱 测试 ${index + 1}: ${testCase.name}`);
    console.log(`   输入: platform=${testCase.platform}, arch=${testCase.arch}`);
    
    try {
      const result = getPlatformInfo(testCase.platform, testCase.arch);
      
      // 检查结果
      const isCorrect = 
        result.architecture === testCase.expected.architecture &&
        result.apiUrl === testCase.expected.apiUrl &&
        result.platformKey === testCase.expected.platformKey;
      
      if (isCorrect) {
        console.log(`   ✅ 通过`);
        console.log(`   架构: ${result.architecture}`);
        console.log(`   API: ${result.apiUrl}`);
        console.log(`   平台键: ${result.platformKey}`);
        passedTests++;
      } else {
        console.log(`   ❌ 失败`);
        console.log(`   期望架构: ${testCase.expected.architecture}, 实际: ${result.architecture}`);
        console.log(`   期望API: ${testCase.expected.apiUrl}`);
        console.log(`   实际API: ${result.apiUrl}`);
        console.log(`   期望平台键: ${testCase.expected.platformKey}, 实际: ${result.platformKey}`);
      }
    } catch (error) {
      console.log(`   ❌ 错误: ${error.message}`);
    }
    
    console.log('');
  });
  
  console.log(`🎯 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！架构检测工作正常。');
  } else {
    console.log('⚠️  部分测试失败，请检查架构检测逻辑。');
  }
}

// 测试当前系统的架构检测
function testCurrentSystem() {
  console.log('\n🖥️  === 当前系统检测 ===');
  
  try {
    const currentPlatform = process.platform;
    const currentArch = process.arch;
    const result = getPlatformInfo(currentPlatform, currentArch);
    
    console.log(`当前系统: ${currentPlatform} (${currentArch})`);
    console.log(`检测结果:`);
    console.log(`  平台: ${result.platform}`);
    console.log(`  架构: ${result.architecture}`);
    console.log(`  API端点: ${result.apiUrl}`);
    console.log(`  平台键: ${result.platformKey}`);
    
    // 给出建议
    if (currentPlatform === 'darwin') {
      if (currentArch === 'arm64') {
        console.log(`💡 您使用的是M系列芯片Mac，将使用M芯片专用更新API`);
      } else {
        console.log(`💡 您使用的是Intel芯片Mac，将使用Intel专用更新API`);
      }
    }
    
  } catch (error) {
    console.log(`❌ 当前系统检测失败: ${error.message}`);
  }
}

// 运行所有测试
runTests();
testCurrentSystem();

console.log('\n📋 API端点说明:');
console.log('  - M芯片Mac: app_update_macos_m1.php');
console.log('  - Intel Mac: app_update_macos_intel.php');
console.log('  - Windows: app_update_new.php');
console.log('\n🔧 这确保了不同架构的用户获得正确的更新检查和下载链接。');
