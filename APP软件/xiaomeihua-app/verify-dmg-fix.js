#!/usr/bin/env node

/**
 * 验证DMG文件包含更新修复
 * 检查打包的app是否包含我们的修复代码
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
    projectRoot: __dirname,
    distDir: path.join(__dirname, 'dist'),
    appPaths: {
        arm64: 'dist/mac-arm64/小梅花AI智能客服.app',
        x64: 'dist/mac/小梅花AI智能客服.app'
    }
};

// 日志函数
function log(message, type = 'info') {
    const colors = {
        info: '\x1b[36m',
        success: '\x1b[32m',
        warning: '\x1b[33m',
        error: '\x1b[31m',
        reset: '\x1b[0m'
    };
    
    const icons = {
        info: 'ℹ️',
        success: '✅',
        warning: '⚠️',
        error: '❌'
    };
    
    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
}

// 检查文件是否存在
function checkFileExists(filePath) {
    return fs.existsSync(filePath);
}

// 检查app内容
function checkAppContents(appPath, arch) {
    log(`检查 ${arch} 版本的app内容...`, 'info');
    
    const resourcesPath = path.join(appPath, 'Contents/Resources');
    const appAsarPath = path.join(resourcesPath, 'app.asar');
    
    if (!checkFileExists(appAsarPath)) {
        log(`app.asar 文件不存在: ${appAsarPath}`, 'error');
        return false;
    }
    
    try {
        // 提取app.asar内容到临时目录
        const tempDir = path.join(config.projectRoot, `temp-${arch}`);
        if (fs.existsSync(tempDir)) {
            execSync(`rm -rf "${tempDir}"`);
        }
        
        execSync(`npx asar extract "${appAsarPath}" "${tempDir}"`);
        
        // 检查关键文件
        const keyFiles = [
            'src/app-updater.js',
            'src/main.js',
            'package.json'
        ];
        
        let allFilesExist = true;
        for (const file of keyFiles) {
            const filePath = path.join(tempDir, file);
            if (checkFileExists(filePath)) {
                log(`✓ ${file} 存在`, 'success');
            } else {
                log(`✗ ${file} 不存在`, 'error');
                allFilesExist = false;
            }
        }
        
        // 检查app-updater.js中是否包含我们的修复
        const updaterPath = path.join(tempDir, 'src/app-updater.js');
        if (checkFileExists(updaterPath)) {
            const updaterContent = fs.readFileSync(updaterPath, 'utf8');
            
            // 检查关键修复代码
            const fixChecks = [
                {
                    name: '版本格式验证函数',
                    pattern: /isValidVersionFormat\s*\(/,
                    found: false
                },
                {
                    name: '版本变化检测',
                    pattern: /检测到版本号变化/,
                    found: false
                },
                {
                    name: '增强错误处理',
                    pattern: /skipReason.*invalid_version_format/,
                    found: false
                }
            ];
            
            for (const check of fixChecks) {
                check.found = check.pattern.test(updaterContent);
                if (check.found) {
                    log(`✓ ${check.name} - 已包含`, 'success');
                } else {
                    log(`✗ ${check.name} - 未找到`, 'warning');
                }
            }
            
            const fixesFound = fixChecks.filter(c => c.found).length;
            log(`修复代码检查: ${fixesFound}/${fixChecks.length} 项通过`, 
                fixesFound === fixChecks.length ? 'success' : 'warning');
        }
        
        // 检查package.json版本
        const packagePath = path.join(tempDir, 'package.json');
        if (checkFileExists(packagePath)) {
            const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            log(`App版本: ${packageContent.version}`, 'info');
            log(`产品名称: ${packageContent.productName}`, 'info');
        }
        
        // 清理临时目录
        execSync(`rm -rf "${tempDir}"`);
        
        return allFilesExist;
        
    } catch (error) {
        log(`检查app内容时出错: ${error.message}`, 'error');
        return false;
    }
}

// 检查DMG文件
function checkDMGFiles() {
    log('检查DMG文件...', 'info');
    
    const dmgFiles = [
        'dist/小梅花AI智能客服-3.0.0-arm64.dmg',
        'dist/小梅花AI智能客服-3.0.0-x64.dmg'
    ];
    
    for (const dmgFile of dmgFiles) {
        const dmgPath = path.join(config.projectRoot, dmgFile);
        if (checkFileExists(dmgPath)) {
            const stats = fs.statSync(dmgPath);
            const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
            log(`✓ ${path.basename(dmgFile)} (${sizeMB}MB)`, 'success');
        } else {
            log(`✗ ${path.basename(dmgFile)} 不存在`, 'error');
        }
    }
}

// 主函数
function main() {
    log('🔍 验证DMG文件包含更新修复...', 'info');
    console.log('');
    
    // 检查DMG文件
    checkDMGFiles();
    console.log('');
    
    // 检查app内容
    let allChecksPass = true;
    
    for (const [arch, appPath] of Object.entries(config.appPaths)) {
        const fullAppPath = path.join(config.projectRoot, appPath);
        if (checkFileExists(fullAppPath)) {
            const result = checkAppContents(fullAppPath, arch);
            if (!result) {
                allChecksPass = false;
            }
        } else {
            log(`${arch} 版本的app不存在: ${appPath}`, 'error');
            allChecksPass = false;
        }
        console.log('');
    }
    
    // 总结
    if (allChecksPass) {
        log('🎉 验证完成！DMG文件包含所有修复代码。', 'success');
        log('📦 可以安全地分发这些DMG文件。', 'success');
    } else {
        log('⚠️ 验证发现问题，请检查上述错误。', 'warning');
    }
    
    console.log('');
    log('📁 DMG文件位置:', 'info');
    log(`   ${path.resolve(config.distDir)}`, 'info');
}

// 运行
if (require.main === module) {
    main();
}

module.exports = { main, checkAppContents, checkDMGFiles };
