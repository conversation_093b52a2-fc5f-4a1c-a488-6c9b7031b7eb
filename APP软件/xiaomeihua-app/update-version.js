#!/usr/bin/env node

/**
 * 版本号统一更新脚本
 * 用于更新所有文件中的版本号，确保版本一致性
 * 
 * 使用方法：
 * node update-version.js [新版本号]
 * 
 * 例如：
 * node update-version.js 1.0.1
 */

const fs = require('fs');
const path = require('path');

// 获取命令行参数中的新版本号
const newVersion = process.argv[2];

if (!newVersion) {
  console.error('❌ 请提供新版本号');
  console.log('使用方法: node update-version.js [版本号]');
  console.log('例如: node update-version.js 1.0.1');
  process.exit(1);
}

// 验证版本号格式
const versionRegex = /^\d+\.\d+\.\d+$/;
if (!versionRegex.test(newVersion)) {
  console.error('❌ 版本号格式错误，请使用 x.y.z 格式');
  process.exit(1);
}

console.log(`🚀 开始更新版本号到 ${newVersion}...`);

// 需要更新的文件列表
const filesToUpdate = [
  {
    file: 'version-config.js',
    pattern: /VERSION: '[^']+'/,
    replacement: `VERSION: '${newVersion}'`
  },
  {
    file: 'package.json',
    pattern: /"version": "[^"]+"/,
    replacement: `"version": "${newVersion}"`
  },
  {
    file: 'package-lock.json',
    pattern: /"version": "[^"]+"/,
    replacement: `"version": "${newVersion}"`,
    multiple: true // 可能有多个匹配
  },
  {
    file: 'src/renderer/main.html',
    pattern: /v\d+\.\d+\.\d+/g,
    replacement: `v${newVersion}`
  },
  {
    file: 'src/renderer/login.html',
    pattern: /版本: v\d+\.\d+\.\d+/,
    replacement: `版本: v${newVersion}`
  },
  {
    file: 'rebuild-all.js',
    pattern: /version: '\d+\.\d+\.\d+'/,
    replacement: `version: '${newVersion}'`
  },
  {
    file: 'scripts/package-all.js',
    pattern: /version: '\d+\.\d+\.\d+'/,
    replacement: `version: '${newVersion}'`
  }
];

// 更新文件函数
function updateFile(fileInfo) {
  const filePath = path.join(__dirname, fileInfo.file);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在，跳过: ${fileInfo.file}`);
    return;
  }
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    if (fileInfo.multiple) {
      // 处理多个匹配的情况
      content = content.replace(fileInfo.pattern, fileInfo.replacement);
    } else {
      // 处理单个匹配的情况
      content = content.replace(fileInfo.pattern, fileInfo.replacement);
    }
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已更新: ${fileInfo.file}`);
    } else {
      console.log(`ℹ️  无需更新: ${fileInfo.file}`);
    }
  } catch (error) {
    console.error(`❌ 更新失败: ${fileInfo.file}`, error.message);
  }
}

// 执行更新
filesToUpdate.forEach(updateFile);

// 更新发布说明模板中的版本号
const releaseNotesPattern = /小梅花AI智能客服 v\d+\.\d+\.\d+ 发布包/;
const releaseNotesReplacement = `小梅花AI智能客服 v${newVersion} 发布包`;

try {
  const packageAllPath = path.join(__dirname, 'scripts/package-all.js');
  if (fs.existsSync(packageAllPath)) {
    let content = fs.readFileSync(packageAllPath, 'utf8');
    content = content.replace(releaseNotesPattern, releaseNotesReplacement);
    content = content.replace(/版本: \d+\.\d+\.\d+/, `版本: ${newVersion}`);
    fs.writeFileSync(packageAllPath, content, 'utf8');
    console.log('✅ 已更新发布说明模板');
  }
} catch (error) {
  console.error('❌ 更新发布说明模板失败:', error.message);
}

console.log(`\n🎉 版本号更新完成！新版本: ${newVersion}`);
console.log('📝 请检查以下位置的版本号是否正确：');
console.log('   - 登录窗口底部');
console.log('   - 设置页面中的软件版本');
console.log('   - 左下角版本显示');
console.log('   - macOS关于窗口');
console.log('\n💡 建议运行测试确保所有版本号显示正确');
