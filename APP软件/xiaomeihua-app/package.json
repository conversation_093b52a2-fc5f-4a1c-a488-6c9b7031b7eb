{"name": "xiaomeihua-ai", "productName": "小梅花AI智能客服", "version": "1.0.0", "description": "小梅花AI智能客服", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win", "build:dmg": "node ultimate-fixed-build.js", "build:with-updater": "node build-with-updater.js", "build:dmg:x64": "electron-builder --mac --x64", "build:dmg:arm64": "electron-builder --mac --arm64", "build:dmg:universal": "electron-builder --mac --universal", "build:all": "electron-builder --mac --win --x64", "clean": "<PERSON><PERSON><PERSON> dist", "package:dmg": "node scripts/package-dmg.js", "package:dmg:enhanced": "node scripts/build-dmg-enhanced.js", "package:dmg:secure": "node scripts/build-dmg-secure.js", "package:dmg:quick": "node scripts/quick-dmg.js", "package:dmg:x64": "node scripts/quick-dmg.js x64", "package:dmg:arm64": "node scripts/quick-dmg.js arm64", "package:dmg:universal": "node scripts/quick-dmg.js universal", "build:signed": "node scripts/build-signed.js", "build:signed:x64": "node scripts/build-signed.js x64", "build:signed:arm64": "node scripts/build-signed.js arm64", "build:signed:universal": "node scripts/build-signed.js universal", "setup:codesign": "./scripts/setup-codesigning.sh", "verify:codesign": "node scripts/verify-codesigning.js", "auto-sign-package": "node scripts/auto-sign-and-package.js", "auto-sign-package:x64": "node scripts/auto-sign-and-package.js x64", "auto-sign-package:arm64": "node scripts/auto-sign-and-package.js arm64", "auto-sign-package:universal": "node scripts/auto-sign-and-package.js universal", "codesign:status": "node scripts/codesign-status.js", "codesign:help": "node scripts/certificate-helper.js", "fix:dmg": "node scripts/fix-dmg-damaged.js", "fix:dmg:x64": "node scripts/fix-dmg-damaged.js x64", "fix:dmg:arm64": "node scripts/fix-dmg-damaged.js arm64", "fix:dmg:universal": "node scripts/fix-dmg-damaged.js universal", "build:optimized": "node scripts/build-optimized-dmg.js", "build:optimized:x64": "node scripts/build-optimized-dmg.js x64", "build:optimized:arm64": "node scripts/build-optimized-dmg.js arm64", "build:optimized:universal": "node scripts/build-optimized-dmg.js universal", "ultimate:fix": "node scripts/ultimate-fix-damaged.js", "ultimate:fix:x64": "node scripts/ultimate-fix-damaged.js x64", "ultimate:fix:arm64": "node scripts/ultimate-fix-damaged.js arm64", "ultimate:fix:universal": "node scripts/ultimate-fix-damaged.js universal", "optimized:build": "node optimized-final-build.js", "optimized:build:x64": "node scripts/optimized-dmg-builder.js x64", "optimized:build:arm64": "node scripts/optimized-dmg-builder.js arm64", "optimized:build:universal": "node scripts/optimized-dmg-builder.js universal", "final:fix": "node scripts/ultimate-fix-gatekeeper.js", "final:fix:x64": "node scripts/ultimate-fix-gatekeeper.js x64", "final:fix:arm64": "node scripts/ultimate-fix-gatekeeper.js arm64", "final:fix:universal": "node scripts/ultimate-fix-gatekeeper.js universal", "dmg:final": "node scripts/optimized-dmg-final.js", "dmg:final:m": "node scripts/optimized-dmg-final.js arm64", "dmg:final:intel": "node scripts/optimized-dmg-final.js x64", "dmg:cleanup": "node scripts/cleanup-old-dmg.js", "build:m-intel": "node scripts/build-m-intel-dmg.js", "rebuild:dmg": "node rebuild-optimized-dmg.js", "rebuild:dmg:m": "node rebuild-optimized-dmg.js arm64", "rebuild:dmg:intel": "node rebuild-optimized-dmg.js x64", "build:final": "node build-final-dmg.js", "fix:m-chip": "node fix-m-chip-dmg.js", "force:fix:m-chip": "node force-fix-m-chip-640x480.js", "ultimate:build": "node ultimate-fixed-build.js", "no-popup:build": "node no-popup-640x480-build.js", "unified:build": "node unified-640x480-build.js", "fix:signing": "node fix-dmg-signing.js", "final:build": "node final-fixed-build.js", "verify:signing": "node verify-locked-signing.js", "update-version": "node update-version.js", "build:version": "node build-with-version.js", "build:version:skip": "node build-with-version.js --skip-version-check"}, "author": "小梅花AI科技", "license": "UNLICENSED", "private": true, "build": {"appId": "cn.xiaomeihuakefu.app", "productName": "小梅花AI智能客服", "copyright": "Copyright © 2025 小梅花AI科技", "directories": {"output": "dist"}, "mac": {"category": "public.app-category.business", "target": [{"target": "dmg", "arch": ["arm64", "x64"]}], "icon": "build/icon.icns", "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "hardenedRuntime": false, "gatekeeperAssess": false, "darkModeSupport": true, "identity": null, "type": "distribution", "minimumSystemVersion": "10.15.0"}, "dmg": {"title": "${productName}-${version}", "icon": "build/icon.icns", "iconSize": 100, "contents": [{"x": 160, "y": 280}, {"x": 480, "y": 280, "type": "link", "path": "/Applications"}, {"x": 320, "y": 80, "type": "file", "path": "build/Mac电脑安装教程.png"}], "window": {"width": 640, "height": 480}, "backgroundColor": "#ffffff", "sign": true, "writeUpdateInfo": false, "artifactName": "${productName}-${version}-${arch}.${ext}"}, "win": {"target": "nsis", "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "installerLanguages": ["zh_CN"], "language": "2052"}, "files": ["src/**/*", "package.json", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*/{.editorconfig,.gitignore,.travis.yml}", "!**/node_modules/*/{*.d.ts,*.map}", "!**/node_modules/*/docs/**", "!**/node_modules/*/doc/**", "!**/node_modules/*/man/**", "!**/node_modules/*/coverage/**", "!**/node_modules/*/.nyc_output/**", "!**/node_modules/*/bench/**", "!**/node_modules/*/benchmark/**", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,thumbs.db,.gitkeep}", "!build/**", "!scripts/**", "!dist/**", "!release/**", "!test*.js", "!verify*.js", "!debug*.js", "!run*.js", "!simple*.js", "!*.md", "!.giti<PERSON>re", "!.eslintrc*", "!.prettierrc*", "!tsconfig.json", "!false/**", "!*.bat", "!*.crx"], "extraResources": [{"from": "resources/icon.ico", "to": "resources/icon.ico"}, {"from": "resources/修复已损坏.command", "to": "修复已损坏.command"}, {"from": "resources/安装前先打开.txt", "to": "安装前先打开.txt"}], "publish": null, "asarUnpack": ["src/preload-browser.js", "src/preload.js", "src/popup-preload.js", "src/agreement-preload.js", "src/renderer/popup.html", "src/renderer/agreement.html"]}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.6.2", "electron-store": "^8.1.0", "electron-updater": "^6.6.2", "uuid": "^9.0.1"}}