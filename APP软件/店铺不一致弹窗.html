<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店铺登录错误提醒</title>
    <script src="https://cdn.tailwindcss.com/3.3.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <style>
        @keyframes fadeInScale {
            0% {
                opacity: 0;
                transform: scale(0.95);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes pulse {
            0% {
                opacity: 0.6;
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0.6;
            }
        }
        
        @keyframes shake {
            0%, 100% {
                transform: translateX(0);
            }
            20%, 60% {
                transform: translateX(-3px);
            }
            40%, 80% {
                transform: translateX(3px);
            }
        }
        
        @keyframes slideIn {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
        }
        
        .modal-container {
            animation: fadeInScale 0.4s ease-out forwards;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-radius: 12px;
            background-color: #f8fafc;
        }
        
        .alert-bar {
            animation: pulse 1.5s infinite ease-in-out;
            background-color: #ef4444;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            display: flex;
            justify-content: center;
        }
        
        .warning-icon {
            animation: shake 0.8s infinite;
        }
        
        .confirm-btn {
            transition: all 0.2s ease;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .correct-shop {
            border-left: 5px solid #00B42A;
            animation: slideIn 0.5s ease-out 0.2s both;
        }
        
        .wrong-shop {
            border-left: 5px solid #F53F3F;
            animation: slideIn 0.5s ease-out 0.4s both;
        }
        
        .shop-info {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 20px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .shop-label {
            font-size: 0.875rem;
            margin-right: 8px;
        }
        
        .correct-label {
            color: #00B42A;
            font-weight: bold;
        }
        
        .wrong-label {
            color: #F53F3F;
            font-weight: bold;
        }
        
        .shop-name {
            font-weight: bold;
            font-size: 16px;
        }
        
        .correct-name {
            color: #00B42A;
        }
        
        .wrong-name {
            color: #F53F3F;
        }
        
        .shop-desc {
            color: #111827;
            margin-top: 8px;
            font-size: 15px;
        }
        
        .warning-text {
            color: #F53F3F;
            font-weight: bold;
            font-size: 15px;
        }
        
        /* 新增样式优化图标文字对齐 */
        .shop-info-container {
            display: flex;
            align-items: center;
            line-height: 1.5;
        }
        
        .shop-icon {
            margin-right: 12px;
            font-size: 20px;
        }
        
        .shop-text-container {
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
    <!-- 弹窗容器 -->
    <div class="fixed inset-0 z-50 flex items-center justify-center modal-overlay">
        <div class="modal-container w-full max-w-md mx-4 overflow-hidden">
            <!-- 顶部警报条 -->
            <div class="alert-bar py-3 px-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-white text-xl mr-3 warning-icon"></i>
                    <span class="text-white font-medium">店铺登录错误提醒</span>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="p-6">
                <!-- 错误提示文字 -->
                <div class="mb-6 text-center">
                    <p class="text-gray-800 font-medium mb-2">老板您好！您登录的店铺不正确～</p>
                    <p class="text-gray-600">请您登录正确的店铺，否则无法使用小梅花AI功能</p>
                </div>
                
                <!-- 店铺信息 -->
                <div class="mb-6">
                    <!-- 正确店铺 -->
                    <div class="shop-info correct-shop">
                        <div class="shop-info-container">
                            <i class="fas fa-check-circle text-green-500 shop-icon"></i>
                            <div class="shop-text-container">
                                <span class="shop-label correct-label">正确的店铺:</span>
                                <span class="shop-name correct-name">xxxxxx</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 错误店铺 -->
                    <div class="shop-info wrong-shop">
                        <div class="shop-info-container">
                            <i class="fas fa-times-circle text-red-500 shop-icon"></i>
                            <div class="shop-text-container">
                                <span class="shop-label wrong-label">您登录的店铺:</span>
                                <span class="shop-name wrong-name">xxxxx</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <p class="warning-text text-center mb-6">系统会为您自动退出，请您重新扫码登录正确店铺</p>
                
                <!-- 确认按钮 -->
                <button class="confirm-btn text-white w-full py-3 rounded-lg font-medium">
                    确认
                </button>
            </div>
        </div>
    </div>

    <script>
        document.querySelector('.confirm-btn').addEventListener('click', function() {
            console.log('确认按钮被点击');
        });
    </script>
</body>
</html>