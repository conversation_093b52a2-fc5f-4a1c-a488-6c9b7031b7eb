<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小梅花AI智能客服安装程序</title>
    <script src="https://cdn.tailwindcss.com/3.3.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: #ffffff;
            color: #333333;
            height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .glass-panel {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
            width: 400px;
            height: 500px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%);
        }
        
        .btn-install {
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-install:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .btn-install:active {
            transform: translateY(0);
        }
        
        .progress-bar {
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
            position: relative;
            width: 80%;
            margin: 0 auto;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            border-radius: 3px;
            transition: width 0.4s ease;
        }
        
        .success-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981 0%, #06b6d4 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 15px;
            box-shadow: 0 4px 8px rgba(16, 185, 129, 0.2);
        }
        
        .panel {
            display: none;
            flex: 1;
            flex-direction: column;
            padding: 20px;
            justify-content: center;
        }
        
        .panel.active {
            display: flex;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .header {
            padding: 20px;
            text-align: center;
        }
        
        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 20px;
        }
        
        .footer {
            padding: 20px;
            text-align: center;
        }
        
        .step-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            color: #1f2937;
        }
        
        .step-description {
            color: #6b7280;
            text-align: center;
            margin-bottom: 25px;
            line-height: 1.5;
            font-size: 14px;
        }

        /* 标题栏样式 */
        .title-bar {
            height: 32px;
            background: #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 8px;
            border-bottom: 1px solid #d0d0d0;
            -webkit-app-region: drag;
        }

        .title-text {
            font-size: 12px;
            color: #333;
            margin-left: 8px;
        }

        .window-controls {
            display: flex;
            -webkit-app-region: no-drag;
        }

        .control-button {
            width: 46px;
            height: 32px;
            border: none;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
            color: #333;
        }

        .control-button:hover {
            background: #e5e5e5;
        }

        .control-button.close:hover {
            background: #e81123;
            color: white;
        }

        .main-container {
            height: calc(100vh - 32px);
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
</head>
<body>
    <!-- 标题栏 -->
    <div class="title-bar">
        <div class="title-text">小梅花AI智能客服安装程序</div>
        <div class="window-controls">
            <button class="control-button minimize" onclick="minimizeWindow()">
                <span>&#8212;</span>
            </button>
            <button class="control-button close" onclick="closeWindow()">
                <span>&#10005;</span>
            </button>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
        <div class="glass-panel">
        <!-- 安装准备面板 -->
        <div class="panel active" id="installPanel">
            <div class="header">
                <img src="./小梅花ai图标logo.png" alt="小梅花AI智能客服logo" class="mx-auto" style="width:80px;height:80px;">
            </div>
            
            <div class="content">
                <h2 class="step-title">安装准备</h2>
                <p class="step-description">请选择安装目录，开始安装小梅花AI智能客服系统</p>
                
                <div class="flex mt-3">
                    <input type="text" class="input-field flex-grow px-3 py-2 rounded-l-lg focus:outline-none border border-gray-300" value="C:\Program Files\小梅花AI客服">
                    <button class="bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded-r-lg text-gray-700 transition-colors">
                        <i class="fas fa-folder-open"></i>
                    </button>
                </div>
            </div>
            
            <div class="footer">
                <button id="installBtn" class="btn-install w-full py-3 rounded-lg text-white font-medium focus:outline-none">
                    开始安装
                </button>
            </div>
        </div>
        
        <!-- 安装进度面板 -->
        <div class="panel" id="progressPanel">
            <div class="header">
                <img src="./小梅花ai图标logo.png" alt="小梅花AI智能客服logo" class="mx-auto" style="width:80px;height:80px;">
            </div>
            
            <div class="content">
                <h2 class="step-title">正在安装...</h2>
                <p class="step-description">小梅花AI智能客服正在安装中，请稍候</p>
                
                <div class="progress-bar mb-4">
                    <div class="progress-fill w-0" id="progressFill"></div>
                </div>
                
                <div class="text-center text-xl font-medium text-gray-800" id="progressDetail">0%</div>
            </div>
        </div>
        
        <!-- 安装完成面板 -->
        <div class="panel" id="completePanel">
            <div class="header">
                <img src="./小梅花ai图标logo.png" alt="小梅花AI智能客服logo" class="mx-auto" style="width:80px;height:80px;">
            </div>
            
            <div class="content">
                <div class="success-icon">
                    <i class="fas fa-check text-white text-2xl"></i>
                </div>
                
                <h2 class="step-title">安装完成</h2>
                <p class="step-description">小梅花AI智能客服已成功安装</p>
            </div>
            
            <div class="footer">
                <button class="btn-install w-full py-3 rounded-lg text-white font-medium focus:outline-none">
                    立即启动
                </button>
            </div>
        </div>
    </div>
    </div>

    <script>
        // 窗口控制函数
        function minimizeWindow() {
            if (window.electronAPI) {
                window.electronAPI.minimizeWindow();
            }
        }

        function closeWindow() {
            if (window.electronAPI) {
                window.electronAPI.closeWindow();
            } else {
                window.close();
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            const installBtn = document.getElementById('installBtn');
            const progressFill = document.getElementById('progressFill');
            const progressDetail = document.getElementById('progressDetail');
            const panels = document.querySelectorAll('.panel');
            
            installBtn.addEventListener('click', function() {
                // 切换到进度面板
                switchPanel('progressPanel');
                
                // 模拟安装过程
                let progress = 0;
                const interval = setInterval(() => {
                    // 更新进度条
                    progress += Math.random() * 5;
                    if (progress > 100) progress = 100;
                    progressFill.style.width = `${progress}%`;
                    progressDetail.textContent = `${Math.floor(progress)}%`;
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            switchPanel('completePanel');
                        }, 800);
                    }
                }, 200);
            });
            
            function switchPanel(panelId) {
                panels.forEach(panel => {
                    panel.classList.remove('active');
                });
                document.getElementById(panelId).classList.add('active');
            }
        });
    </script>
</body>
</html>